Metadata-Version: 2.4
Name: wcag-contrast-ratio
Version: 0.9
Summary: A library for computing contrast ratios, as required by WCAG 2.0
Home-page: https://github.com/gsnedders/wcag-contrast-ratio
Maintainer: <PERSON>
Maintainer-email: <EMAIL>
License: MIT License
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 2
Classifier: Programming Language :: Python :: 2.6
Classifier: Programming Language :: Python :: 2.7
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.2
Classifier: Programming Language :: Python :: 3.3
Classifier: Programming Language :: Python :: 3.4
Classifier: Topic :: Software Development
Dynamic: classifier
Dynamic: description
Dynamic: home-page
Dynamic: license
Dynamic: maintainer
Dynamic: maintainer-email
Dynamic: summary

wcag-contrast-ratio
===================

A library for computing contrast ratios, as required by `WCAG 2.0`.

Usage
-----

Simple usage follows this pattern:

.. code-block:: python

  >> import wcag_contrast_ratio as contrast
  >> black = (0.0, 0.0, 0.0)
  >> white = (1.0, 1.0, 1.0)
  >> contrast.rgb(black, white)
  21.0

Two useful helper functions are provided, to check if contrast meets
the required level:

.. code-block:: python

  >> import wcag_contrast_ratio as contrast
  >> contrast.passes_AA(21.0)
  True
  >> contrast.passes_AAA(21.0)
  True

.. _WCAG 2.0: http://www.w3.org/TR/WCAG20/
