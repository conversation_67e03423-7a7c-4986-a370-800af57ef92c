# This file is part of CycloneDX Python Library
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
# SPDX-License-Identifier: Apache-2.0
# Copyright (c) OWASP Foundation. All Rights Reserved.


"""
Exceptions that are for specific error scenarios during the output of a Model to a SBOM.
"""

from . import CycloneDxException


class BomGenerationErrorException(CycloneDxException):
    """
    Raised if there is an unknown error.
    """
    pass


class FormatNotSupportedException(CycloneDxException):
    """
    Exception raised when attempting to output a BOM to a format not supported in the requested version.

    For example, JSON is not supported prior to 1.2.
    """
    pass
