<?xml version="1.0" encoding="UTF-8"?>
<!--
CycloneDX Bill of Material (BOM) Specification

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
-->
<xs:schema xmlns:xs="http://www.w3.org/2001/XMLSchema"
           xmlns:vc="http://www.w3.org/2007/XMLSchema-versioning"
           xmlns:bom="http://cyclonedx.org/schema/bom/1.6"
           xmlns:spdx="http://cyclonedx.org/schema/spdx"
           elementFormDefault="qualified"
           targetNamespace="http://cyclonedx.org/schema/bom/1.6"
           vc:minVersion="1.0"
           vc:maxVersion="1.1"
           version="1.6.1">

    <xs:import namespace="http://cyclonedx.org/schema/spdx" schemaLocation="spdx.SNAPSHOT.xsd"/>

    <xs:annotation>
        <xs:documentation>
            <name>CycloneDX Bill of Materials Standard</name>
            <url>https://cyclonedx.org/</url>
            <license uri="http://www.apache.org/licenses/LICENSE-2.0"
                     version="2.0">Apache License, Version 2.0</license>
        </xs:documentation>
    </xs:annotation>

    <xs:simpleType name="refType">
        <xs:annotation>
            <xs:documentation>Identifier for referable and therefore interlink-able elements.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:minLength value="1"/>
            <!-- value SHOULD not start with the BOM-Link intro "urn:cdx:" -->
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="refLinkType">
        <xs:annotation>
            <xs:documentation xml:lang="en">
                Descriptor for an element identified by the attribute "bom-ref" in the same BOM document.
                In contrast to `bomLinkElementType`.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="bom:refType"/>
    </xs:simpleType>

    <xs:simpleType name="versionType">
        <xs:annotation>
            <xs:documentation xml:lang="en"><![CDATA[
                A single disjunctive version identifier, for a component or service.

                Example values:
                - "9.0.14"
                - "v1.33.7"
                - "7.0.0-M1"
                - "2.0pre1"
                - "1.0.0-beta1"
                - "0.8.15"
            ]]>
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:normalizedString">
            <xs:maxLength value="1024"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="versionRangeType">
        <xs:annotation>
            <xs:documentation xml:lang="en"><![CDATA[
                A version range specified in Package URL Version Range syntax (vers) which is defined at https://github.com/package-url/purl-spec/VERSION-RANGE-SPEC.rst

                Example values:
                - "vers:cargo/9.0.14"
                - "vers:npm/1.2.3|>=2.0.0|<5.0.0"
                - "vers:pypi/0.0.0|0.0.1|0.0.2|0.0.3|1.0|2.0pre1"
                - "vers:tomee/>=1.0.0-beta1|<=1.7.5|>=7.0.0-M1|<=7.0.7|>=7.1.0|<=7.1.2|>=8.0.0-M1|<=8.0.1"
                - "vers:gem/>=2.2.0|!= 2.2.1|<2.3.0"
            ]]>
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:normalizedString">
            <xs:minLength value="1"/>
            <xs:maxLength value="4096"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="bomLinkDocumentType">
        <xs:annotation>
            <xs:documentation xml:lang="en">
                Descriptor for another BOM document.
                See https://cyclonedx.org/capabilities/bomlink/
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:anyURI">
            <!-- part of the pattern is based on `bom.serialNumber`'s pattern -->
            <xs:pattern value="urn:cdx:[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/[1-9][0-9]*"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="bomLinkElementType">
        <xs:annotation>
            <xs:documentation  xml:lang="en">
                Descriptor for an element in another BOM document.
                See https://cyclonedx.org/capabilities/bomlink/
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:anyURI">
            <!-- part of the pattern is based on `bom.serialNumber`'s pattern -->
            <xs:pattern value="urn:cdx:[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}/[1-9][0-9]*#.+"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="bomLinkType">
        <xs:union memberTypes="bom:bomLinkDocumentType bom:bomLinkElementType"/>
    </xs:simpleType>

    <xs:complexType name="metadata">
        <xs:sequence minOccurs="0" maxOccurs="1">
            <xs:element name="timestamp" type="xs:dateTime" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>The date and time (timestamp) when the BOM was created.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="lifecycles" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Lifecycles communicate the stage(s) in which data in the BOM was captured. Different types of data may be available at various phases of a lifecycle, such as the Software Development Lifecycle (SDLC), IT Asset Management (ITAM), and Software Asset Management (SAM). Thus, a BOM may include data specific to or only obtainable in a given lifecycle.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="lifecycle" minOccurs="0" maxOccurs="unbounded">
                            <xs:complexType>
                                <xs:choice>
                                    <xs:sequence>
                                        <xs:element name="phase" type="bom:lifecyclePhaseType" minOccurs="1" maxOccurs="1">
                                            <xs:annotation>
                                                <xs:documentation>
                                                    A pre-defined phase in the product lifecycle.
                                                </xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                    <xs:sequence>
                                        <xs:element name="name" type="xs:normalizedString" minOccurs="1" maxOccurs="1">
                                            <xs:annotation>
                                                <xs:documentation>
                                                    The name of the lifecycle phase
                                                </xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="description" type="xs:string" minOccurs="0" maxOccurs="1">
                                            <xs:annotation>
                                                <xs:documentation>
                                                    The description of the lifecycle phase
                                                </xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:sequence>
                                </xs:choice>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="tools" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The tool(s) used in the creation of the BOM.</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:choice>
                        <xs:sequence minOccurs="0" maxOccurs="unbounded">
                            <xs:element name="tool" minOccurs="0" type="bom:toolType">
                                <xs:annotation>
                                    <xs:documentation>DEPRECATED. Use tools\components or tools\services instead.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                        <xs:sequence minOccurs="0" maxOccurs="1">
                            <xs:element name="components" type="bom:componentsType" minOccurs="0" maxOccurs="1">
                                <xs:annotation>
                                    <xs:documentation>A list of software and hardware components used as tools.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="services" type="bom:servicesType" minOccurs="0" maxOccurs="1">
                                <xs:annotation>
                                    <xs:documentation>A list of services used as tools.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:choice>
                </xs:complexType>
            </xs:element>
            <xs:element name="authors" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The person(s) who created the BOM.
                        Authors are common in BOMs created through manual processes. BOMs created through automated means may have './manufacturer' instead.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence minOccurs="0" maxOccurs="unbounded">
                        <xs:element name="author" type="bom:organizationalContact"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="component" type="bom:component" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>The component that the BOM describes.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="manufacturer" type="bom:organizationalEntity" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The organization that created the BOM.
                        Manufacturer is common in BOMs created through automated processes. BOMs created through manual means may have './authors' instead.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="manufacture" type="bom:organizationalEntity" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        DEPRECATED - DO NOT USE. This will be removed in a future version. Use the `./component/manufacturer` instead.
                        The organization that manufactured the component that the BOM describes.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="supplier" type="bom:organizationalEntity" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The organization that supplied the component that the BOM describes. The
                        supplier may often be the manufacturer, but may also be a distributor or repackager.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="licenses" type="bom:licenseChoiceType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The license information for the BOM document.
                        This may be different from the license(s) of the component(s) that the BOM describes.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="properties" type="bom:propertiesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Provides the ability to document properties in a name/value store.
                        This provides flexibility to include data not officially supported in the standard
                        without having to use additional namespaces or create extensions. Property names
                        of interest to the general public are encouraged to be registered in the
                        CycloneDX Property Taxonomy - https://github.com/CycloneDX/cyclonedx-property-taxonomy.
                        Formal registration is optional.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:anyAttribute namespace="##other" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:simpleType name="lifecyclePhaseType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="design">
                <xs:annotation>
                    <xs:documentation>
                        BOM produced early in the development lifecycle containing inventory of components and services
                        that are proposed or planned to be used. The inventory may need to be procured, retrieved,
                        or resourced prior to use.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="pre-build">
                <xs:annotation>
                    <xs:documentation>
                        BOM consisting of information obtained prior to a build process and may contain source files
                        and development artifacts and manifests. The inventory may need to be resolved and retrieved
                        prior to use.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="build">
                <xs:annotation>
                    <xs:documentation>
                        BOM consisting of information obtained during a build process where component inventory is
                        available for use. The precise versions of resolved components are usually available at this
                        time as well as the provenance of where the components were retrieved from.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="post-build">
                <xs:annotation>
                    <xs:documentation>
                        BOM consisting of information obtained after a build process has completed and the resulting
                        components(s) are available for further analysis. Built components may exist as the result of a
                        CI/CD process, may have been installed or deployed to a system or device, and may need to be
                        retrieved or extracted from the system or device.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="operations">
                <xs:annotation>
                    <xs:documentation>
                        BOM produced that represents inventory that is running and operational. This may include staging
                        or production environments and will generally encompass multiple SBOMs describing the applications
                        and operating system, along with HBOMs describing the hardware that makes up the system. Operations
                        Bill of Materials (OBOM) can provide full-stack inventory of runtime environments, configurations,
                        and additional dependencies.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="discovery">
                <xs:annotation>
                    <xs:documentation>
                        BOM consisting of information observed through network discovery providing point-in-time
                        enumeration of embedded, on-premise, and cloud-native services such as server applications,
                        connected devices, microservices, and serverless functions.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="decommission">
                <xs:annotation>
                    <xs:documentation>
                        BOM containing inventory that will be, or has been retired from operations.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="organizationalEntity">
        <xs:sequence minOccurs="0" maxOccurs="1">
            <xs:element name="name" type="xs:normalizedString" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The name of the organization</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="address" type="bom:postalAddressType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The physical address (location) of the organization.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="url" type="xs:anyURI" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        The URL of the organization. Multiple URLs are allowed.
                        Example: https://example.com
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="contact" type="bom:organizationalContact" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>A contact person at the organization. Multiple contacts are allowed.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:attribute name="bom-ref" type="bom:refType">
            <xs:annotation>
                <xs:documentation>
                    An optional identifier which can be used to reference the object elsewhere in the BOM.
                    Uniqueness is enforced within all elements and children of the root-level bom element.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:anyAttribute namespace="##other" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="toolType">
        <xs:annotation>
            <xs:documentation>Information about the automated or manual tool used</xs:documentation>
        </xs:annotation>
        <xs:sequence minOccurs="0" maxOccurs="1">
            <xs:element name="vendor" minOccurs="0" maxOccurs="1" type="xs:normalizedString">
                <xs:annotation>
                    <xs:documentation>The name of the vendor who created the tool</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="name" minOccurs="0" maxOccurs="1" type="xs:normalizedString">
                <xs:annotation>
                    <xs:documentation>The name of the tool</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="version" minOccurs="0" maxOccurs="1" type="bom:versionType">
                <xs:annotation>
                    <xs:documentation>The version of the tool</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="hashes" minOccurs="0" maxOccurs="1">
                <xs:complexType>
                    <xs:sequence minOccurs="0" maxOccurs="unbounded">
                        <xs:element name="hash" type="bom:hashType"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="externalReferences" type="bom:externalReferences" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Provides the ability to document external references related to the tool.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:anyAttribute namespace="##other" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="organizationalContact">
        <xs:sequence minOccurs="0" maxOccurs="1">
            <xs:element name="name" type="xs:normalizedString" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The name of the contact</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="email" type="xs:normalizedString" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The email address of the contact.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="phone" type="xs:normalizedString" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The phone number of the contact.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:attribute name="bom-ref" type="bom:refType">
            <xs:annotation>
                <xs:documentation>
                    An optional identifier which can be used to reference the object elsewhere in the BOM.
                    Uniqueness is enforced within all elements and children of the root-level bom element.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:anyAttribute namespace="##other" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="componentsType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
            <xs:element name="component" type="bom:component"/>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="component">
        <xs:sequence>
            <xs:element name="supplier" type="bom:organizationalEntity" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The organization that supplied the component. The supplier may often
                        be the manufacturer, but may also be a distributor or repackager.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="manufacturer" type="bom:organizationalEntity" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The organization that created the component.
                        Manufacturer is common in components created through automated processes. Components created through manual means may have './authors' instead.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="authors" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The person(s) who created the component.
                        Authors are common in components created through manual processes. Components created through automated means may have `./manufacturer` instead.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence minOccurs="0" maxOccurs="unbounded">
                        <xs:element name="author" type="bom:organizationalContact"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="author" type="xs:normalizedString" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        DEPRECATED - DO NOT USE. This will be removed in a future version. Use `./authors` or `./manufacturer` instead.
                        The person(s) or organization(s) that authored the component.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="publisher" type="xs:normalizedString" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The person(s) or organization(s) that published the component</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="group" type="xs:normalizedString" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The grouping name or identifier. This will often be a shortened, single
                        name of the company or project that produced the component, or the source package or
                        domain name. Whitespace and special characters should be avoided. Examples include:
                        apache, org.apache.commons, and apache.org.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="name" type="xs:normalizedString" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The name of the component. This will often be a shortened, single name
                        of the component. Examples: commons-lang3 and jquery</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="version" type="bom:versionType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The component version. The version should ideally comply with semantic versioning
                        but is not enforced.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="description" type="xs:normalizedString" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Specifies a description for the component</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="scope" type="bom:scope" minOccurs="0" maxOccurs="1" default="required">
                <xs:annotation>
                    <xs:documentation>Specifies the scope of the component. If scope is not specified, 'required'
                        scope SHOULD be assumed by the consumer of the BOM.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="hashes" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The hashes of the component.</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence minOccurs="0" maxOccurs="unbounded">
                        <xs:element name="hash" type="bom:hashType"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="licenses" type="bom:licenseChoiceType" minOccurs="0" maxOccurs="1"/>
            <xs:element name="copyright" type="xs:normalizedString" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>A copyright notice informing users of the underlying claims to copyright ownership in a published work.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cpe" type="bom:cpe" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Specifies a well-formed CPE name that conforms to the CPE 2.2 or 2.3 specification. See https://nvd.nist.gov/products/cpe
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="purl" type="xs:anyURI" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Specifies the package-url (purl). The purl, if specified, must be valid and conform
                        to the specification defined at: https://github.com/package-url/purl-spec
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="omniborId" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Specifies the OmniBOR Artifact ID. The OmniBOR, if specified, must be valid and conform
                        to the specification defined at: https://www.iana.org/assignments/uri-schemes/prov/gitoid
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="swhid" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Specifies the Software Heritage persistent identifier (SWHID). The SWHID, if specified, must
                        be valid and conform to the specification defined at:
                        https://docs.softwareheritage.org/devel/swh-model/persistent-identifiers.html
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="swid" type="bom:swidType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Specifies metadata and content for ISO-IEC 19770-2 Software Identification (SWID) Tags.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="modified" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        DEPRECATED - DO NOT USE. This will be removed in a future version. Use the pedigree
                        element instead to supply information on exactly how the component was modified.
                        A boolean value indicating if the component has been modified from the original.
                        A value of true indicates the component is a derivative of the original.
                        A value of false indicates the component has not been modified from the original.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="pedigree" type="bom:pedigreeType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Component pedigree is a way to document complex supply chain scenarios where components are
                        created, distributed, modified, redistributed, combined with other components, etc.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="externalReferences" type="bom:externalReferences" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Provides the ability to document external references related to the
                        component or to the project the component describes.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="properties" type="bom:propertiesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Provides the ability to document properties in a name/value store.
                        This provides flexibility to include data not officially supported in the standard
                        without having to use additional namespaces or create extensions. Property names
                        of interest to the general public are encouraged to be registered in the
                        CycloneDX Property Taxonomy - https://github.com/CycloneDX/cyclonedx-property-taxonomy.
                        Formal registration is optional.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="components" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        A list of software and hardware components included in the parent component. This is not a
                        dependency tree. It provides a way to specify a hierarchical representation of component
                        assemblies, similar to system -> subsystem -> parts assembly in physical supply chains.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence minOccurs="0" maxOccurs="unbounded">
                        <xs:element name="component" type="bom:component"/>
                        <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>
                                    Allows any undeclared elements as long as the elements are placed in a different namespace.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:any>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="evidence" type="bom:componentEvidenceType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Provides the ability to document evidence collected through various forms of extraction or analysis.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="releaseNotes" type="bom:releaseNotesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Specifies optional release notes.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="modelCard" type="bom:modelCardType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>A model card describes the intended uses of a machine learning model and potential
                        limitations, including biases and ethical considerations. Model cards typically contain the
                        training parameters, which datasets were used to train the model, performance metrics, and other
                        relevant data useful for ML transparency. This object SHOULD be specified for any component of
                        type `machine-learning-model` and must not be specified for other component types.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="data" type="bom:componentDataType" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>This object SHOULD be specified for any component of type `data` and must not be
                        specified for other component types.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="cryptoProperties" type="bom:cryptoPropertiesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Cryptographic assets have properties that uniquely define them and that make them actionable
                        for further reasoning. As an example, it makes a difference if one knows the algorithm family
                        (e.g. AES) or the specific variant or instantiation (e.g. AES-128-GCM). This is because the
                        security level and the algorithm primitive (authenticated encryption) is only defined by the
                        definition of the algorithm variant. The presence of a weak cryptographic algorithm like SHA1
                        vs. HMAC-SHA1 also makes a difference.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="tags" type="bom:tagsType" minOccurs="0" maxOccurs="1" />
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:attribute name="type" type="bom:classification" use="required">
            <xs:annotation>
                <xs:documentation>
                    Specifies the type of component. For software components, classify as application if no more
                    specific appropriate classification is available or cannot be determined for the component.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="mime-type" type="bom:mimeType">
            <xs:annotation>
                <xs:documentation>
                    The optional mime-type of the component. When used on file components, the mime-type
                    can provide additional context about the kind of file being represented such as an image,
                    font, or executable. Some library or framework components may also have an associated mime-type.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="bom-ref" type="bom:refType">
            <xs:annotation>
                <xs:documentation>
                    An optional identifier which can be used to reference the component elsewhere in the BOM.
                    Uniqueness is enforced within all elements and children of the root-level bom element.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="licenseType">
        <xs:annotation>
            <xs:documentation>Specifies the details and attributes related to a software license.
                It can either include a valid SPDX license identifier or a named license, along with additional
                properties such as license acknowledgment, comprehensive commercial licensing information, and
                the full text of the license.</xs:documentation>
            </xs:annotation>
        <xs:sequence>
            <xs:choice>
                <xs:element name="id" type="spdx:licenseId" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>A valid SPDX license identifier. If specified, this value must be one of the enumeration of valid SPDX license identifiers defined in the spdx.schema.json (or spdx.xml) subschema which is synchronized with the official SPDX license list.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="name" type="xs:normalizedString" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>The name of the license. This may include the name of a commercial or proprietary license or an open source license that may not be defined by SPDX.</xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:choice>
            <xs:element name="text" type="bom:attachedTextType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Specifies the optional full text of the attachment</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="url" type="xs:anyURI" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The URL to the attachment file. If the attachment is a license or BOM,
                        an externalReference should also be specified for completeness.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="licensing" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Licensing details describing the licensor/licensee, license type, renewal and
                        expiration dates, and other important metadata</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="altIds" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>License identifiers that may be used to manage licenses and
                                    their lifecycle</xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="altId" type="xs:normalizedString" minOccurs="0" maxOccurs="unbounded"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="licensor" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>The individual or organization that grants a license to another
                                    individual or organization</xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:choice>
                                        <xs:element name="organization" type="bom:organizationalEntity" minOccurs="0" maxOccurs="1">
                                            <xs:annotation>
                                                <xs:documentation>The organization that granted the license</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="individual" type="bom:organizationalContact" minOccurs="0" maxOccurs="1">
                                            <xs:annotation>
                                                <xs:documentation>The individual, not associated with an organization,
                                                    that granted the license</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:choice>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="licensee" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>The individual or organization for which a license was granted to</xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:choice>
                                        <xs:element name="organization" type="bom:organizationalEntity" minOccurs="0" maxOccurs="1">
                                            <xs:annotation>
                                                <xs:documentation>The organization that was granted the license</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="individual" type="bom:organizationalContact" minOccurs="0" maxOccurs="1">
                                            <xs:annotation>
                                                <xs:documentation>The individual, not associated with an organization,
                                                    that was granted the license</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:choice>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="purchaser" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>The individual or organization that purchased the license</xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:choice>
                                        <xs:element name="organization" type="bom:organizationalEntity" minOccurs="0" maxOccurs="1">
                                            <xs:annotation>
                                                <xs:documentation>The organization that purchased the license</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                        <xs:element name="individual" type="bom:organizationalContact" minOccurs="0" maxOccurs="1">
                                            <xs:annotation>
                                                <xs:documentation>The individual, not associated with an organization,
                                                    that purchased the license</xs:documentation>
                                            </xs:annotation>
                                        </xs:element>
                                    </xs:choice>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="purchaseOrder" type="xs:string" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>The purchase order identifier the purchaser sent to a supplier or
                                    vendor to authorize a purchase</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="licenseTypes" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>The type of license(s) that was granted to the licensee</xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="licenseType" type="bom:licenseTypeEnum" minOccurs="0" maxOccurs="unbounded"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="lastRenewal" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation xml:lang="en">The timestamp indicating when the license was last
                                    renewed. For new purchases, this is often the purchase or acquisition date.
                                    For non-perpetual licenses or subscriptions, this is the timestamp of when the
                                    license was last renewed.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="expiration" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation xml:lang="en">The timestamp indicating when the current license
                                    expires (if applicable).</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>
                                    Allows any undeclared elements as long as the elements are placed in a different namespace.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:any>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="properties" type="bom:propertiesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Provides the ability to document properties in a name/value store.
                        This provides flexibility to include data not officially supported in the standard
                        without having to use additional namespaces or create extensions. Property names
                        of interest to the general public are encouraged to be registered in the
                        CycloneDX Property Taxonomy - https://github.com/CycloneDX/cyclonedx-property-taxonomy.
                        Formal registration is optional.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:attribute name="bom-ref" type="bom:refType">
            <xs:annotation>
                <xs:documentation>
                    An optional identifier which can be used to reference the license elsewhere in the BOM.
                    Uniqueness is enforced within all elements and children of the root-level bom element.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="acknowledgement" type="bom:licenseAcknowledgementEnumerationType">
            <xs:annotation>
                <xs:documentation>
                    Declared licenses and concluded licenses represent two different stages in the
                    licensing process within software development. Declared licenses refer to the
                    initial intention of the software authors regarding the licensing terms under
                    which their code is released. On the other hand, concluded licenses are the
                    result of a comprehensive analysis of the project's codebase to identify and
                    confirm the actual licenses of the components used, which may differ from the
                    initially declared licenses. While declared licenses provide an upfront indication
                    of the licensing intentions, concluded licenses offer a more thorough understanding
                    of the actual licensing within a project, facilitating proper compliance and risk
                    management. Observed licenses are defined in `evidence.licenses`. Observed licenses
                    form the evidence necessary to substantiate a concluded license.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="attachedTextType">
        <xs:simpleContent>
            <xs:extension base="xs:string">
                <xs:annotation>
                    <xs:documentation>The attachment data. Proactive controls such as input validation and sanitization should be employed to prevent misuse of attachment text.</xs:documentation>
                </xs:annotation>
                <xs:attribute name="content-type" type="xs:normalizedString" default="text/plain">
                    <xs:annotation>
                        <xs:documentation>
                            Specifies the format and nature of the data being attached, helping systems correctly
                            interpret and process the content. Common content type examples include `application/json`
                            for JSON data and `text/plain` for plan text documents.
                            RFC 2045 section 5.1 outlines the structure and use of content types. For a comprehensive
                            list of registered content types, refer to the IANA media types registry at
                            https://www.iana.org/assignments/media-types/media-types.xhtml.
                        </xs:documentation>
                    </xs:annotation>
                </xs:attribute>
                <xs:attribute name="encoding" type="bom:encoding">
                    <xs:annotation>
                        <xs:documentation>
                            Specifies the optional encoding the text is represented in
                        </xs:documentation>
                    </xs:annotation>
                </xs:attribute>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>

    <xs:complexType name="hashType">
        <xs:annotation>
            <xs:documentation>Specifies the file hash of the component</xs:documentation>
        </xs:annotation>
        <xs:simpleContent>
            <xs:extension base="bom:hashValue">
                <xs:attribute name="alg" type="bom:hashAlg" use="required">
                    <xs:annotation>
                        <xs:documentation>Specifies the algorithm used to create the hash</xs:documentation>
                    </xs:annotation>
                </xs:attribute>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>

    <xs:simpleType name="scope">
        <xs:restriction base="xs:string">
            <xs:enumeration value="required">
                <xs:annotation>
                    <xs:documentation>The component is required for runtime</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="optional">
                <xs:annotation>
                    <xs:documentation>The component is optional at runtime. Optional components are components that
                        are not capable of being called due to them not be installed or otherwise accessible by any means.
                        Components that are installed but due to configuration or other restrictions are prohibited from
                        being called must be scoped as 'required'.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="excluded">
                <xs:annotation>
                    <xs:documentation>Components that are excluded provide the ability to document component usage
                        for test and other non-runtime purposes. Excluded components are not reachable within a call
                        graph at runtime.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="classification">
        <xs:restriction base="xs:string">
            <xs:enumeration value="application">
                <xs:annotation>
                    <xs:documentation>A software application. Refer to https://en.wikipedia.org/wiki/Application_software
                        for information about applications.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="framework">
                <xs:annotation>
                    <xs:documentation>A software framework. Refer to https://en.wikipedia.org/wiki/Software_framework
                        for information on how frameworks vary slightly from libraries.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="library">
                <xs:annotation>
                    <xs:documentation>A software library. Refer to https://en.wikipedia.org/wiki/Library_(computing)
                        for information about libraries. All third-party and open source reusable components will likely
                        be a library. If the library also has key features of a framework, then it should be classified
                        as a framework. If not, or is unknown, then specifying library is recommended.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="container">
                <xs:annotation>
                    <xs:documentation>A packaging and/or runtime format, not specific to any particular technology,
                        which isolates software inside the container from software outside of a container through
                        virtualization technology. Refer to https://en.wikipedia.org/wiki/OS-level_virtualization</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="platform">
                <xs:annotation>
                    <xs:documentation>A runtime environment which interprets or executes software. This may include
                        runtimes such as those that execute bytecode or low-code/no-code application platforms.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="operating-system">
                <xs:annotation>
                    <xs:documentation>A software operating system without regard to deployment model
                        (i.e. installed on physical hardware, virtual machine, image, etc) Refer to
                        https://en.wikipedia.org/wiki/Operating_system</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="device">
                <xs:annotation>
                    <xs:documentation>A hardware device such as a processor, or chip-set. A hardware device
                        containing firmware SHOULD include a component for the physical hardware itself, and another
                        component of type 'firmware' or 'operating-system' (whichever is relevant), describing
                        information about the software running on the device.
                        See also the list of known device properties: https://github.com/CycloneDX/cyclonedx-property-taxonomy/blob/main/cdx/device.md
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="device-driver">
                <xs:annotation>
                    <xs:documentation>A special type of software that operates or controls a particular type of device.
                        Refer to https://en.wikipedia.org/wiki/Device_driver</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="firmware">
                <xs:annotation>
                    <xs:documentation>A special type of software that provides low-level control over a devices
                        hardware. Refer to https://en.wikipedia.org/wiki/Firmware</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="file">
                <xs:annotation>
                    <xs:documentation>A computer file. Refer to https://en.wikipedia.org/wiki/Computer_file
                        for information about files.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="machine-learning-model">
                <xs:annotation>
                    <xs:documentation>A model based on training data that can make predictions or decisions without
                        being explicitly programmed to do so.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="data">
                <xs:annotation>
                    <xs:documentation>A collection of discrete values that convey information.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="cryptographic-asset">
                <xs:annotation>
                    <xs:documentation>A cryptographic asset including algorithms, protocols, certificates, keys, tokens, and secrets.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="hashAlg">
        <xs:restriction base="xs:string">
            <xs:enumeration value="MD5"/>
            <xs:enumeration value="SHA-1"/>
            <xs:enumeration value="SHA-256"/>
            <xs:enumeration value="SHA-384"/>
            <xs:enumeration value="SHA-512"/>
            <xs:enumeration value="SHA3-256"/>
            <xs:enumeration value="SHA3-384"/>
            <xs:enumeration value="SHA3-512"/>
            <xs:enumeration value="BLAKE2b-256"/>
            <xs:enumeration value="BLAKE2b-384"/>
            <xs:enumeration value="BLAKE2b-512"/>
            <xs:enumeration value="BLAKE3"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="licenseTypeEnum">
        <xs:restriction base="xs:string">
            <xs:enumeration value="academic">
                <xs:annotation>
                    <xs:documentation>A license that grants use of software solely for the purpose
                        of education or research.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="appliance">
                <xs:annotation>
                    <xs:documentation>A license covering use of software embedded in a specific
                        piece of hardware.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="client-access">
                <xs:annotation>
                    <xs:documentation>A Client Access License (CAL) allows client computers to access
                        services provided by server software.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="concurrent-user">
                <xs:annotation>
                    <xs:documentation>A Concurrent User license (aka floating license) limits the
                        number of licenses for a software application and licenses are shared among
                        a larger number of users.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="core-points">
                <xs:annotation>
                    <xs:documentation>A license where the core of a computer's processor is assigned
                        a specific number of points.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="custom-metric">
                <xs:annotation>
                    <xs:documentation>A license for which consumption is measured by non-standard
                        metrics.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="device">
                <xs:annotation>
                    <xs:documentation>A license that covers a defined number of installations on
                        computers and other types of devices.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="evaluation">
                <xs:annotation>
                    <xs:documentation>A license that grants permission to install and use software
                        for trial purposes.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="named-user">
                <xs:annotation>
                    <xs:documentation>A license that grants access to the software to one or more
                        pre-defined users.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="node-locked">
                <xs:annotation>
                    <xs:documentation>A license that grants access to the software on one or more
                        pre-defined computers or devices.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="oem">
                <xs:annotation>
                    <xs:documentation>An Original Equipment Manufacturer license that is delivered
                        with hardware, cannot be transferred to other hardware, and is valid for the
                        life of the hardware.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="perpetual">
                <xs:annotation>
                    <xs:documentation>A license where the software is sold on a one-time basis and
                        the licensee can use a copy of the software indefinitely.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="processor-points">
                <xs:annotation>
                    <xs:documentation>A license where each installation consumes points per
                        processor.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="subscription">
                <xs:annotation>
                    <xs:documentation>A license where the licensee pays a fee to use the software
                        or service.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="user">
                <xs:annotation>
                    <xs:documentation>A license that grants access to the software or service by a
                        specified number of users.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="other">
                <xs:annotation>
                    <xs:documentation>Another license type.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="hashValue">
        <xs:restriction base="xs:token">
            <xs:pattern value="([a-fA-F0-9]{32})|([a-fA-F0-9]{40})|([a-fA-F0-9]{64})|([a-fA-F0-9]{96})|([a-fA-F0-9]{128})"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="mimeType">
        <xs:restriction base="xs:token">
            <xs:pattern value="[-+a-z0-9.]+/[-+a-z0-9.]+"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="encoding">
        <xs:restriction base="xs:string">
            <xs:enumeration value="base64"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="cpe">
        <xs:annotation>
            <xs:documentation xml:lang="en">
                Define the format for acceptable CPE URIs. Supports CPE 2.2 and CPE 2.3 formats.
                Refer to https://nvd.nist.gov/products/cpe for official specification.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="([c][pP][eE]:/[AHOaho]?(:[A-Za-z0-9\._\-~%]*){0,6})|(cpe:2\.3:[aho\*\-](:(((\?*|\*?)([a-zA-Z0-9\-\._]|(\\[\\\*\?!&quot;#$$%&amp;'\(\)\+,/:;&lt;=&gt;@\[\]\^`\{\|}~]))+(\?*|\*?))|[\*\-])){5}(:(([a-zA-Z]{2,3}(-([a-zA-Z]{2}|[0-9]{3}))?)|[\*\-]))(:(((\?*|\*?)([a-zA-Z0-9\-\._]|(\\[\\\*\?!&quot;#$$%&amp;'\(\)\+,/:;&lt;=&gt;@\[\]\^`\{\|}~]))+(\?*|\*?))|[\*\-])){4})"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="swidType">
        <xs:sequence>
            <xs:element name="text" type="bom:attachedTextType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Specifies the full content of the SWID tag.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="url" type="xs:anyURI" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The URL to the SWID file.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:attribute name="tagId" type="xs:string" use="required">
            <xs:annotation>
                <xs:documentation>Maps to the tagId of a SoftwareIdentity.</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="name" type="xs:string" use="required">
            <xs:annotation>
                <xs:documentation>Maps to the name of a SoftwareIdentity.</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="version" type="xs:string" use="optional" default="0.0">
            <xs:annotation>
                <xs:documentation>Maps to the version of a SoftwareIdentity.</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="tagVersion" type="xs:integer" use="optional" default="0">
            <xs:annotation>
                <xs:documentation>Maps to the tagVersion of a SoftwareIdentity.</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:attribute name="patch" type="xs:boolean" use="optional" default="false">
            <xs:annotation>
                <xs:documentation>Maps to the patch of a SoftwareIdentity.</xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:simpleType name="urnUuid">
        <xs:annotation>
            <xs:documentation xml:lang="en">
                Defines a string representation of a UUID conforming to RFC 4122.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="urn:uuid:([0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12})|(\{[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\})"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="externalReferenceType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="vcs">
                <xs:annotation>
                    <xs:documentation>Version Control System</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="issue-tracker">
                <xs:annotation>
                    <xs:documentation>Issue or defect tracking system, or an Application Lifecycle Management (ALM) system</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="website">
                <xs:annotation>
                    <xs:documentation>Website</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="advisories">
                <xs:annotation>
                    <xs:documentation>Security advisories</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="bom">
                <xs:annotation>
                    <xs:documentation>Bill-of-materials (SBOM, OBOM, HBOM, SaaSBOM, etc)</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="mailing-list">
                <xs:annotation>
                    <xs:documentation>Mailing list or discussion group</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="social">
                <xs:annotation>
                    <xs:documentation>Social media account</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="chat">
                <xs:annotation>
                    <xs:documentation>Real-time chat platform</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="documentation">
                <xs:annotation>
                    <xs:documentation>Documentation, guides, or how-to instructions</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="support">
                <xs:annotation>
                    <xs:documentation>Community or commercial support</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="source-distribution">
                <xs:annotation>
                    <xs:documentation>The location where the source code distributable can be obtained. This is often an archive format such as zip or tgz. The source-distribution type complements use of the version control (vcs) type.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="distribution">
                <xs:annotation>
                    <xs:documentation>Direct or repository download location</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="distribution-intake">
                <xs:annotation>
                    <xs:documentation>The location where a component was published to. This is often the same as "distribution" but may also include specialized publishing processes that act as an intermediary</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="license">
                <xs:annotation>
                    <xs:documentation>
                        The URL to the license file. If a license URL has been defined in the license
                        node, it should also be defined as an external reference for completeness.
                        Example: https://www.apache.org/licenses/LICENSE-2.0.txt
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="build-meta">
                <xs:annotation>
                    <xs:documentation>Build-system specific meta file (i.e. pom.xml, package.json, .nuspec, etc)</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="build-system">
                <xs:annotation>
                    <xs:documentation>URL to an automated build system</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="release-notes">
                <xs:annotation>
                    <xs:documentation>URL to release notes</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="security-contact">
                <xs:annotation>
                    <xs:documentation>Specifies a way to contact the maintainer, supplier, or provider in the event of a security incident. Common URIs include links to a disclosure procedure, a mailto (RFC-2368) that specifies an email address, a tel (RFC-3966) that specifies a phone number, or dns (RFC-4501) that specifies the records containing DNS Security TXT.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="model-card">
                <xs:annotation>
                    <xs:documentation>A model card describes the intended uses of a machine learning model, potential
                        limitations, biases, ethical considerations, training parameters, datasets used to train the
                        model, performance metrics, and other relevant data useful for ML transparency.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="log">
                <xs:annotation>
                    <xs:documentation>A record of events that occurred in a computer system or application, such as problems, errors, or information on current operations.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="configuration">
                <xs:annotation>
                    <xs:documentation>Parameters or settings that may be used by other components or services.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="evidence">
                <xs:annotation>
                    <xs:documentation>Information used to substantiate a claim.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="formulation">
                <xs:annotation>
                    <xs:documentation>Describes how a component or service was manufactured or deployed.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="attestation">
                <xs:annotation>
                    <xs:documentation>Human or machine-readable statements containing facts, evidence, or testimony</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="threat-model">
                <xs:annotation>
                    <xs:documentation>An enumeration of identified weaknesses, threats, and countermeasures, dataflow diagram (DFD), attack tree, and other supporting documentation in human-readable or machine-readable format</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="adversary-model">
                <xs:annotation>
                    <xs:documentation>The defined assumptions, goals, and capabilities of an adversary.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="risk-assessment">
                <xs:annotation>
                    <xs:documentation>Identifies and analyzes the potential of future events that may negatively impact individuals, assets, and/or the environment. Risk assessments may also include judgments on the tolerability of each risk.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="vulnerability-assertion">
                <xs:annotation>
                    <xs:documentation>A Vulnerability Disclosure Report (VDR) which asserts the known and previously unknown vulnerabilities that affect a component, service, or product including the analysis and findings describing the impact (or lack of impact) that the reported vulnerability has on a component, service, or product.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="exploitability-statement">
                <xs:annotation>
                    <xs:documentation>A Vulnerability Exploitability eXchange (VEX) which asserts the known vulnerabilities that do not affect a product, product family, or organization, and optionally the ones that do. The VEX should include the analysis and findings describing the impact (or lack of impact) that the reported vulnerability has on the product, product family, or organization.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="pentest-report">
                <xs:annotation>
                    <xs:documentation>Results from an authorized simulated cyberattack on a component or service, otherwise known as a penetration test</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="static-analysis-report">
                <xs:annotation>
                    <xs:documentation>SARIF or proprietary machine or human-readable report for which static analysis has identified code quality, security, and other potential issues with the source code</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="dynamic-analysis-report">
                <xs:annotation>
                    <xs:documentation>Dynamic analysis report that has identified issues such as vulnerabilities and misconfigurations</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="runtime-analysis-report">
                <xs:annotation>
                    <xs:documentation>Report generated by analyzing the call stack of a running application</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="component-analysis-report">
                <xs:annotation>
                    <xs:documentation>Report generated by Software Composition Analysis (SCA), container analysis, or other forms of component analysis</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="maturity-report">
                <xs:annotation>
                    <xs:documentation>Report containing a formal assessment of an organization, business unit, or team against a maturity model</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="certification-report">
                <xs:annotation>
                    <xs:documentation>Industry, regulatory, or other certification from an accredited (if applicable) certification body</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="quality-metrics">
                <xs:annotation>
                    <xs:documentation>Report or system in which quality metrics can be obtained</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="codified-infrastructure">
                <xs:annotation>
                    <xs:documentation>Code or configuration that defines and provisions virtualized infrastructure, commonly referred to as Infrastructure as Code (IaC)</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="poam">
                <xs:annotation>
                    <xs:documentation>Plans of Action and Milestones (POA&amp;M) complement an "attestation" external reference. POA&amp;M is defined by NIST as a "document that identifies tasks needing to be accomplished. It details resources required to accomplish the elements of the plan, any milestones in meeting the tasks and scheduled completion dates for the milestones".</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="electronic-signature">
                <xs:annotation>
                    <xs:documentation>An e-signature is commonly a scanned representation of a written signature or a stylized script of the persons name.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="digital-signature">
                <xs:annotation>
                    <xs:documentation>A signature that leverages cryptography, typically public/private key pairs, which provides strong authenticity verification.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="rfc-9116">
                <xs:annotation>
                    <xs:documentation>Document that complies with RFC-9116 (A File Format to Aid in Security Vulnerability Disclosure)</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="other">
                <xs:annotation>
                    <xs:documentation>Use this if no other types accurately describe the purpose of the external reference</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="externalReferences">
        <xs:annotation>
            <xs:documentation xml:lang="en">
                External references provide a way to document systems, sites, and information that may be
                relevant, but are not included with the BOM. They may also establish specific relationships
                within or external to the BOM.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
            <xs:element name="reference" type="bom:externalReference">
                <xs:annotation>
                    <xs:documentation xml:lang="en">Zero or more external references can be defined</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="externalReference">
        <xs:sequence>
            <xs:element name="url" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation xml:lang="en">The URI (URL or URN) to the external reference. External references
                        are URIs and therefore can accept any URL scheme including https, mailto, tel, and dns.
                        External references may also include formally registered URNs such as CycloneDX BOM-Link to
                        reference CycloneDX BOMs or any object within a BOM. BOM-Link transforms applicable external
                        references into relationships that can be expressed in a BOM or across BOMs. Refer to:
                        https://cyclonedx.org/capabilities/bomlink/</xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:union memberTypes="xs:anyURI bom:bomLinkType"/>
                </xs:simpleType>
            </xs:element>
            <xs:element name="comment" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation xml:lang="en">An optional comment describing the external reference</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="hashes" minOccurs="0" maxOccurs="1">
                <xs:complexType>
                    <xs:sequence minOccurs="0" maxOccurs="unbounded">
                        <xs:element name="hash" type="bom:hashType"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="type" type="bom:externalReferenceType" use="required">
            <xs:annotation>
                <xs:documentation>Specifies the type of external reference. There are built-in types to describe common
                    references. If a type does not exist for the reference being referred to, use the "other" type.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="commitsType">
        <xs:annotation>
            <xs:documentation xml:lang="en">Zero or more commits can be specified.</xs:documentation>
        </xs:annotation>
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
            <xs:element name="commit" type="bom:commitType">
                <xs:annotation>
                    <xs:documentation xml:lang="en">Specifies an individual commit.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="commitType">
        <xs:sequence>
            <xs:element name="uid" type="xs:normalizedString" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation xml:lang="en">A unique identifier of the commit. This may be version control
                        specific. For example, Subversion uses revision numbers whereas git uses commit hashes.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="url" type="xs:anyURI" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation xml:lang="en">The URL to the commit. This URL will typically point to a commit
                        in a version control system.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="author" type="bom:identifiableActionType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation xml:lang="en">The author who created the changes in the commit</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="committer" type="bom:identifiableActionType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation xml:lang="en">The person who committed or pushed the commit</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="message" type="xs:normalizedString" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation xml:lang="en">The text description of the contents of the commit</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="patchesType">
        <xs:annotation>
            <xs:documentation xml:lang="en">Zero or more patches can be specified.</xs:documentation>
        </xs:annotation>
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
            <xs:element name="patch" type="bom:patchType">
                <xs:annotation>
                    <xs:documentation xml:lang="en">Specifies an individual patch.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="patchType">
        <xs:sequence>
            <xs:element name="diff" type="bom:diffType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation xml:lang="en">The patch file (or diff) that show changes.
                        Refer to https://en.wikipedia.org/wiki/Diff</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="resolves" minOccurs="0" maxOccurs="1">
                <xs:complexType>
                    <xs:sequence minOccurs="0" maxOccurs="unbounded">
                        <xs:element name="issue" type="bom:issueType"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:attribute name="type" type="bom:patchClassification" use="required">
            <xs:annotation>
                <xs:documentation>Specifies the purpose for the patch including the resolution of defects,
                    security issues, or new behavior or functionality</xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:simpleType name="patchClassification">
        <xs:restriction base="xs:string">
            <xs:enumeration value="unofficial">
                <xs:annotation>
                    <xs:documentation>A patch which is not developed by the creators or maintainers of the software
                        being patched. Refer to https://en.wikipedia.org/wiki/Unofficial_patch</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="monkey">
                <xs:annotation>
                    <xs:documentation>A patch which dynamically modifies runtime behavior.
                        Refer to https://en.wikipedia.org/wiki/Monkey_patch</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="backport">
                <xs:annotation>
                    <xs:documentation>A patch which takes code from a newer version of software and applies
                        it to older versions of the same software. Refer to https://en.wikipedia.org/wiki/Backporting</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="cherry-pick">
                <xs:annotation>
                    <xs:documentation>A patch created by selectively applying commits from other versions or
                        branches of the same software.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="issueClassification">
        <xs:restriction base="xs:string">
            <xs:enumeration value="defect">
                <xs:annotation>
                    <xs:documentation>A fault, flaw, or bug in software</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="enhancement">
                <xs:annotation>
                    <xs:documentation>A new feature or behavior in software</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="security">
                <xs:annotation>
                    <xs:documentation>A special type of defect which impacts security</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="diffType">
        <xs:sequence>
            <xs:element name="text" type="bom:attachedTextType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation xml:lang="en">Specifies the optional text of the diff</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="url" type="xs:anyURI" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation xml:lang="en">Specifies the URL to the diff</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="issueType">
        <xs:annotation>
            <xs:documentation>
                An individual issue that has been resolved.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="id" type="xs:normalizedString" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation xml:lang="en">The identifier of the issue assigned by the source of the issue</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="name" type="xs:normalizedString" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation xml:lang="en">The name of the issue</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="description" type="xs:normalizedString" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation xml:lang="en">A description of the issue</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="source" minOccurs="0" maxOccurs="1">
                <xs:complexType>
                    <xs:annotation>
                        <xs:documentation xml:lang="en">
                            The source of the issue where it is documented.
                        </xs:documentation>
                    </xs:annotation>
                    <xs:sequence>
                        <xs:element name="name" minOccurs="0" type="xs:normalizedString" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation xml:lang="en">
                                    The name of the source. For example "National Vulnerability Database",
                                    "NVD", and "Apache"
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="url" minOccurs="0" type="xs:anyURI" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation xml:lang="en">
                                    The url of the issue documentation as provided by the source
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="references" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation xml:lang="en">
                        A collection of URL's for reference. Multiple URLs are allowed.
                        Example: "https://example.com"
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence minOccurs="0" maxOccurs="unbounded">
                        <xs:element name="url" type="xs:anyURI"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:attribute name="type" type="bom:issueClassification" use="required">
            <xs:annotation>
                <xs:documentation>Specifies the type of issue</xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="identifiableActionType">
        <xs:sequence>
            <xs:element name="timestamp" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation xml:lang="en">The timestamp in which the action occurred</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="name" type="xs:normalizedString" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation xml:lang="en">The name of the individual who performed the action</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="email" type="xs:normalizedString" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation xml:lang="en">The email address of the individual who performed the action</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="pedigreeType">
        <xs:annotation>
            <xs:documentation xml:lang="en">
                Component pedigree is a way to document complex supply chain scenarios where components are created,
                distributed, modified, redistributed, combined with other components, etc. Pedigree supports viewing
                this complex chain from the beginning, the end, or anywhere in the middle. It also provides a way to
                document variants where the exact relation may not be known.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="ancestors" type="bom:componentsType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation xml:lang="en">Describes zero or more components in which a component is derived
                        from. This is commonly used to describe forks from existing projects where the forked version
                        contains a ancestor node containing the original component it was forked from. For example,
                        Component A is the original component. Component B is the component being used and documented
                        in the BOM. However, Component B contains a pedigree node with a single ancestor documenting
                        Component A - the original component from which Component B is derived from.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="descendants" type="bom:componentsType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation xml:lang="en">Descendants are the exact opposite of ancestors. This provides a
                        way to document all forks (and their forks) of an original or root component.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="variants" type="bom:componentsType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation xml:lang="en">Variants describe relations where the relationship between the
                        components are not known. For example, if Component A contains nearly identical code to
                        Component B. They are both related, but it is unclear if one is derived from the other,
                        or if they share a common ancestor.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="commits" type="bom:commitsType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation xml:lang="en">A list of zero or more commits which provide a trail describing
                        how the component deviates from an ancestor, descendant, or variant.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="patches" type="bom:patchesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation xml:lang="en">A list of zero or more patches describing how the component
                        deviates from an ancestor, descendant, or variant. Patches may be complementary to commits
                        or may be used in place of commits.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="notes" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation xml:lang="en">Notes, observations, and other non-structured commentary
                        describing the components pedigree.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="dependencyType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
            <xs:element name="dependency" type="bom:dependencyType" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>The component or service that is a dependency of this dependency object.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="provides" minOccurs="0">
                <xs:annotation>
                    <xs:documentation>
                        The component or service that define a given specification or standard, which is provided or implemented by this dependency object.
                        For example, a cryptographic library which implements a cryptographic algorithm. A component which implements another component does not imply that the implementation is in use.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:attribute name="ref" type="bom:refLinkType" use="required">
                        <xs:annotation>
                            <xs:documentation>References a component or service by its bom-ref attribute</xs:documentation>
                        </xs:annotation>
                    </xs:attribute>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="ref" type="bom:refLinkType" use="required">
            <xs:annotation>
                <xs:documentation>References a component or service by its bom-ref attribute</xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:anyAttribute namespace="##other" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="dependenciesType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
            <xs:element name="dependency" type="bom:dependencyType">
                <xs:annotation>
                    <xs:documentation>Defines the direct dependencies of a component or service. Components or services
                        that do not have their own dependencies must be declared as empty elements within the graph.
                        Components or services that are not represented in the dependency graph may have unknown
                        dependencies. It is recommended that implementations assume this to be opaque and not an
                        indicator of a object being dependency-free. It is recommended to leverage compositions to
                        indicate unknown dependency graphs.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="servicesType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
            <xs:element name="service" type="bom:service"/>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="service">
        <xs:sequence>
            <xs:element name="provider" type="bom:organizationalEntity" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The organization that provides the service.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="group" type="xs:normalizedString" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The grouping name, namespace, or identifier. This will often be a shortened,
                        single name of the company or project that produced the service or domain name.
                        Whitespace and special characters should be avoided.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="name" type="xs:normalizedString" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The name of the service. This will often be a shortened, single name
                        of the service.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="version" type="bom:versionType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The service version.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="description" type="xs:normalizedString" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Specifies a description for the service.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="endpoints" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The endpoint URIs of the service. Multiple endpoints are allowed.
                        Example: "https://example.com/api/v1/ticker"
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence minOccurs="0" maxOccurs="unbounded">
                        <xs:element name="endpoint" type="xs:anyURI" minOccurs="1">
                            <xs:annotation>
                                <xs:documentation>A service endpoint URI.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="authenticated" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>A boolean value indicating if the service requires authentication.
                        A value of true indicates the service requires authentication prior to use.
                        A value of false indicates the service does not require authentication.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="x-trust-boundary" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>A boolean value indicating if use of the service crosses a trust zone or boundary.
                        A value of true indicates that by using the service, a trust boundary is crossed.
                        A value of false indicates that by using the service, a trust boundary is not crossed.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="trustZone" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The name of the trust zone the service resides in.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="data" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Specifies information about the data including the directional flow of data and the data classification.</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:choice>
                        <xs:sequence minOccurs="0" maxOccurs="unbounded">
                            <xs:element name="classification" type="bom:dataClassificationType">
                                <xs:annotation>
                                    <xs:documentation>DEPRECATED: Specifies the data classification. THIS FIELD IS DEPRECATED AS OF v1.5. Use dataflow\classification instead</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                        <xs:element name="dataflow" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>Specifies the data classification.</xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="classification" type="bom:dataClassificationType" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>Specifies the data classification.</xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="governance" type="bom:dataGovernance" minOccurs="0" maxOccurs="1" />
                                    <xs:element name="source" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>The URI, URL, or BOM-Link of the components or services the data came in from.</xs:documentation>
                                        </xs:annotation>
                                        <xs:complexType>
                                            <xs:sequence minOccurs="0" maxOccurs="unbounded">
                                                <xs:element name="url">
                                                    <xs:simpleType>
                                                        <xs:union memberTypes="xs:anyURI bom:bomLinkElementType"/>
                                                    </xs:simpleType>
                                                </xs:element>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                    <xs:element name="destination" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>The URI, URL, or BOM-Link of the components or services the data is sent to.</xs:documentation>
                                        </xs:annotation>
                                        <xs:complexType>
                                            <xs:sequence minOccurs="0" maxOccurs="unbounded">
                                                <xs:element name="url">
                                                    <xs:simpleType>
                                                        <xs:union memberTypes="xs:anyURI bom:bomLinkElementType"/>
                                                    </xs:simpleType>
                                                </xs:element>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                </xs:sequence>
                                <xs:attribute name="name" type="xs:string" use="optional">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Name for the defined data.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:attribute name="description" type="xs:string" use="optional">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Short description of the data content and usage.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:anyAttribute namespace="##any" processContents="lax">
                                    <xs:annotation>
                                        <xs:documentation>User-defined attributes may be used on this element as long as they
                                            do not have the same name as an existing attribute used by the schema.</xs:documentation>
                                    </xs:annotation>
                                </xs:anyAttribute>
                            </xs:complexType>
                        </xs:element>
                    </xs:choice>
                </xs:complexType>
            </xs:element>
            <xs:element name="licenses" type="bom:licenseChoiceType" minOccurs="0" maxOccurs="1"/>
            <xs:element name="externalReferences" type="bom:externalReferences" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Provides the ability to document external references related to the service.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="properties" type="bom:propertiesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Provides the ability to document properties in a name/value store.
                        This provides flexibility to include data not officially supported in the standard
                        without having to use additional namespaces or create extensions. Property names
                        of interest to the general public are encouraged to be registered in the
                        CycloneDX Property Taxonomy - https://github.com/CycloneDX/cyclonedx-property-taxonomy.
                        Formal registration is optional.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="services" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        A list of services included or deployed behind the parent service. This is not a dependency
                        tree. It provides a way to specify a hierarchical representation of service assemblies.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence minOccurs="0" maxOccurs="unbounded">
                        <xs:element name="service" type="bom:service"/>
                        <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>
                                    Allows any undeclared elements as long as the elements are placed in a different namespace.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:any>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="releaseNotes" type="bom:releaseNotesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Specifies optional release notes.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="tags" type="bom:tagsType" minOccurs="0" maxOccurs="1" />
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:attribute name="bom-ref" type="bom:refType">
            <xs:annotation>
                <xs:documentation>
                    An optional identifier which can be used to reference the service elsewhere in the BOM.
                    Uniqueness is enforced within all elements and children of the root-level bom element.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="dataClassificationType">
        <xs:annotation>
            <xs:documentation>Specifies the data classification.</xs:documentation>
        </xs:annotation>
        <xs:simpleContent>
            <xs:extension base="xs:normalizedString">
                <xs:attribute name="flow" type="bom:dataFlowType" use="required">
                    <xs:annotation>
                        <xs:documentation>Specifies the flow direction of the data.</xs:documentation>
                    </xs:annotation>
                </xs:attribute>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>

    <xs:simpleType name="dataFlowType">
        <xs:annotation>
            <xs:documentation>Specifies the flow direction of the data. Valid values are:
                inbound, outbound, bi-directional, and unknown. Direction is relative to the service.
                Inbound flow states that data enters the service. Outbound flow states that data
                leaves the service. Bi-directional states that data flows both ways, and unknown
                states that the direction is not known.</xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="inbound">
                <xs:annotation>
                    <xs:documentation>
                        Data that enters a service.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="outbound">
                <xs:annotation>
                    <xs:documentation>Data that exits a service.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="bi-directional">
                <xs:annotation>
                    <xs:documentation>Data flows in and out of the service.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="unknown">
                <xs:annotation>
                    <xs:documentation>The directional flow of data is not known.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="licenseChoiceType">
        <xs:choice>
            <xs:element name="license" type="bom:licenseType" minOccurs="0" maxOccurs="unbounded"/>
            <xs:element name="expression" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>A valid SPDX license expression.
                        Refer to https://spdx.org/specifications for syntax requirements

                        Example values:
                        - Apache-2.0 AND (MIT OR GPL-2.0-only)
                        - GPL-3.0-only WITH Classpath-exception-2.0
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:simpleContent>
                        <xs:extension base="xs:normalizedString">
                            <xs:attribute name="bom-ref" type="bom:refType">
                                <xs:annotation>
                                    <xs:documentation>
                                        An optional identifier which can be used to reference the license elsewhere in the BOM.
                                        Uniqueness is enforced within all elements and children of the root-level bom element.
                                    </xs:documentation>
                                </xs:annotation>
                            </xs:attribute>
                            <xs:attribute name="acknowledgement" type="bom:licenseAcknowledgementEnumerationType">
                                <xs:annotation>
                                    <xs:documentation>
                                        Declared licenses and concluded licenses represent two different stages in the
                                        licensing process within software development. Declared licenses refer to the
                                        initial intention of the software authors regarding the licensing terms under
                                        which their code is released. On the other hand, concluded licenses are the
                                        result of a comprehensive analysis of the project's codebase to identify and
                                        confirm the actual licenses of the components used, which may differ from the
                                        initially declared licenses. While declared licenses provide an upfront indication
                                        of the licensing intentions, concluded licenses offer a more thorough understanding
                                        of the actual licensing within a project, facilitating proper compliance and risk
                                        management. Observed licenses are defined in `evidence.licenses`. Observed licenses
                                        form the evidence necessary to substantiate a concluded license.
                                    </xs:documentation>
                                </xs:annotation>
                            </xs:attribute>
                        </xs:extension>
                    </xs:simpleContent>
                </xs:complexType>
            </xs:element>
        </xs:choice>
    </xs:complexType>

    <xs:simpleType name="licenseAcknowledgementEnumerationType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="declared">
                    <xs:annotation>
                        <xs:documentation>
                            Declared licenses represent the initial intentions of authors regarding
                            the licensing terms of their code.
                        </xs:documentation>
                    </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="concluded">
                <xs:annotation>
                    <xs:documentation>
                        Concluded licenses are verified and confirmed.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="copyrightsType">
        <xs:sequence>
            <xs:element name="text" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="identityFieldType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="group"/>
            <xs:enumeration value="name"/>
            <xs:enumeration value="version"/>
            <xs:enumeration value="purl"/>
            <xs:enumeration value="cpe"/>
            <xs:enumeration value="omniborId"/>
            <xs:enumeration value="swhid"/>
            <xs:enumeration value="swid"/>
            <xs:enumeration value="hash"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="decimalPercentType">
        <xs:restriction base="xs:decimal">
            <xs:minInclusive value="0"/>
            <xs:maxInclusive value="1"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="evidenceTechnique">
        <xs:restriction base="xs:string">
            <xs:enumeration value="source-code-analysis">
                <xs:annotation>
                    <xs:documentation>
                        Examines the source code without executing it.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="binary-analysis">
                <xs:annotation>
                    <xs:documentation>
                        Examines a compiled binary through reverse engineering, typically via disassembly or bytecode reversal.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="manifest-analysis">
                <xs:annotation>
                    <xs:documentation>
                        Examines a package management system such as those used for building software or installing software.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="ast-fingerprint">
                <xs:annotation>
                    <xs:documentation>
                        Examines the Abstract Syntax Tree (AST) of source code or a compiled binary.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="hash-comparison">
                <xs:annotation>
                    <xs:documentation>
                        Evaluates the cryptographic hash of a component against a set of pre-computed hashes of identified software.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="instrumentation">
                <xs:annotation>
                    <xs:documentation>
                        Examines the call stack of running applications by intercepting and monitoring application logic without the need to modify the application.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="dynamic-analysis">
                <xs:annotation>
                    <xs:documentation>
                        Evaluates a running application.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="filename">
                <xs:annotation>
                    <xs:documentation>
                        Evaluates file name of a component against a set of known file names of identified software.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="attestation">
                <xs:annotation>
                    <xs:documentation>
                        A testimony to the accuracy of the identify of a component made by an individual or entity.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="other">
                <xs:annotation>
                    <xs:documentation>
                        Any other technique.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="componentEvidenceType">
        <xs:sequence>
            <xs:element name="identity" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>Evidence that substantiates the identity of a component. The identify may be an
                        object or an array of identity objects. Support for specifying identity as a single object was
                        introduced in CycloneDX v1.5. "unbounded" was introduced in v1.6. It is recommended that all
                        implementations are aware of "unbounded".</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="field" type="bom:identityFieldType" minOccurs="1" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>The identity field of the component which the evidence describes.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="confidence" type="bom:decimalPercentType" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>The overall confidence of the evidence from 0 - 1, where 1 is 100% confidence.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="concludedValue" type="xs:string" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>The value of the field (cpe, purl, etc) that has been concluded based on the aggregate of all methods (if available).</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="methods" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>The methods used to extract and/or analyze the evidence.</xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="method" minOccurs="0" maxOccurs="unbounded">
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="technique" type="bom:evidenceTechnique" minOccurs="1" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>The technique used in this method of analysis.</xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                                <xs:element name="confidence" type="bom:decimalPercentType" minOccurs="1" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>The confidence of the evidence from 0 - 1, where 1 is 100% confidence. Confidence is specific to the technique used. Each technique of analysis can have independent confidence.</xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                                <xs:element name="value" type="xs:string" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>The value or contents of the evidence.</xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="tools" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The object in the BOM identified by its bom-ref. This is often a component or service,
                                    but may be any object type supporting bom-refs. Tools used for analysis should already
                                    be defined in the BOM, either in the metadata/tools, components, or formulation.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="tool" type="bom:bomReferenceType" minOccurs="0" maxOccurs="unbounded"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="occurrences" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Evidence of individual instances of a component spread across multiple locations.</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="occurrence" minOccurs="0" maxOccurs="unbounded">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="location" type="xs:string" minOccurs="1" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>The location or path to where the component was found.</xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="line" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>The line number where the component was found.</xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="offset" type="xs:nonNegativeInteger" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>The offset where the component was found.</xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="symbol" type="xs:string" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>The symbol name that was found associated with the component.</xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="additionalContext" type="xs:string" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>Any additional context of the detected component (e.g. a code snippet).</xs:documentation>
                                        </xs:annotation>
                                    </xs:element>

                                </xs:sequence>
                                <xs:attribute name="bom-ref" type="bom:refType">
                                    <xs:annotation>
                                        <xs:documentation>
                                            An optional identifier which can be used to reference the occurrence elsewhere
                                            in the BOM. Every bom-ref must be unique within the BOM.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="callstack" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Evidence of the components use through the callstack.</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="frames" minOccurs="0" maxOccurs="1">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="frame" minOccurs="0" maxOccurs="unbounded">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Within a call stack, a frame is a discrete unit that encapsulates an execution context, including local variables, parameters, and the return address. As function calls are made, frames are pushed onto the stack, forming an array-like structure that orchestrates the flow of program execution and manages the sequence of function invocations.
                                            </xs:documentation>
                                        </xs:annotation>
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="package" type="xs:string" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>A package organizes modules into namespaces, providing a unique namespace for each type it contains.</xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                                <xs:element name="module" type="xs:string" minOccurs="1" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>A module or class that encloses functions/methods and other code.</xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                                <xs:element name="function" type="xs:string" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>A block of code designed to perform a particular task.</xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                                <xs:element name="parameters" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>Optional arguments that are passed to the module or function.</xs:documentation>
                                                    </xs:annotation>
                                                    <xs:complexType>
                                                        <xs:sequence>
                                                            <xs:element name="parameter" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
                                                        </xs:sequence>
                                                    </xs:complexType>
                                                </xs:element>
                                                <xs:element name="line" type="xs:integer" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>The line number the code that is called resides on.</xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                                <xs:element name="column" type="xs:integer" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>The column the code that is called resides.</xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                                <xs:element name="fullFilename" type="xs:string" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>The full path and filename of the module.</xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="tools" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The object in the BOM identified by its bom-ref. This is often a component or service,
                                    but may be any object type supporting bom-refs. Tools used for analysis should already
                                    be defined in the BOM, either in the metadata/tools, components, or formulation.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="tool" type="bom:bomReferenceType" minOccurs="0" maxOccurs="unbounded"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="licenses" type="bom:licenseChoiceType" minOccurs="0" maxOccurs="1"/>
            <xs:element name="copyright" type="bom:copyrightsType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        opyright evidence captures intellectual property assertions, providing evidence of possible ownership and legal protection.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="compositionsType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
            <xs:element name="composition" type="bom:compositionType"/>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="compositionType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
            <xs:element name="aggregate" type="bom:aggregateType" default="not_specified">
                <xs:annotation>
                    <xs:documentation>Specifies an aggregate type that describe how complete a relationship is.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="assemblies" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The bom-ref identifiers of the components or services being described. Assemblies refer to
                        nested relationships whereby a constituent part may include other constituent parts. References
                        do not cascade to child parts. References are explicit for the specified constituent part only.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence minOccurs="0" maxOccurs="unbounded">
                        <xs:element name="assembly" type="bom:bomReferenceType"/>
                        <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>
                                    Allows any undeclared elements as long as the elements are placed in a different namespace.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:any>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="dependencies" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The bom-ref identifiers of the components or services being described. Dependencies refer to a
                        relationship whereby an independent constituent part requires another independent constituent
                        part. References do not cascade to transitive dependencies. References are explicit for the
                        specified dependency only.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence minOccurs="0" maxOccurs="unbounded">
                        <xs:element name="dependency" type="bom:bomReferenceType"/>
                        <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>
                                    Allows any undeclared elements as long as the elements are placed in a different namespace.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:any>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="vulnerabilities" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The bom-ref identifiers of the vulnerabilities being described.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence minOccurs="0" maxOccurs="unbounded">
                        <xs:element name="vulnerability" type="bom:bomReferenceType"/>
                        <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>
                                    Allows any undeclared elements as long as the elements are placed in a different namespace.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:any>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="bom-ref" type="bom:refType">
            <xs:annotation>
                <xs:documentation>
                    An optional identifier which can be used to reference the composition elsewhere in the BOM.
                    Uniqueness is enforced within all elements and children of the root-level bom element.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:simpleType name="aggregateType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="complete">
                <xs:annotation>
                    <xs:documentation>The relationship is complete. No further relationships including constituent components, services, or dependencies are known to exist.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="incomplete">
                <xs:annotation>
                    <xs:documentation>The relationship is incomplete. Additional relationships exist and may include constituent components, services, or dependencies.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="incomplete_first_party_only">
                <xs:annotation>
                    <xs:documentation>The relationship is incomplete. Only relationships for first-party components, services, or their dependencies are represented.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="incomplete_first_party_proprietary_only">
                <xs:annotation>
                    <xs:documentation>The relationship is incomplete. Only relationships for third-party components, services, or their dependencies are represented, limited specifically to those that are proprietary.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="incomplete_first_party_opensource_only">
                <xs:annotation>
                    <xs:documentation>The relationship is incomplete. Only relationships for third-party components, services, or their dependencies are represented, limited specifically to those that are opensource.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="incomplete_third_party_only">
                <xs:annotation>
                    <xs:documentation>The relationship is incomplete. Only relationships for third-party components, services, or their dependencies are represented.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="incomplete_third_party_proprietary_only">
                <xs:annotation>
                    <xs:documentation>The relationship is incomplete. Only relationships for third-party components, services, or their dependencies are represented, limited specifically to those that are proprietary.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="incomplete_third_party_opensource_only">
                <xs:annotation>
                    <xs:documentation>The relationship is incomplete. Only relationships for third-party components, services, or their dependencies are represented, limited specifically to those that are opensource.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="unknown">
                <xs:annotation>
                    <xs:documentation>The relationship may be complete or incomplete. This usually signifies a 'best-effort' to obtain constituent components, services, or dependencies but the completeness is inconclusive.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="not_specified">
                <xs:annotation>
                    <xs:documentation>The relationship completeness is not specified.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="localeType">
        <xs:annotation>
            <xs:documentation xml:lang="en">
                Defines a syntax for representing two character language code (ISO-639) followed by an optional two
                character country code. The language code must be lower case. If the country code is specified, the
                country code must be upper case. The language code and country code must be separated by a minus sign.
                Examples: en, en-US, fr, fr-CA
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:pattern value="([a-z]{2})(-[A-Z]{2})?"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="releaseNotesType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
            <xs:element name="type" type="xs:normalizedString" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The software versioning type. It is recommended that the release type use one
                        of 'major', 'minor', 'patch', 'pre-release', or 'internal'. Representing all possible software
                        release types is not practical, so standardizing on the recommended values, whenever possible,
                        is strongly encouraged.
                        * major = A major release may contain significant changes or may introduce breaking changes.
                        * minor = A minor release, also known as an update, may contain a smaller number of changes than major releases.
                        * patch = Patch releases are typically unplanned and may resolve defects or important security issues.
                        * pre-release = A pre-release may include alpha, beta, or release candidates and typically have
                          limited support. They provide the ability to preview a release prior to its general availability.
                        * internal = Internal releases are not for public consumption and are intended to be used exclusively
                          by the project or manufacturer that produced it.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="title" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The title of the release.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="featuredImage" type="xs:anyURI" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The URL to an image that may be prominently displayed with the release note.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="socialImage" type="xs:anyURI" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The URL to an image that may be used in messaging on social media platforms.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="description" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>A short description of the release.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="timestamp" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The date and time (timestamp) when the release note was created.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="aliases" minOccurs="0" maxOccurs="1">
                <xs:complexType>
                    <xs:sequence minOccurs="0" maxOccurs="unbounded">
                        <xs:element name="alias" type="xs:normalizedString">
                            <xs:annotation>
                                <xs:documentation>One or more alternate names the release may be referred to. This may
                                include unofficial terms used by development and marketing teams (e.g. code names).</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="tags" type="bom:tagsType" minOccurs="0" maxOccurs="1" />
            <xs:element name="resolves" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>A collection of issues that have been resolved.</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence minOccurs="0" maxOccurs="unbounded">
                        <xs:element name="issue" type="bom:issueType"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="notes" minOccurs="0" maxOccurs="1">
                <xs:complexType>
                    <xs:sequence minOccurs="0" maxOccurs="unbounded">
                        <xs:element name="note">
                            <xs:annotation>
                                <xs:documentation>Zero or more release notes containing the locale and content. Multiple
                                note elements may be specified to support release notes in a wide variety of languages.</xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence minOccurs="0" maxOccurs="unbounded">
                                    <xs:element name="locale" type="bom:localeType" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>The ISO-639 (or higher) language code and optional ISO-3166
                                                (or higher) country code. Examples include: "en", "en-US", "fr" and "fr-CA".</xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="text" type="bom:attachedTextType" minOccurs="1" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>Specifies the full content of the release note.</xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="properties" type="bom:propertiesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Provides the ability to document properties in a name/value store.
                        This provides flexibility to include data not officially supported in the standard
                        without having to use additional namespaces or create extensions. Property names
                        of interest to the general public are encouraged to be registered in the
                        CycloneDX Property Taxonomy - https://github.com/CycloneDX/cyclonedx-property-taxonomy.
                        Formal registration is optional.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="modelCardType">
        <!--
        Model card support in CycloneDX is derived from TensorFlow Model Card Toolkit released under the Apache 2.0 license and
        available from https://github.com/tensorflow/model-card-toolkit/blob/main/model_card_toolkit/schema/v0.0.2/model_card.schema.json.
        In addition, CycloneDX model card support includes portions of VerifyML, also released under the Apache 2.0 license and
        available from https://github.com/cylynx/verifyml/blob/main/verifyml/model_card_toolkit/schema/v0.0.4/model_card.schema.json.
        -->
        <xs:annotation>
            <xs:documentation>
                A model card describes the intended uses of a machine learning model and potential limitations, including
                biases and ethical considerations. Model cards typically contain the training parameters, which datasets
                were used to train the model, performance metrics, and other relevant data useful for ML transparency.
                This object SHOULD be specified for any component of type `machine-learning-model` and must not be specified
                for other component types.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="modelParameters" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Hyper-parameters for construction of the model.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="approach" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The overall approach to learning used by the model for problem solving.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="type" type="bom:machineLearningApproachType" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Learning types describing the learning problem or hybrid learning problem.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="task" type="xs:string" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    Directly influences the input and/or output. Examples include classification,
                                    regression, clustering, etc.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="architectureFamily" type="xs:string" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The model architecture family such as transformer network, convolutional neural
                                    network, residual neural network, LSTM neural network, etc.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="modelArchitecture" type="xs:string" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The specific architecture of the model such as GPT-1, ResNet-50, YOLOv3, etc.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="datasets" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The datasets used to train and evaluate the model.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:choice minOccurs="0" maxOccurs="unbounded">
                                    <xs:element name="ref" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>References a data component by the components bom-ref attribute</xs:documentation>
                                        </xs:annotation>
                                        <xs:simpleType>
                                            <xs:union memberTypes="bom:refLinkType bom:bomLinkElementType"/>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="dataset" type="bom:componentDataType" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>Inline Data Information</xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                </xs:choice>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="inputs" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The input format(s) of the model
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="input" minOccurs="0" maxOccurs="unbounded">
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="format" type="xs:string" minOccurs="1" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            The data format for input to the model. Example formats include string, image, time-series
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="outputs" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The output format(s) from the model
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="output" minOccurs="0" maxOccurs="unbounded">
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="format" type="xs:string" minOccurs="1" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            The data format for output from the model. Example formats include string, image, time-series
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="quantitativeAnalysis" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        A quantitative analysis of the model
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="performanceMetrics" minOccurs="0" maxOccurs="1">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="performanceMetric" minOccurs="0" maxOccurs="unbounded">
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="type" type="xs:string" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            The type of performance metric.
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                                <xs:element name="value" type="xs:string" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            The value of the performance metric.
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                                <xs:element name="slice" type="xs:string" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            The name of the slice this metric was computed on. By default, assume
                                                            this metric is not sliced.
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                                <xs:element name="confidenceInterval" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            The confidence interval of the metric.
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                    <xs:complexType>
                                                        <xs:sequence>
                                                            <xs:element name="lowerBound" type="xs:string" minOccurs="0" maxOccurs="1">
                                                                <xs:annotation>
                                                                    <xs:documentation>
                                                                        The lower bound of the confidence interval.
                                                                    </xs:documentation>
                                                                </xs:annotation>
                                                            </xs:element>
                                                            <xs:element name="upperBound" type="xs:string" minOccurs="0" maxOccurs="1">
                                                                <xs:annotation>
                                                                    <xs:documentation>
                                                                        The upper bound of the confidence interval.
                                                                    </xs:documentation>
                                                                </xs:annotation>
                                                            </xs:element>
                                                        </xs:sequence>
                                                    </xs:complexType>
                                                </xs:element>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="graphics" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    A collection of graphics that represent various measurements
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="description" type="xs:string" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                A description of this collection of graphics.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="collection" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                A collection of graphics.
                                            </xs:documentation>
                                        </xs:annotation>
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="graphic" minOccurs="0" maxOccurs="unbounded">
                                                    <xs:complexType>
                                                        <xs:sequence>
                                                            <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1">
                                                                <xs:annotation>
                                                                    <xs:documentation>
                                                                        The name of the graphic.
                                                                    </xs:documentation>
                                                                </xs:annotation>
                                                            </xs:element>
                                                            <xs:element name="image" type="bom:attachedTextType" minOccurs="0" maxOccurs="1">
                                                                <xs:annotation>
                                                                    <xs:documentation>
                                                                        The graphic (vector or raster). Base64 encoding must be specified for binary images.
                                                                    </xs:documentation>
                                                                </xs:annotation>
                                                            </xs:element>
                                                        </xs:sequence>
                                                    </xs:complexType>
                                                </xs:element>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="considerations" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        What considerations should be taken into account regarding the model's construction, training,
                        and application?
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="users" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    Who are the intended users of the model?
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="user" type="xs:string" minOccurs="0" maxOccurs="1" />
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="useCases" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    What are the intended use cases of the model?
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="useCase" type="xs:string" minOccurs="0" maxOccurs="1" />
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="technicalLimitations" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    What are the known technical limitations of the model? E.g. What kind(s) of data
                                    should the model be expected not to perform well on? What are the factors that might
                                    degrade model performance?
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="technicalLimitation" type="xs:string" minOccurs="0" maxOccurs="1" />
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="performanceTradeoffs" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    What are the known tradeoffs in accuracy/performance of the model?
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="performanceTradeoff" type="xs:string" minOccurs="0" maxOccurs="1" />
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="ethicalConsiderations" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    What are the ethical risks involved in the application of this model?
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="ethicalConsideration" minOccurs="0" maxOccurs="unbounded">
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            The name of the risk
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                                <xs:element name="mitigationStrategy" type="xs:string" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            Strategy used to address this risk
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="environmentalConsiderations" type="bom:environmentalConsiderationsType" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    What are the various environmental impacts the corresponding machine learning model has exhibited across its lifecycle?
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="fairnessAssessments" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    How does the model affect groups at risk of being systematically disadvantaged?
                                    What are the harms and benefits to the various affected groups?
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="fairnessAssessment" minOccurs="0" maxOccurs="unbounded">
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="groupAtRisk" type="xs:string" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            The groups or individuals at risk of being systematically disadvantaged by the model.
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                                <xs:element name="benefits" type="xs:string" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            Expected benefits to the identified groups.
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                                <xs:element name="harms" type="xs:string" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            Expected harms to the identified groups.
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                                <xs:element name="mitigationStrategy" type="xs:string" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            With respect to the benefits and harms outlined, please
                                                            describe any mitigation strategy implemented.
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="bom-ref" type="bom:refType">
            <xs:annotation>
                <xs:documentation>
                    An optional identifier which can be used to reference the model card elsewhere in the BOM.
                    Every bom-ref must be unique within the BOM.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="environmentalConsiderationsType">
        <xs:annotation>
            <xs:documentation>
                Describes various environmental impact metrics.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="energyConsumptions" type="bom:energyConsumptionsType" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Describes energy consumption information incurred for one or more component lifecycle activities.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="properties" type="bom:propertiesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Provides the ability to document properties in a name/value store.
                        This provides flexibility to include data not officially supported in the standard
                        without having to use additional namespaces or create extensions. Property names
                        of interest to the general public are encouraged to be registered in the
                        CycloneDX Property Taxonomy - https://github.com/CycloneDX/cyclonedx-property-taxonomy.
                        Formal registration is optional.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="energyConsumptionsType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
            <xs:element name="energyConsumption" type="bom:energyConsumptionType"/>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="energyConsumptionType">
        <xs:annotation>
            <xs:documentation>
                Describes energy consumption information incurred for the specified lifecycle activity.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="activity" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The type of activity that is part of a machine learning model development or operational lifecycle.
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="design">
                            <xs:annotation>
                                <xs:documentation>
                                    model design including problem framing, goal definition and algorithm selection.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:enumeration>
                        <xs:enumeration value="data-collection">
                            <xs:annotation>
                                <xs:documentation>
                                    model data acquisition including search, selection and transfer.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:enumeration>
                        <xs:enumeration value="data-preparation">
                            <xs:annotation>
                                <xs:documentation>
                                    model data preparation including data cleaning, labeling and conversion.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:enumeration>
                        <xs:enumeration value="training">
                            <xs:annotation>
                                <xs:documentation>
                                    model building, training and generalized tuning.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:enumeration>
                        <xs:enumeration value="fine-tuning">
                            <xs:annotation>
                                <xs:documentation>
                                    refining a trained model to produce desired outputs for a given problem space.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:enumeration>
                        <xs:enumeration value="validation">
                            <xs:annotation>
                                <xs:documentation>
                                    model validation including model output evaluation and testing.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:enumeration>
                        <xs:enumeration value="deployment">
                            <xs:annotation>
                                <xs:documentation>
                                    explicit model deployment to a target hosting infrastructure.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:enumeration>
                        <xs:enumeration value="inference">
                            <xs:annotation>
                                <xs:documentation>
                                    generating an output response from a hosted model from a set of inputs.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:enumeration>
                        <xs:enumeration value="other">
                            <xs:annotation>
                                <xs:documentation>
                                    a lifecycle activity type whose description does not match currently defined values.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:enumeration>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="energyProviders" type="bom:energyProviderType" minOccurs="1" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        The provider(s) of the energy consumed by the associated model development lifecycle activity.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="activityEnergyCost" type="bom:energyMeasureType" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The total energy cost associated with the model lifecycle activity.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="co2CostEquivalent" type="bom:co2MeasureType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The CO2 cost (debit) equivalent to the total energy cost.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="co2CostOffset" type="bom:co2MeasureType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The CO2 offset (credit) for the CO2 equivalent cost.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="properties" type="bom:propertiesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Provides the ability to document properties in a name/value store.
                        This provides flexibility to include data not officially supported in the standard
                        without having to use additional namespaces or create extensions. Property names
                        of interest to the general public are encouraged to be registered in the
                        CycloneDX Property Taxonomy - https://github.com/CycloneDX/cyclonedx-property-taxonomy.
                        Formal registration is optional.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="energyMeasureType">
        <xs:annotation>
            <xs:documentation>
                A measure of energy.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="value" type="xs:decimal" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Quantity of energy.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="unit" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Unit of energy.
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="kWh">
                            <xs:annotation>
                                <xs:documentation>
                                    kilowatt-hour (kWh) is the energy delivered by one kilowatt (kW) of power for one hour (h).
                                </xs:documentation>
                            </xs:annotation>
                        </xs:enumeration>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="co2MeasureType">
        <xs:annotation>
            <xs:documentation>
                A measure of carbon dioxide (CO2).
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="value" type="xs:decimal" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Quantity of carbon dioxide (CO2).
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="unit" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Unit of carbon dioxide (CO2).
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="tCO2eq">
                            <xs:annotation>
                                <xs:documentation>
                                    Tonnes (t) of carbon dioxide (CO2) equivalent (eq).
                                </xs:documentation>
                            </xs:annotation>
                        </xs:enumeration>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="energyProviderType">
        <xs:annotation>
            <xs:documentation>
                Describes the physical provider of energy used for model development or operations.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="description" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        A description of the energy provider.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="organization" type="bom:organizationalEntity" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The organization of the energy provider.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="energySource" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The energy source for the energy provider.
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="coal">
                            <xs:annotation>
                                <xs:documentation>
                                    Energy produced by types of coal.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:enumeration>
                        <xs:enumeration value="oil">
                            <xs:annotation>
                                <xs:documentation>
                                    Petroleum products (primarily crude oil and its derivative fuel oils).
                                </xs:documentation>
                            </xs:annotation>
                        </xs:enumeration>
                        <xs:enumeration value="natural-gas">
                            <xs:annotation>
                                <xs:documentation>
                                    Hydrocarbon gas liquids (HGL) that occur as gases at atmospheric pressure and as liquids under higher pressures including Natural gas (C5H12 and heavier), Ethane (C2H6), Propane (C3H8), etc.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:enumeration>
                        <xs:enumeration value="nuclear">
                            <xs:annotation>
                                <xs:documentation>
                                    Energy produced from the cores of atoms (i.e., through nuclear fission or fusion).
                                </xs:documentation>
                            </xs:annotation>
                        </xs:enumeration>
                        <xs:enumeration value="wind">
                            <xs:annotation>
                                <xs:documentation>
                                    Energy produced from moving air.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:enumeration>
                        <xs:enumeration value="solar">
                            <xs:annotation>
                                <xs:documentation>
                                    Energy produced from the sun (i.e., solar radiation).
                                </xs:documentation>
                            </xs:annotation>
                        </xs:enumeration>
                        <xs:enumeration value="geothermal">
                            <xs:annotation>
                                <xs:documentation>
                                    Energy produced from heat within the earth.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:enumeration>
                        <xs:enumeration value="hydropower">
                            <xs:annotation>
                                <xs:documentation>
                                    Energy produced from flowing water.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:enumeration>
                        <xs:enumeration value="biofuel">
                            <xs:annotation>
                                <xs:documentation>
                                    Liquid fuels produced from biomass feedstocks (i.e., organic materials such as plants or animals).
                                </xs:documentation>
                            </xs:annotation>
                        </xs:enumeration>
                        <xs:enumeration value="unknown">
                            <xs:annotation>
                                <xs:documentation>
                                    The energy source is unknown.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:enumeration>
                        <xs:enumeration value="other">
                            <xs:annotation>
                                <xs:documentation>
                                    An energy source that is not listed.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:enumeration>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="energyProvided" type="bom:energyMeasureType" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The energy provided by the energy source for an associated activity.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="externalReferences" type="bom:externalReferences" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>External references provide a way to document systems, sites, and information that may be relevant but are not included with the BOM. They may also establish specific relationships within or external to the BOM.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="bom-ref" type="bom:refType">
            <xs:annotation>
                <xs:documentation>
                    An optional identifier which can be used to reference the energy provider elsewhere in the BOM.
                    Uniqueness is enforced within all elements and children of the root-level bom element.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="postalAddressType">
        <xs:annotation>
            <xs:documentation>
                An address used to identify a contactable location.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="country" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The country name or the two-letter ISO 3166-1 country code.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="region" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The region or state in the country. For example, Texas.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="locality" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The locality or city within the country. For example, Austin.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="postOfficeBoxNumber" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The post office box number. For example, 901.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="postalCode" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The postal code. For example, 78758.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="streetAddress" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The street address. For example, 100 Main Street.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="bom-ref" type="bom:refType">
            <xs:annotation>
                <xs:documentation>
                    An optional identifier which can be used to reference the address elsewhere in the BOM.
                    Uniqueness is enforced within all elements and children of the root-level bom element.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:simpleType name="machineLearningApproachType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="supervised">
                <xs:annotation>
                    <xs:documentation>
                        Supervised machine learning involves training an algorithm on labeled
                        data to predict or classify new data based on the patterns learned from
                        the labeled examples.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="unsupervised">
                <xs:annotation>
                    <xs:documentation>
                        Unsupervised machine learning involves training algorithms on unlabeled
                        data to discover patterns, structures, or relationships without explicit
                        guidance, allowing the model to identify inherent structures or clusters
                        within the data.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="reinforcement-learning">
                <xs:annotation>
                    <xs:documentation>
                        Reinforcement learning is a type of machine learning where an agent learns
                        to make decisions by interacting with an environment to maximize cumulative
                        rewards, through trial and error.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="semi-supervised">
                <xs:annotation>
                    <xs:documentation>
                        Semi-supervised machine learning utilizes a combination of labeled and
                        unlabeled data during training to improve model performance, leveraging
                        the benefits of both supervised and unsupervised learning techniques.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="self-supervised">
                <xs:annotation>
                    <xs:documentation>
                        Self-supervised machine learning involves training models to predict parts
                        of the input data from other parts of the same data, without requiring
                        external labels, enabling learning from large amounts of unlabeled data.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="componentDataType">
        <xs:sequence>
            <xs:element name="type" type="bom:componentDataTypeEnumeration" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The general theme or subject matter of the data being specified.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The name of the dataset.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="contents" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The contents or references to the contents of the data being described.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="attachment" type="bom:attachedTextType" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>An optional way to include textual or encoded data.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="url" type="xs:anyURI" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>The URL to where the data can be retrieved.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="properties" type="bom:propertiesType" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Provides the ability to document name-value parameters used for configuration.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="classification" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Data classification tags data according to its type, sensitivity, and value if altered, stolen, or destroyed.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="sensitiveData" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        A description of any sensitive data in a dataset.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="graphics" type="bom:graphicsCollectionType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        A collection of graphics that represent various measurements.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="description" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        A description of the dataset. Can describe size of dataset, whether it's used for source code,
                        training, testing, or validation, etc.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="governance" type="bom:dataGovernance" minOccurs="0" maxOccurs="1" />
        </xs:sequence>
        <xs:attribute name="bom-ref" type="bom:refType">
            <xs:annotation>
                <xs:documentation>
                    An optional identifier which can be used to reference the dataset elsewhere in the BOM.
                    Every bom-ref must be unique within the BOM.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="dataGovernance">
        <xs:sequence>
            <xs:element name="custodians" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Data custodians are responsible for the safe custody, transport, and storage of data.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="custodian" type="bom:organizationOrIndividualType" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="stewards" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Data stewards are responsible for data content, context, and associated business rules.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="steward" type="bom:organizationOrIndividualType" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="owners" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Data owners are concerned with risk and appropriate access to data.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="owner" type="bom:organizationOrIndividualType" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="organizationOrIndividualType">
        <xs:choice>
            <xs:element name="organization" type="bom:organizationalEntity" minOccurs="0" maxOccurs="1" />
            <xs:element name="individual" type="bom:organizationalContact" minOccurs="0" maxOccurs="1" />
        </xs:choice>
    </xs:complexType>

    <xs:complexType name="graphicsCollectionType">
        <xs:annotation>
            <xs:documentation>
                A collection of graphics that represent various measurements.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="description" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        A description of this collection of graphics.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="collection" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        A collection of graphics.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="graphic" minOccurs="0" maxOccurs="unbounded">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The name of the graphic.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="image" type="bom:attachedTextType" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The graphic (vector or raster). Base64 encoding must be specified for binary images.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="componentDataTypeEnumeration">
        <xs:restriction base="xs:string">
            <xs:enumeration value="source-code">
                <xs:annotation>
                    <xs:documentation>Any type of code, code snippet, or data-as-code.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="configuration">
                <xs:annotation>
                    <xs:documentation>Parameters or settings that may be used by other components.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="dataset">
                <xs:annotation>
                    <xs:documentation>A collection of data.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="definition">
                <xs:annotation>
                    <xs:documentation>Data that can be used to create new instances of what the definition defines.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="other">
                <xs:annotation>
                    <xs:documentation>Any other type of data that does not fit into existing definitions.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="bomReferenceType">
        <xs:attribute name="ref" use="required">
            <xs:annotation>
                <xs:documentation>References a component or service by its bom-ref attribute</xs:documentation>
            </xs:annotation>
            <xs:simpleType>
                <xs:union memberTypes="bom:refLinkType bom:bomLinkType"/>
            </xs:simpleType>
        </xs:attribute>
        <xs:anyAttribute namespace="##other" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="propertiesType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
            <xs:element name="property" type="bom:propertyType"/>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="propertyType">
        <xs:annotation>
            <xs:documentation>Specifies an individual property with a name and value.</xs:documentation>
        </xs:annotation>
        <xs:simpleContent>
            <xs:extension base="xs:normalizedString">
                <xs:attribute name="name" type="xs:string" use="required">
                    <xs:annotation>
                        <xs:documentation>The name of the property. Duplicate names are allowed, each potentially having a different value.</xs:documentation>
                    </xs:annotation>
                </xs:attribute>
            </xs:extension>
        </xs:simpleContent>
    </xs:complexType>

    <xs:complexType name="vulnerabilitiesType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
            <xs:element name="vulnerability" type="bom:vulnerabilityType">
                <xs:annotation>
                    <xs:documentation>Defines a weakness in a component or service that could be exploited or triggered by a threat source.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="vulnerabilityType">
        <xs:sequence minOccurs="0" maxOccurs="1">
            <xs:element name="id" type="xs:normalizedString" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The identifier that uniquely identifies the vulnerability. For example:
                        CVE-2021-39182, GHSA-35m5-8cvj-8783, and SNYK-PYTHON-ENROCRYPT-1912876.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="source" type="bom:vulnerabilitySourceType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The source that published the vulnerability.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="references" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Zero or more pointers to vulnerabilities that are the equivalent of the
                        vulnerability specified. Often times, the same vulnerability may exist in multiple sources of
                        vulnerability intelligence, but have different identifiers. References provide a way to
                        correlate vulnerabilities across multiple sources of vulnerability intelligence.</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence minOccurs="0" maxOccurs="unbounded">
                        <xs:element name="reference">
                            <xs:annotation>
                                <xs:documentation>A pointer to a vulnerability that is the equivalent of the
                                    vulnerability specified.</xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence minOccurs="1" maxOccurs="1">
                                    <xs:element name="id" type="xs:normalizedString" minOccurs="1" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>The identifier that uniquely identifies the vulnerability. For example:
                                                CVE-2021-39182, GHSA-35m5-8cvj-8783, and SNYK-PYTHON-ENROCRYPT-1912876.</xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="source" type="bom:vulnerabilitySourceType" minOccurs="1" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>The source that published the vulnerability.</xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>
                                    Allows any undeclared elements as long as the elements are placed in a different namespace.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:any>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="ratings" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation xml:lang="en">List of vulnerability ratings.</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="rating" type="bom:ratingType" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="cwes" minOccurs="0" maxOccurs="1">
                <xs:complexType>
                    <xs:annotation>
                        <xs:documentation xml:lang="en">
                            List of Common Weaknesses Enumerations (CWEs) codes that describes this vulnerability.
                            For example 399 (of https://cwe.mitre.org/data/definitions/399.html)
                        </xs:documentation>
                    </xs:annotation>
                    <xs:sequence>
                        <xs:element name="cwe" type="xs:integer" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="description" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>A description of the vulnerability as provided by the source.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="detail" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>If available, an in-depth description of the vulnerability as provided by the
                        source organization. Details often include information useful in understanding root cause.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="recommendation" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Recommendations of how the vulnerability can be remediated or mitigated.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="workaround" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>A bypass, usually temporary, of the vulnerability that reduces its likelihood and/or impact. Workarounds often involve changes to configuration or deployments.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="proofOfConcept" minOccurs="0" maxOccurs="1">
                <xs:complexType>
                    <xs:annotation>
                        <xs:documentation xml:lang="en">
                            Evidence used to reproduce the vulnerability.
                        </xs:documentation>
                    </xs:annotation>
                    <xs:sequence>
                        <xs:element name="reproductionSteps" type="xs:string" minOccurs="0" maxOccurs="1">
                                <xs:annotation>
                                    <xs:documentation>Precise steps to reproduce the vulnerability.</xs:documentation>
                                </xs:annotation>
                        </xs:element>
                        <xs:element name="environment" type="xs:string" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>A description of the environment in which reproduction was possible.</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="supportingMaterial" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>Supporting material that helps in reproducing or understanding how reproduction is possible. This may include screenshots, payloads, and PoC exploit code.</xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="attachment" type="bom:attachedTextType" minOccurs="0" maxOccurs="unbounded" />
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="advisories" minOccurs="0" maxOccurs="1">
                <xs:complexType>
                    <xs:annotation>
                        <xs:documentation xml:lang="en">
                            Published advisories of the vulnerability if provided.
                        </xs:documentation>
                    </xs:annotation>
                    <xs:sequence>
                        <xs:element name="advisory" type="bom:advisoryType" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="created" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The date and time (timestamp) when the vulnerability record was created in the vulnerability database.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="published" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The date and time (timestamp) when the vulnerability record was first published.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="updated" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The date and time (timestamp) when the vulnerability record was last updated.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="rejected" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The date and time (timestamp) when the vulnerability record was rejected (if applicable).</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="credits" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Individuals or organizations credited with the discovery of the vulnerability.</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="organizations" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>The organizations credited with vulnerability discovery.</xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence minOccurs="0" maxOccurs="unbounded">
                                    <xs:element name="organization" type="bom:organizationalEntity"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="individuals" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>The individuals, not associated with organizations, that are credited with vulnerability discovery.</xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence minOccurs="0" maxOccurs="unbounded">
                                    <xs:element name="individual" type="bom:organizationalContact"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="tools" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The tool(s) used to identify, confirm, or score the vulnerability.</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:choice>
                        <xs:sequence minOccurs="0" maxOccurs="unbounded">
                            <xs:element name="tool" minOccurs="0" type="bom:toolType">
                                <xs:annotation>
                                    <xs:documentation>DEPRECATED. Use tools\components or tools\services instead.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                        <xs:sequence minOccurs="0" maxOccurs="1">
                            <xs:element name="components" type="bom:componentsType" minOccurs="0" maxOccurs="1">
                                <xs:annotation>
                                    <xs:documentation>A list of software and hardware components used as tools.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                            <xs:element name="services" type="bom:servicesType" minOccurs="0" maxOccurs="1">
                                <xs:annotation>
                                    <xs:documentation>A list of services used as tools.</xs:documentation>
                                </xs:annotation>
                            </xs:element>
                        </xs:sequence>
                    </xs:choice>
                </xs:complexType>
            </xs:element>
            <xs:element name="analysis" minOccurs="0" maxOccurs="1">
                <xs:complexType>
                    <xs:annotation>
                        <xs:documentation xml:lang="en">
                            An assessment of the impact and exploitability of the vulnerability.
                        </xs:documentation>
                    </xs:annotation>
                    <xs:sequence minOccurs="0" maxOccurs="1">
                        <xs:element name="state" type="bom:impactAnalysisStateType" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation xml:lang="en">
                                    Declares the current state of an occurrence of a vulnerability, after automated or manual analysis.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="justification" type="bom:impactAnalysisJustificationType" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation xml:lang="en">
                                    The rationale of why the impact analysis state was asserted.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="responses" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>A response to the vulnerability by the manufacturer, supplier, or
                                    project responsible for the affected component or service. More than one response
                                    is allowed. Responses are strongly encouraged for vulnerabilities where the analysis
                                    state is exploitable.</xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence minOccurs="0" maxOccurs="unbounded">
                                    <xs:element name="response" type="bom:impactAnalysisResponsesType"/>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="detail" type="xs:string" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation xml:lang="en">
                                    Detailed description of the impact including methods used during assessment.
                                    If a vulnerability is not exploitable, this field should include specific details
                                    on why the component or service is not impacted by this vulnerability.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="firstIssued" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation xml:lang="en">
                                    The date and time (timestamp) when the analysis was first issued.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="lastUpdated" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation xml:lang="en">
                                    The date and time (timestamp) when the analysis was last updated.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="affects" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The components or services that are affected by the vulnerability.</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence minOccurs="0" maxOccurs="unbounded">
                        <xs:element name="target">
                            <xs:complexType>
                                <xs:sequence minOccurs="0" maxOccurs="1">
                                    <xs:element name="ref" minOccurs="1" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>References a component or service by the objects bom-ref.</xs:documentation>
                                        </xs:annotation>
                                        <xs:simpleType>
                                            <xs:union memberTypes="bom:refLinkType bom:bomLinkElementType"/>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="versions" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>Zero or more individual versions or range of versions.</xs:documentation>
                                        </xs:annotation>
                                        <xs:complexType>
                                            <xs:sequence minOccurs="0" maxOccurs="unbounded">
                                                <xs:element name="version">
                                                    <xs:complexType>
                                                        <xs:sequence minOccurs="0" maxOccurs="1">
                                                            <xs:choice>
                                                                <xs:element name="version" type="bom:versionType" minOccurs="1" maxOccurs="1">
                                                                    <xs:annotation>
                                                                        <xs:documentation>A single version of a component or service.</xs:documentation>
                                                                    </xs:annotation>
                                                                </xs:element>
                                                                <xs:element name="range" type="bom:versionRangeType" minOccurs="1" maxOccurs="1">
                                                                    <xs:annotation>
                                                                        <xs:documentation>A version range specified in Package URL Version Range syntax (vers) which is defined at https://github.com/package-url/purl-spec/VERSION-RANGE-SPEC.rst</xs:documentation>
                                                                    </xs:annotation>
                                                                </xs:element>
                                                            </xs:choice>
                                                            <xs:element name="status" type="bom:impactAnalysisAffectedStatusType" minOccurs="0" maxOccurs="1" default="affected">
                                                                <xs:annotation>
                                                                    <xs:documentation>
                                                                        The vulnerability status for the version or range of versions.
                                                                    </xs:documentation>
                                                                </xs:annotation>
                                                            </xs:element>
                                                        </xs:sequence>
                                                    </xs:complexType>
                                                </xs:element>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="properties" type="bom:propertiesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Provides the ability to document properties in a name/value store.
                        This provides flexibility to include data not officially supported in the standard
                        without having to use additional namespaces or create extensions. Property names
                        of interest to the general public are encouraged to be registered in the
                        CycloneDX Property Taxonomy - https://github.com/CycloneDX/cyclonedx-property-taxonomy.
                        Formal registration is optional.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="bom-ref" type="bom:refType">
            <xs:annotation>
                <xs:documentation>
                    An optional identifier which can be used to reference the vulnerability elsewhere in the BOM.
                    Uniqueness is enforced within all elements and children of the root-level bom element.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
    </xs:complexType>

    <xs:complexType name="vulnerabilitySourceType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
            <xs:element name="name" type="xs:normalizedString" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The name of the source.
                        For example: NVD, National Vulnerability Database, OSS Index, VulnDB, and GitHub Advisories
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="url" type="xs:anyURI" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The url of the vulnerability documentation as provided by the source.
                    For example: https://nvd.nist.gov/vuln/detail/CVE-2021-39182</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="ratingType">
        <xs:sequence>
            <xs:element name="source" type="bom:vulnerabilitySourceType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The source that calculated the severity or risk rating of the vulnerability.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="score" type="xs:decimal" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The numerical score of the rating.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="severity" type="bom:severityType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Textual representation of the severity that corresponds to the numerical score of the rating.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="method" type="bom:scoreSourceType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The risk scoring methodology/standard used.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="vector" type="xs:normalizedString" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Textual representation of the metric values used to score the vulnerability.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="justification" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>An optional reason for rating the vulnerability as it was.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="advisoryType">
        <xs:sequence>
            <xs:element name="title" type="xs:normalizedString" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>An optional name of the advisory.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="url" type="xs:anyURI" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Location where the advisory can be obtained.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="annotationsType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
            <xs:element name="annotation" type="bom:annotationType"/>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="annotatorChoiceType">
        <xs:choice>
            <xs:element name="organization" type="bom:organizationalEntity" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The organization that created the annotation</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="individual" type="bom:organizationalContact" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The person that created the annotation</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="component" type="bom:component" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The tool or component that created the annotation</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="service" type="bom:service" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The service that created the annotation</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:choice>
    </xs:complexType>

    <xs:complexType name="annotationType">
        <xs:sequence>
            <xs:element name="subjects" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The objects in the BOM identified by their bom-ref's. This is often components or services, but may be any object type supporting bom-refs.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence minOccurs="0" maxOccurs="unbounded">
                        <xs:element name="subject" type="bom:bomReferenceType"/>
                        <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>
                                    Allows any undeclared elements as long as the elements are placed in a different namespace.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:any>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="annotator" type="bom:annotatorChoiceType" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The organization, individual, component, or service which created the textual content
                        of the annotation.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="timestamp" type="xs:dateTime" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The date and time (timestamp) when the annotation was created.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="text" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The textual content of the annotation.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:attribute name="bom-ref" type="bom:refType">
            <xs:annotation>
                <xs:documentation>
                    An optional identifier which can be used to reference the annotation elsewhere in the BOM.
                    Uniqueness is enforced within all elements and children of the root-level bom element.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:simpleType name="severityType" final="restriction">
        <xs:annotation>
            <xs:documentation xml:lang="en">
                Textual representation of the severity of the vulnerability adopted by the analysis method. If the
                analysis method uses values other than what is provided, the user is expected to translate appropriately.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="critical">
                <xs:annotation>
                    <xs:documentation>Critical severity</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="high">
                <xs:annotation>
                    <xs:documentation>High severity</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="medium">
                <xs:annotation>
                    <xs:documentation>Medium severity</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="low">
                <xs:annotation>
                    <xs:documentation>Low severity</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="info">
                <xs:annotation>
                    <xs:documentation>Informational warning.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="none">
                <xs:annotation>
                    <xs:documentation>None</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="unknown">
                <xs:annotation>
                    <xs:documentation>The severity is not known</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="impactAnalysisStateType" final="restriction">
        <xs:annotation>
            <xs:documentation xml:lang="en">
                Declares the current state of an occurrence of a vulnerability, after automated or manual analysis.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="resolved">
                <xs:annotation>
                    <xs:documentation>
                        The vulnerability has been remediated.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="resolved_with_pedigree">
                <xs:annotation>
                    <xs:documentation>
                        The vulnerability has been remediated and evidence of the changes are provided in the affected
                        components pedigree containing verifiable commit history and/or diff(s).
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="exploitable">
                <xs:annotation>
                    <xs:documentation>
                        The vulnerability may be directly or indirectly exploitable.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="in_triage">
                <xs:annotation>
                    <xs:documentation>
                        The vulnerability is being investigated.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="false_positive">
                <xs:annotation>
                    <xs:documentation>
                        The vulnerability is not specific to the component or service and was falsely identified or associated.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="not_affected">
                <xs:annotation>
                    <xs:documentation>
                        The component or service is not affected by the vulnerability. Justification should be specified
                        for all not_affected cases.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="impactAnalysisJustificationType" final="restriction">
        <xs:annotation>
            <xs:documentation xml:lang="en">
                The rationale of why the impact analysis state was asserted.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="code_not_present">
                <xs:annotation>
                    <xs:documentation>
                        The code has been removed or tree-shaked.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="code_not_reachable">
                <xs:annotation>
                    <xs:documentation>
                        The vulnerable code is not invoked at runtime.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="requires_configuration">
                <xs:annotation>
                    <xs:documentation>
                        Exploitability requires a configurable option to be set/unset.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="requires_dependency">
                <xs:annotation>
                    <xs:documentation>
                        Exploitability requires a dependency that is not present.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="requires_environment">
                <xs:annotation>
                    <xs:documentation>
                        Exploitability requires a certain environment which is not present.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="protected_by_compiler">
                <xs:annotation>
                    <xs:documentation>
                        Exploitability requires a compiler flag to be set/unset.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="protected_at_runtime">
                <xs:annotation>
                    <xs:documentation>
                        Exploits are prevented at runtime.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="protected_at_perimeter">
                <xs:annotation>
                    <xs:documentation>
                        Attacks are blocked at physical, logical, or network perimeter.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="protected_by_mitigating_control">
                <xs:annotation>
                    <xs:documentation>
                        Preventative measures have been implemented that reduce the likelihood and/or impact of the vulnerability.
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="scoreSourceType" final="restriction">
        <xs:annotation>
            <xs:documentation xml:lang="en">
                Specifies the severity or risk scoring methodology or standard used.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="CVSSv2">
                <xs:annotation>
                    <xs:documentation xml:lang="en">
                        Common Vulnerability Scoring System v2.0 standard as defined at https://www.first.org/cvss/v2/
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CVSSv3">
                <xs:annotation>
                    <xs:documentation xml:lang="en">
                        Common Vulnerability Scoring System v3.0 standard as defined at https://www.first.org/cvss/v3-0/
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CVSSv31">
                <xs:annotation>
                    <xs:documentation xml:lang="en">
                        Common Vulnerability Scoring System v3.1 standard as defined at https://www.first.org/cvss/v3-1/
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="CVSSv4">
                <xs:annotation>
                    <xs:documentation xml:lang="en">
                        Common Vulnerability Scoring System v4.0 standard as defined at https://www.first.org/cvss/v4-0/
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="OWASP">
                <xs:annotation>
                    <xs:documentation xml:lang="en">
                        OWASP Risk Rating as defined at https://owasp.org/www-community/OWASP_Risk_Rating_Methodology
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="SSVC">
                <xs:annotation>
                    <xs:documentation xml:lang="en">
                        Stakeholder Specific Vulnerability Categorization as defined at https://github.com/CERTCC/SSVC
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="other">
                <xs:annotation>
                    <xs:documentation xml:lang="en">
                        Another severity or risk scoring methodology
                    </xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="impactAnalysisResponsesType" final="restriction">
        <xs:annotation>
            <xs:documentation xml:lang="en">
                The rationale of why the impact analysis state was asserted.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="can_not_fix">
                <xs:annotation>
                    <xs:documentation>Can not fix</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="will_not_fix">
                <xs:annotation>
                    <xs:documentation>Will not fix</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="update">
                <xs:annotation>
                    <xs:documentation>Update to a different revision or release</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="rollback">
                <xs:annotation>
                    <xs:documentation>Revert to a previous revision or release</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="workaround_available">
                <xs:annotation>
                    <xs:documentation>There is a workaround available</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:simpleType name="impactAnalysisAffectedStatusType" final="restriction">
        <xs:annotation>
            <xs:documentation xml:lang="en">
                The vulnerability status of a given version or range of versions of a product. The statuses
                'affected' and 'unaffected' indicate that the version is affected or unaffected by the vulnerability.
                The status 'unknown' indicates that it is unknown or unspecified whether the given version is affected.
                There can be many reasons for an 'unknown' status, including that an investigation has not been
                undertaken or that a vendor has not disclosed the status.
            </xs:documentation>
        </xs:annotation>
        <xs:restriction base="xs:string">
            <xs:enumeration value="affected">
                <xs:annotation>
                    <xs:documentation>The version is affected by the vulnerability.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="unaffected">
                <xs:annotation>
                    <xs:documentation>The version is not affected by the vulnerability.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="unknown">
                <xs:annotation>
                    <xs:documentation>It is unknown (or unspecified) whether the given version is affected.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="formulationType">
        <xs:annotation>
            <xs:documentation>
                Describes how a component or service was manufactured or deployed. This is achieved through the use
                of formulas, workflows, tasks, and steps, which declare the precise steps to reproduce along with the
                observed formulas describing the steps which transpired in the manufacturing process.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
            <xs:element name="formula" type="bom:formulaType"/>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="formulaType">
        <xs:annotation>
            <xs:documentation>
                Describes workflows and resources that captures rules and other aspects of how the associated
                BOM component or service was formed.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="components" type="bom:componentsType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Transient components that are used in tasks that constitute one or more of
                        this formula's workflows</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="services" type="bom:servicesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Transient services that are used in tasks that constitute one or more of
                        this formula's workflows</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="workflows" type="bom:workflowsType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>List of workflows that can be declared to accomplish specific orchestrated goals
                        and independently triggered.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="properties" type="bom:propertiesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Provides the ability to document properties in a name/value store.
                        This provides flexibility to include data not officially supported in the standard
                        without having to use additional namespaces or create extensions. Property names
                        of interest to the general public are encouraged to be registered in the
                        CycloneDX Property Taxonomy - https://github.com/CycloneDX/cyclonedx-property-taxonomy.
                        Formal registration is optional.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
        <xs:attribute name="bom-ref" type="bom:refType">
            <xs:annotation>
                <xs:documentation>
                    An optional identifier which can be used to reference the formula elsewhere in the BOM.
                    Uniqueness is enforced within all elements and children of the root-level bom element.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="workflowsType">
        <xs:sequence>
            <xs:element name="workflow" type="bom:workflowType" minOccurs="0" maxOccurs="unbounded"/>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="workflowType">
        <xs:sequence>
            <xs:element name="uid" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The unique identifier for the resource instance within its deployment context.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The name of the resource instance.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="description" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The description of the resource instance.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="resourceReferences" type="bom:resourceReferencesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>References to component or service resources that are used to realize
                        the resource instance.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="tasks" type="bom:tasksType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The tasks that comprise the workflow.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="taskDependencies" type="bom:dependenciesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The graph of dependencies between tasks within the workflow.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="taskTypes" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Indicates the types of activities performed by the set of workflow tasks.</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="taskType" type="bom:taskTypeEnum" minOccurs="0" maxOccurs="unbounded" />
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="trigger" type="bom:triggerType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>The trigger that initiated the task.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="steps" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The sequence of steps for the task.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="step" type="bom:stepType" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="inputs" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Represents resources and data brought into a task at runtime by executor
                        or task commands</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="input" type="bom:inputType" minOccurs="0" maxOccurs="unbounded" />
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="outputs" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Represents resources and data output from a task at runtime by executor
                        or task commands</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="output" type="bom:outputType" minOccurs="0" maxOccurs="unbounded" />
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="timeStart" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The date and time (timestamp) when the task started.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="timeEnd" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The date and time (timestamp) when the task ended.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="workspaces" type="bom:workspacesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>A set of named filesystem or data resource shareable by workflow tasks.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="runtimeTopology" type="bom:dependenciesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>A graph of the component runtime topology for workflow's instance.
                        A description of the runtime component and service topology.  This can describe a partial or
                        complete topology used to host and execute the task (e.g., hardware, operating systems,
                        configurations, etc.)</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="properties" type="bom:propertiesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Provides the ability to document properties in a name/value store.
                        This provides flexibility to include data not officially supported in the standard
                        without having to use additional namespaces or create extensions. Property names
                        of interest to the general public are encouraged to be registered in the
                        CycloneDX Property Taxonomy - https://github.com/CycloneDX/cyclonedx-property-taxonomy.
                        Formal registration is optional.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:attribute name="bom-ref" type="bom:refType" use="required">
            <xs:annotation>
                <xs:documentation>
                    An optional identifier which can be used to reference the workflow elsewhere in the BOM.
                    Uniqueness is enforced within all elements and children of the root-level bom element.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="resourceReferencesType">
        <xs:sequence>
            <xs:element name="resourceReference" type="bom:resourceReferenceType" minOccurs="0" maxOccurs="unbounded" />
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="resourceReferenceType">
        <xs:sequence>
            <xs:choice>
                <xs:element name="ref" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>
                            References an object by its bom-ref attribute
                        </xs:documentation>
                    </xs:annotation>
                    <xs:simpleType>
                        <xs:union memberTypes="bom:refLinkType bom:bomLinkElementType"/>
                    </xs:simpleType>
                </xs:element>
                <xs:element name="externalReference" type="bom:externalReference" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>
                            Reference to an externally accessible resource.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:choice>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="tasksType">
        <xs:sequence>
            <xs:element name="task" type="bom:taskType" minOccurs="0" maxOccurs="unbounded" />
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="taskType">
        <xs:sequence>
            <xs:element name="uid" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The unique identifier for the resource instance within its deployment context.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The name of the resource instance.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="description" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The description of the resource instance.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="resourceReferences" type="bom:resourceReferencesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        References to component or service resources that are used to realize the resource instance.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="taskTypes" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Indicates the types of activities performed by the set of workflow tasks.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="taskType" type="bom:taskTypeEnum" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="trigger" type="bom:triggerType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The trigger that initiated the task.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="steps" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The sequence of steps for the task.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="step" type="bom:stepType" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="inputs" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Represents resources and data brought into a task at runtime by executor or task commands.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="input" type="bom:inputType" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="outputs" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Represents resources and data output from a task at runtime by executor or task commands
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="output" type="bom:outputType" minOccurs="0" maxOccurs="unbounded"/>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="timeStart" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The date and time (timestamp) when the task started.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="timeEnd" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The date and time (timestamp) when the task ended.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="workspaces" type="bom:workspacesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        A set of named filesystem or data resource shareable by workflow tasks.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="runtimeTopology" type="bom:dependenciesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        A graph of the component runtime topology for task's instance.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="properties" type="bom:propertiesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Provides the ability to document properties in a name/value store.
                        This provides flexibility to include data not officially supported in the standard
                        without having to use additional namespaces or create extensions. Property names
                        of interest to the general public are encouraged to be registered in the
                        CycloneDX Property Taxonomy - https://github.com/CycloneDX/cyclonedx-property-taxonomy.
                        Formal registration is optional.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:attribute name="bom-ref" type="bom:refType" use="required">
            <xs:annotation>
                <xs:documentation>
                    An optional identifier which can be used to reference the task elsewhere in the BOM.
                    Uniqueness is enforced within all elements and children of the root-level bom element.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:simpleType name="taskTypeEnum">
        <xs:restriction base="xs:string">
            <xs:enumeration value="copy">
                <xs:annotation>
                    <xs:documentation>A task that copies software or data used to accomplish other tasks in the workflow.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="clone">
                <xs:annotation>
                    <xs:documentation>A task that clones a software repository into the workflow in order to retrieve its source code or data for use in a build step.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="lint">
                <xs:annotation>
                    <xs:documentation>A task that checks source code for programmatic and stylistic errors.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="scan">
                <xs:annotation>
                    <xs:documentation>A task that performs a scan against source code, or built or deployed components and services. Scans are typically run to gather or test for security vulnerabilities or policy compliance.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="merge">
                <xs:annotation>
                    <xs:documentation>A task that merges changes or fixes into source code prior to a build step in the workflow.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="build">
                <xs:annotation>
                    <xs:documentation>A task that builds the source code, dependencies and/or data into an artifact that can be deployed to and executed on target systems.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="test">
                <xs:annotation>
                    <xs:documentation>A task that verifies the functionality of a component or service.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="deliver">
                <xs:annotation>
                    <xs:documentation>A task that delivers a built artifact to one or more target repositories or storage systems.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="deploy">
                <xs:annotation>
                    <xs:documentation>A task that deploys a built artifact for execution on one or more target systems.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="release">
                <xs:annotation>
                    <xs:documentation>A task that releases a built, versioned artifact to a target repository or distribution system.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="clean">
                <xs:annotation>
                    <xs:documentation>A task that cleans unnecessary tools, build artifacts and/or data from workflow storage.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
            <xs:enumeration value="other">
                <xs:annotation>
                    <xs:documentation>A workflow task that does not match current task type definitions.</xs:documentation>
                </xs:annotation>
            </xs:enumeration>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="workspacesType">
        <xs:sequence>
            <xs:element name="workspace" type="bom:workspaceType" minOccurs="0" maxOccurs="unbounded"/>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="workspaceType">
        <xs:annotation>
            <xs:documentation>
                A named filesystem or data resource shareable by workflow tasks.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
            <xs:element name="uid" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The unique identifier for the resource instance within its deployment context.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The name of the resource instance.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="aliases" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The names for the workspace as referenced by other workflow tasks. Effectively, a name mapping
                        so other tasks can use their own local name in their steps.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="alias" type="xs:string" minOccurs="0" maxOccurs="unbounded" />
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="description" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The description of the resource instance.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="resourceReferences" type="bom:resourceReferencesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        References to component or service resources that are used to realize the resource instance.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="accessMode" type="bom:accessModeEnum" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Describes the read-write access control for the workspace relative to the owning resource instance.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="mountPath" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        A path to a location on disk where the workspace will be available to the associated task's steps.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="managedDataType" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The name of a domain-specific data type the workspace represents. This property is for CI/CD
                        frameworks that are able to provide access to structured, managed data at a more granular level
                        than a filesystem.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="volumeRequest" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Identifies the reference to the request for a specific volume type and parameters.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="volume" type="bom:volumeType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Information about the actual volume instance allocated to the workspace.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="properties" type="bom:propertiesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Provides the ability to document properties in a name/value store.
                        This provides flexibility to include data not officially supported in the standard
                        without having to use additional namespaces or create extensions. Property names
                        of interest to the general public are encouraged to be registered in the
                        CycloneDX Property Taxonomy - https://github.com/CycloneDX/cyclonedx-property-taxonomy.
                        Formal registration is optional.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:attribute name="bom-ref" type="bom:refType" use="required">
            <xs:annotation>
                <xs:documentation>
                    An optional identifier which can be used to reference the workflow elsewhere in the BOM.
                    Uniqueness is enforced within all elements and children of the root-level bom element.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:simpleType name="accessModeEnum">
        <xs:restriction base="xs:string">
            <xs:enumeration value="read-only"/>
            <xs:enumeration value="read-write"/>
            <xs:enumeration value="read-write-once"/>
            <xs:enumeration value="write-once"/>
            <xs:enumeration value="write-only"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="volumeType">
        <xs:annotation>
            <xs:documentation>
                An identifiable, logical unit of data storage tied to a physical device.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="uid" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The unique identifier for the volume instance within its deployment context.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The name of the volume instance
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="mode" type="bom:volumeModeEnum" minOccurs="0" maxOccurs="1" default="filesystem">
                <xs:annotation>
                    <xs:documentation>
                        The mode for the volume instance.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="path" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The underlying path created from the actual volume.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="sizeAllocated" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The allocated size of the volume accessible to the associated workspace. This should include
                        the scalar size as well as IEC standard unit in either decimal or binary form.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="persistent" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Indicates if the volume persists beyond the life of the resource it is associated with.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="remote" type="xs:boolean" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Indicates if the volume is remotely (i.e., network) attached.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="properties" type="bom:propertiesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Provides the ability to document properties in a name/value store.
                        This provides flexibility to include data not officially supported in the standard
                        without having to use additional namespaces or create extensions. Property names
                        of interest to the general public are encouraged to be registered in the
                        CycloneDX Property Taxonomy - https://github.com/CycloneDX/cyclonedx-property-taxonomy.
                        Formal registration is optional.</xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:simpleType name="volumeModeEnum">
        <xs:restriction base="xs:string">
            <xs:enumeration value="filesystem"/>
            <xs:enumeration value="block"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="stepType">
        <xs:annotation>
            <xs:documentation>
                Executes specific commands or tools in order to accomplish its owning task as part of a sequence.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        A name for the step.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="description" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        A description of the step.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="commands" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Ordered list of commands or directives for the step
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="command" minOccurs="0" maxOccurs="unbounded">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="executed" type="xs:string" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                A text representation of the executed command.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="properties" type="bom:propertiesType" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>Provides the ability to document properties in a name/value store.
                                                This provides flexibility to include data not officially supported in the standard
                                                without having to use additional namespaces or create extensions. Property names
                                                of interest to the general public are encouraged to be registered in the
                                                CycloneDX Property Taxonomy - https://github.com/CycloneDX/cyclonedx-property-taxonomy.
                                                Formal registration is optional.</xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="properties" type="bom:propertiesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Provides the ability to document properties in a name/value store.
                        This provides flexibility to include data not officially supported in the standard
                        without having to use additional namespaces or create extensions. Property names
                        of interest to the general public are encouraged to be registered in the
                        CycloneDX Property Taxonomy - https://github.com/CycloneDX/cyclonedx-property-taxonomy.
                        Formal registration is optional.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="triggerType">
        <xs:sequence>
            <xs:element name="uid" type="xs:string" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The unique identifier for the resource instance within its deployment context.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The name of the resource instance.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="description" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The description of the resource instance.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="resourceReferences" type="bom:resourceReferencesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        References to component or service resources that are used to realize the resource instance.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="type" type="bom:triggerTypeType" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The source type of event which caused the trigger to fire.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="event" type="bom:eventType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The event data that caused the associated trigger to activate.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="conditions" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>A list of conditions used to determine if a trigger should be activated.</xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="condition" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>
                                    A condition that was used to determine a trigger should be activated.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="description" type="xs:string" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Describes the set of conditions which cause the trigger to activate.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="expression" type="xs:string" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The logical expression that was evaluated that determined the trigger should be fired.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="properties" type="bom:propertiesType" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>Provides the ability to document properties in a name/value store.
                                                This provides flexibility to include data not officially supported in the standard
                                                without having to use additional namespaces or create extensions. Property names
                                                of interest to the general public are encouraged to be registered in the
                                                CycloneDX Property Taxonomy - https://github.com/CycloneDX/cyclonedx-property-taxonomy.
                                                Formal registration is optional.</xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="timeActivated" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The date and time (timestamp) when the trigger was activated.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="inputs" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Represents resources and data brought into a task at runtime by executor or task commands
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="input" type="bom:inputType" minOccurs="0" maxOccurs="unbounded" />
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="outputs" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Represents resources and data output from a task at runtime by executor or task commands
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="output" type="bom:outputType" minOccurs="0" maxOccurs="unbounded" />
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="properties" type="bom:propertiesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Provides the ability to document properties in a name/value store.
                        This provides flexibility to include data not officially supported in the standard
                        without having to use additional namespaces or create extensions. Property names
                        of interest to the general public are encouraged to be registered in the
                        CycloneDX Property Taxonomy - https://github.com/CycloneDX/cyclonedx-property-taxonomy.
                        Formal registration is optional.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:attribute name="bom-ref" type="bom:refType" use="required">
            <xs:annotation>
                <xs:documentation>
                    An optional identifier which can be used to reference the trigger elsewhere in the BOM.
                    Uniqueness is enforced within all elements and children of the root-level bom element.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:simpleType name="triggerTypeType">
        <xs:restriction base="xs:string">
            <xs:enumeration value="manual"/>
            <xs:enumeration value="api"/>
            <xs:enumeration value="webhook"/>
            <xs:enumeration value="scheduled"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="eventType">
        <xs:sequence>
            <xs:element name="uid" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The unique identifier of the event.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="description" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        A description of the event.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="timeReceived" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The date and time (timestamp) when the event was received.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="data" type="bom:attachedTextType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Encoding of the raw event data.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="source" type="bom:resourceReferenceType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        References the component or service that was the source of the event
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="target" type="bom:resourceReferenceType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        References the component or service that was the target of the event
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="properties" type="bom:propertiesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Provides the ability to document properties in a name/value store.
                        This provides flexibility to include data not officially supported in the standard
                        without having to use additional namespaces or create extensions. Property names
                        of interest to the general public are encouraged to be registered in the
                        CycloneDX Property Taxonomy - https://github.com/CycloneDX/cyclonedx-property-taxonomy.
                        Formal registration is optional.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="inputType">
        <xs:annotation>
            <xs:documentation>
                Type that represents various input data types and formats.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:choice>
                <xs:element name="resource" type="bom:resourceReferenceType" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>
                            A reference to an independent resource provided as an input to a task by the workflow runtime.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="parameters" type="bom:parametersType" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>
                            Inputs that have the form of parameters with names and values.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="environmentVars" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>
                            Inputs that have the form of parameters with names and values.
                        </xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence minOccurs="0" maxOccurs="unbounded">
                            <!-- maxOccurs="unbounded" NEEDS to be set on the sequence, not the individual elements -->
                            <xs:choice>
                                <xs:element name="environmentVar" type="bom:propertyType" minOccurs="0" maxOccurs="1"/>
                                <xs:element name="value" type="xs:string" minOccurs="0" maxOccurs="1"/>
                            </xs:choice>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="data" type="bom:attachedTextType" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>
                            Inputs that have the form of data.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:choice>
            <xs:element name="source" type="bom:resourceReferenceType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        A references to the component or service that provided the input to the task
                        (e.g., reference to a service with data flow value of inbound)
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="target" type="bom:resourceReferenceType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        A reference to the component or service that received or stored the input if not the task
                        itself (e.g., a local, named storage workspace)
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="properties" type="bom:propertiesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Provides the ability to document properties in a name/value store.
                        This provides flexibility to include data not officially supported in the standard
                        without having to use additional namespaces or create extensions. Property names
                        of interest to the general public are encouraged to be registered in the
                        CycloneDX Property Taxonomy - https://github.com/CycloneDX/cyclonedx-property-taxonomy.
                        Formal registration is optional.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="outputType">
        <xs:annotation>
            <xs:documentation>
                Represents resources and data output from a task at runtime by executor or task commands
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:choice>
                <xs:element name="resource" type="bom:resourceReferenceType" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>
                            A reference to an independent resource generated as output by the task.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="environmentVars" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>
                            Outputs that have the form of environment variables.
                        </xs:documentation>
                    </xs:annotation>
                    <xs:complexType>
                        <xs:sequence minOccurs="0" maxOccurs="unbounded">
                            <!-- maxOccurs="unbounded" NEEDS to be set on the sequence, not the individual elements -->
                            <xs:choice>
                                <xs:element name="environmentVar" type="bom:propertyType" minOccurs="0" maxOccurs="1"/>
                                <xs:element name="value" type="xs:string" minOccurs="0" maxOccurs="1"/>
                            </xs:choice>
                        </xs:sequence>
                    </xs:complexType>
                </xs:element>
                <xs:element name="data" type="bom:attachedTextType" minOccurs="1" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>
                            Outputs that have the form of data.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
            </xs:choice>
            <xs:element name="type" type="bom:outputTypeEnum" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Describes the type of data output.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="source" type="bom:resourceReferenceType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Component or service that generated or provided the output from the task (e.g., a build tool)
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="target" type="bom:resourceReferenceType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Component or service that received the output from the task
                        (e.g., reference to an artifactory service with data flow value of outbound)
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="properties" type="bom:propertiesType" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Provides the ability to document properties in a name/value store.
                        This provides flexibility to include data not officially supported in the standard
                        without having to use additional namespaces or create extensions. Property names
                        of interest to the general public are encouraged to be registered in the
                        CycloneDX Property Taxonomy - https://github.com/CycloneDX/cyclonedx-property-taxonomy.
                        Formal registration is optional.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:simpleType name="outputTypeEnum">
        <xs:restriction base="xs:string">
            <xs:enumeration value="artifact"/>
            <xs:enumeration value="attestation"/>
            <xs:enumeration value="log"/>
            <xs:enumeration value="evidence"/>
            <xs:enumeration value="metrics"/>
            <xs:enumeration value="other"/>
        </xs:restriction>
    </xs:simpleType>

    <xs:complexType name="parametersType">
        <xs:sequence>
            <xs:element name="parameter" type="bom:parameterType" minOccurs="0" maxOccurs="unbounded" />
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="parameterType">
        <xs:annotation>
            <xs:documentation>
                A representation of a functional parameter.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The name of the parameter.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="value" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The value of the parameter.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="dataType" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The data type of the parameter.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="cryptoPropertiesType">
        <xs:annotation>
            <xs:documentation>
                Cryptographic assets have properties that uniquely define them and that make them actionable for
                further reasoning. As an example, it makes a difference if one knows the algorithm family (e.g. AES)
                or the specific variant or instantiation (e.g. AES-128-GCM). This is because the security level and the
                algorithm primitive (authenticated encryption) is only defined by the definition of the algorithm variant.
                The presence of a weak cryptographic algorithm like SHA1 vs. HMAC-SHA1 also makes a difference.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="assetType" minOccurs="1" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Cryptographic assets occur in several forms. Algorithms and protocols are most commonly
                        implemented in specialized cryptographic libraries. They may however also be 'hardcoded'
                        in software components. Certificates and related cryptographic material like keys, tokens,
                        secrets or passwords are other cryptographic assets to be modelled.
                    </xs:documentation>
                </xs:annotation>
                <xs:simpleType>
                    <xs:restriction base="xs:string">
                        <xs:enumeration value="algorithm">
                            <xs:annotation>
                                <xs:documentation>
                                    Mathematical function commonly used for data encryption, authentication, and
                                    digital signatures.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:enumeration>
                        <xs:enumeration value="certificate">
                            <xs:annotation>
                                <xs:documentation>
                                    An electronic document that is used to provide the identity or validate a public key.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:enumeration>
                        <xs:enumeration value="protocol">
                            <xs:annotation>
                                <xs:documentation>
                                    A set of rules and guidelines that govern the behavior and communication with each other.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:enumeration>
                        <xs:enumeration value="related-crypto-material">
                            <xs:annotation>
                                <xs:documentation>
                                    Other cryptographic assets that are related to algorithms, certificate, and protocols
                                    such as keys and tokens.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:enumeration>
                    </xs:restriction>
                </xs:simpleType>
            </xs:element>
            <xs:element name="algorithmProperties" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Additional properties specific to a cryptographic algorithm.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="primitive" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    Cryptographic building blocks used in higher-level cryptographic systems and
                                    protocols. Primitives represent different cryptographic routines: deterministic
                                    random bit generators (drbg, e.g. CTR_DRBG from NIST SP800-90A-r1), message
                                    authentication codes (mac, e.g. HMAC-SHA-256), blockciphers (e.g. AES),
                                    streamciphers (e.g. Salsa20), signatures (e.g. ECDSA), hash functions (e.g. SHA-256),
                                    public-key encryption schemes (pke, e.g. RSA), extended output functions
                                    (xof, e.g. SHAKE256), key derivation functions (e.g. pbkdf2), key agreement
                                    algorithms (e.g. ECDH), key encapsulation mechanisms (e.g. ML-KEM), authenticated
                                    encryption (ae, e.g. AES-GCM) and the combination of multiple algorithms
                                    (combiner, e.g. SP800-56Cr2).
                                </xs:documentation>
                            </xs:annotation>
                            <xs:simpleType>
                                <xs:restriction base="xs:string">
                                    <xs:enumeration value="drbg">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Deterministic Random Bit Generator (DRBG) is a type of pseudorandom
                                                number generator designed to produce a sequence of bits from an initial
                                                seed value. DRBGs are commonly used in cryptographic applications where
                                                reproducibility of random values is important.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="mac">
                                        <xs:annotation>
                                            <xs:documentation>
                                                In cryptography, a Message Authentication Code (MAC) is information
                                                used for authenticating and integrity-checking a message.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="block-cipher">
                                        <xs:annotation>
                                            <xs:documentation>
                                                A block cipher is a symmetric key algorithm that operates on fixed-size
                                                blocks of data. It encrypts or decrypts the data in block units,
                                                providing confidentiality. Block ciphers are widely used in various
                                                cryptographic modes and protocols for secure data transmission.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="stream-cipher">
                                        <xs:annotation>
                                            <xs:documentation>
                                                A stream cipher is a symmetric key cipher where plaintext digits are
                                                combined with a pseudorandom cipher digit stream (keystream).
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="signature">
                                        <xs:annotation>
                                            <xs:documentation>
                                                In cryptography, a signature is a digital representation of a message
                                                or data that proves its origin, identity, and integrity. Digital
                                                signatures are generated using cryptographic algorithms and are widely
                                                used for authentication and verification in secure communication.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="hash">
                                        <xs:annotation>
                                            <xs:documentation>
                                                A hash function is a mathematical algorithm that takes an input
                                                (or 'message') and produces a fixed-size string of characters, which is
                                                typically a hash value. Hash functions are commonly used in various
                                                cryptographic applications, including data integrity verification and
                                                password hashing.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="pke">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Public Key Encryption (PKE) is a type of encryption that uses a pair of
                                                public and private keys for secure communication. The public key is used
                                                for encryption, while the private key is used for decryption. PKE is a
                                                fundamental component of public-key cryptography.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="xof">
                                        <xs:annotation>
                                            <xs:documentation>
                                                An XOF is an extendable output function that can take arbitrary input
                                                and creates a stream of output, up to a limit determined by the size of
                                                the internal state of the hash function that underlies the XOF.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="kdf">
                                        <xs:annotation>
                                            <xs:documentation>
                                                A Key Derivation Function (KDF) derives key material from another source
                                                of entropy while preserving the entropy of the input.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="key-agree">
                                        <xs:annotation>
                                            <xs:documentation>
                                                In cryptography, a key-agreement is a protocol whereby two or more
                                                parties agree on a cryptographic key in such a way that both influence
                                                the outcome.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="kem">
                                        <xs:annotation>
                                            <xs:documentation>
                                                A Key Encapsulation Mechanism (KEM) algorithm is a mechanism for
                                                transporting random keying material to a recipient using the recipient's
                                                public key.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="ae">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Authenticated Encryption (AE) is a cryptographic process that provides
                                                both confidentiality and data integrity. It ensures that the encrypted
                                                data has not been tampered with and comes from a legitimate source.
                                                AE is commonly used in secure communication protocols.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="combiner">
                                        <xs:annotation>
                                            <xs:documentation>
                                                A combiner aggregates many candidates for a cryptographic primitive and
                                                generates a new candidate for the same primitive.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="other">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Another primitive type.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="unknown">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The primitive is not known.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="parameterSetIdentifier" type="xs:string" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    An identifier for the parameter set of the cryptographic algorithm. Examples: in
                                    AES128, '128' identifies the key length in bits, in SHA256, '256' identifies the
                                    digest length, '128' in SHAKE128 identifies its maximum security level in bits, and
                                    'SHA2-128s' identifies a parameter set used in SLH-DSA (FIPS205).
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="curve" type="xs:string" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The specific underlying Elliptic Curve (EC) definition employed which is an indicator
                                    of the level of security strength, performance and complexity. Absent an
                                    authoritative source of curve names, CycloneDX recommends use of curve names as
                                    defined at https://neuromancer.sk/std/, the source from which can be found at
                                    https://github.com/J08nY/std-curves.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="executionEnvironment" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The target and execution environment in which the algorithm is implemented in.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:simpleType>
                                <xs:restriction base="xs:string">
                                    <xs:enumeration value="software-plain-ram">
                                        <xs:annotation>
                                            <xs:documentation>
                                                A software implementation running in plain unencrypted RAM.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="software-encrypted-ram">
                                        <xs:annotation>
                                            <xs:documentation>
                                                A software implementation running in encrypted RAM.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration><xs:enumeration value="software-tee">
                                    <xs:annotation>
                                        <xs:documentation>
                                            A software implementation running in a trusted execution environment.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:enumeration><xs:enumeration value="hardware">
                                    <xs:annotation>
                                        <xs:documentation>
                                            A hardware implementation.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:enumeration><xs:enumeration value="other">
                                    <xs:annotation>
                                        <xs:documentation>
                                            Another implementation environment.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:enumeration><xs:enumeration value="unknown">
                                    <xs:annotation>
                                        <xs:documentation>
                                            The execution environment is not known.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:enumeration>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="implementationPlatform" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The target platform for which the algorithm is implemented. The implementation can
                                    be 'generic', running on any platform or for a specific platform.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:simpleType>
                                <xs:restriction base="xs:string">
                                    <xs:enumeration value="generic"/>
                                    <xs:enumeration value="x86_32"/>
                                    <xs:enumeration value="x86_64"/>
                                    <xs:enumeration value="armv7-a"/>
                                    <xs:enumeration value="armv7-m"/>
                                    <xs:enumeration value="armv8-a"/>
                                    <xs:enumeration value="armv8-m"/>
                                    <xs:enumeration value="armv9-a"/>
                                    <xs:enumeration value="armv9-m"/>
                                    <xs:enumeration value="s390x"/>
                                    <xs:enumeration value="ppc64"/>
                                    <xs:enumeration value="ppc64le"/>
                                    <xs:enumeration value="other"/>
                                    <xs:enumeration value="unknown"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="certificationLevel" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>
                                    The certification that the implementation of the cryptographic algorithm has
                                    received, if any. Certifications include revisions and levels of FIPS 140 or
                                    Common Criteria of different Extended Assurance Levels (CC-EAL).
                                </xs:documentation>
                            </xs:annotation>
                            <xs:simpleType>
                                <xs:restriction base="xs:string">
                                    <xs:enumeration value="none">
                                        <xs:annotation>
                                            <xs:documentation>
                                                No certification obtained
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="fips140-1-l1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                FIPS 140-1 Level 1
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="fips140-1-l2">
                                        <xs:annotation>
                                            <xs:documentation>
                                                FIPS 140-1 Level 2
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="fips140-1-l3">
                                        <xs:annotation>
                                            <xs:documentation>
                                                FIPS 140-1 Level 3
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="fips140-1-l4">
                                        <xs:annotation>
                                            <xs:documentation>
                                                FIPS 140-1 Level 4
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="fips140-2-l1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                FIPS 140-2 Level 1
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="fips140-2-l2">
                                        <xs:annotation>
                                            <xs:documentation>
                                                FIPS 140-2 Level 2
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="fips140-2-l3">
                                        <xs:annotation>
                                            <xs:documentation>
                                                FIPS 140-2 Level 3
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="fips140-2-l4">
                                        <xs:annotation>
                                            <xs:documentation>
                                                FIPS 140-2 Level 4
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="fips140-3-l1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                FIPS 140-3 Level 1
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="fips140-3-l2">
                                        <xs:annotation>
                                            <xs:documentation>
                                                FIPS 140-3 Level 2
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="fips140-3-l3">
                                        <xs:annotation>
                                            <xs:documentation>
                                                FIPS 140-3 Level 3
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="fips140-3-l4">
                                        <xs:annotation>
                                            <xs:documentation>
                                                FIPS 140-3 Level 4
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="cc-eal1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Common Criteria - Evaluation Assurance Level 1
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="cc-eal1+">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Common Criteria - Evaluation Assurance Level 1 (Augmented)
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="cc-eal2">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Common Criteria - Evaluation Assurance Level 2
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="cc-eal2+">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Common Criteria - Evaluation Assurance Level 2 (Augmented)
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="cc-eal3">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Common Criteria - Evaluation Assurance Level 3
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="cc-eal3+">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Common Criteria - Evaluation Assurance Level 3 (Augmented)
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="cc-eal4">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Common Criteria - Evaluation Assurance Level 4
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="cc-eal4+">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Common Criteria - Evaluation Assurance Level 4 (Augmented)
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="cc-eal5">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Common Criteria - Evaluation Assurance Level 5
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="cc-eal5+">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Common Criteria - Evaluation Assurance Level 5 (Augmented)
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="cc-eal6">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Common Criteria - Evaluation Assurance Level 6
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="cc-eal6+">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Common Criteria - Evaluation Assurance Level 6 (Augmented)
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="cc-eal7">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Common Criteria - Evaluation Assurance Level 7
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="cc-eal7+">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Common Criteria - Evaluation Assurance Level 7 (Augmented)
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="other">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Another certification
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="unknown">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The certification level is not known
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="mode" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The mode of operation in which the cryptographic algorithm (block cipher) is used.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:simpleType>
                                <xs:restriction base="xs:string">
                                    <xs:enumeration value="cbc">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Cipher block chaining
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="ecb">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Electronic codebook
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="ccm">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Counter with cipher block chaining message authentication code
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="gcm">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Galois/counter
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="cfb">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Cipher feedback
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="ofb">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Output feedback
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="ctr">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Counter
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="other">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Another mode of operation
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="unknown">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The mode of operation is not known
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="padding" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The padding scheme that is used for the cryptographic algorithm.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:simpleType>
                                <xs:restriction base="xs:string">
                                    <xs:enumeration value="pkcs5">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Password-Based Cryptography Specification #5
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="pkcs7">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Public Key Cryptography Standard: Cryptographic Message Syntax
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="pkcs1v15">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Public Key Cryptography Standard: RSA Cryptography v1.5
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="oaep">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Optimal asymmetric encryption padding
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="raw">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Raw
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="other">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Another padding scheme
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="unknown">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The padding scheme is not known
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="cryptoFunctions" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The cryptographic functions implemented by the cryptographic algorithm.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="cryptoFunction" minOccurs="0" maxOccurs="unbounded">
                                        <xs:simpleType>
                                            <xs:restriction base="xs:string">
                                                <xs:enumeration value="generate"/>
                                                <xs:enumeration value="keygen"/>
                                                <xs:enumeration value="encrypt"/>
                                                <xs:enumeration value="decrypt"/>
                                                <xs:enumeration value="digest"/>
                                                <xs:enumeration value="tag"/>
                                                <xs:enumeration value="keyderive"/>
                                                <xs:enumeration value="sign"/>
                                                <xs:enumeration value="verify"/>
                                                <xs:enumeration value="encapsulate"/>
                                                <xs:enumeration value="decapsulate"/>
                                                <xs:enumeration value="other"/>
                                                <xs:enumeration value="unknown"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="classicalSecurityLevel" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The classical security level that a cryptographic algorithm provides (in bits).
                                </xs:documentation>
                            </xs:annotation>
                            <xs:simpleType>
                                <xs:restriction base="xs:integer">
                                    <xs:minInclusive value="0"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="nistQuantumSecurityLevel" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The NIST security strength category as defined in
                                    https://csrc.nist.gov/projects/post-quantum-cryptography/post-quantum-cryptography-standardization/evaluation-criteria/security-(evaluation-criteria).
                                    A value of 0 indicates that none of the categories are met.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:simpleType>
                                <xs:restriction base="xs:integer">
                                    <xs:minInclusive value="0"/>
                                    <xs:maxInclusive value="6"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="certificateProperties" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Properties for cryptographic assets of asset type 'certificate'
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="subjectName" type="xs:string" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The subject name for the certificate
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="issuerName" type="xs:string" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The issuer name for the certificate
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="notValidBefore" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The date and time according to ISO-8601 standard from which the certificate is valid
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="notValidAfter" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The date and time according to ISO-8601 standard from which the certificate is not valid anymore
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="signatureAlgorithmRef" type="bom:refType" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The bom-ref to signature algorithm used by the certificate
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="subjectPublicKeyRef" type="bom:refType" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The bom-ref to the public key of the subject
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="certificateFormat" type="xs:string" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The format of the certificate. Examples include X.509, PEM, DER, and CVC
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="certificateExtension" type="xs:string" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The file extension of the certificate. Examples include crt, pem, cer, der, and p12.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="relatedCryptoMaterialProperties" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Properties for cryptographic assets of asset type 'relatedCryptoMaterial'
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="type" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The type for the related cryptographic material
                                </xs:documentation>
                            </xs:annotation>
                            <xs:simpleType>
                                <xs:restriction base="xs:string">
                                    <xs:enumeration value="private-key"/>
                                    <xs:enumeration value="public-key"/>
                                    <xs:enumeration value="secret-key"/>
                                    <xs:enumeration value="key"/>
                                    <xs:enumeration value="ciphertext"/>
                                    <xs:enumeration value="signature"/>
                                    <xs:enumeration value="digest"/>
                                    <xs:enumeration value="initialization-vector"/>
                                    <xs:enumeration value="nonce"/>
                                    <xs:enumeration value="seed"/>
                                    <xs:enumeration value="salt"/>
                                    <xs:enumeration value="shared-secret"/>
                                    <xs:enumeration value="tag"/>
                                    <xs:enumeration value="additional-data"/>
                                    <xs:enumeration value="password"/>
                                    <xs:enumeration value="credential"/>
                                    <xs:enumeration value="token"/>
                                    <xs:enumeration value="other"/>
                                    <xs:enumeration value="unknown"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="id" type="xs:string" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The optional unique identifier for the related cryptographic material.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="state" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The key state as defined by NIST SP 800-57.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:simpleType>
                                <xs:restriction base="xs:string">
                                    <xs:enumeration value="pre-activation"/>
                                    <xs:enumeration value="active"/>
                                    <xs:enumeration value="suspended"/>
                                    <xs:enumeration value="deactivated"/>
                                    <xs:enumeration value="compromised"/>
                                    <xs:enumeration value="destroyed"/>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="algorithmRef" type="bom:refType" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The bom-ref to the algorithm used to generate the related cryptographic material.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="creationDate" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The date and time (timestamp) when the related cryptographic material was created.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="activationDate" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The date and time (timestamp) when the related cryptographic material was activated.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="updateDate" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The date and time (timestamp) when the related cryptographic material was updated.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="expirationDate" type="xs:dateTime" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The date and time (timestamp) when the related cryptographic material expires.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="value" type="xs:string" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The associated value of the cryptographic material.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="size" type="xs:integer" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The size of the cryptographic asset (in bits).
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="format" type="xs:string" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The format of the related cryptographic material (e.g. P8, PEM, DER).
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="securedBy" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The mechanism by which the cryptographic asset is secured by.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="mechanism" type="xs:string" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Specifies the mechanism by which the cryptographic asset is secured by.
                                                Examples include HSM, TPM, XGX, Software, and None.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="algorithmRef" type="bom:refType" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The bom-ref to the algorithm.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="protocolProperties" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        Properties specific to cryptographic assets of type: 'protocol'.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="type" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The concrete protocol type.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:simpleType>
                                <xs:restriction base="xs:string">
                                    <xs:enumeration value="tls">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Transport Layer Security
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="ssh">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Secure Shell
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="ipsec">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Internet Protocol Security
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="ike">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Internet Key Exchange
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="sstp">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Secure Socket Tunneling Protocol
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="wpa">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Wi-Fi Protected Access
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="other">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Another protocol type
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                    <xs:enumeration value="unknown">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The protocol type is not known
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:enumeration>
                                </xs:restriction>
                            </xs:simpleType>
                        </xs:element>
                        <xs:element name="version" type="xs:string" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The version of the protocol. Examples include 1.0, 1.2, and 1.99.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="cipherSuites" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    A list of cipher suites related to the protocol.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="cipherSuite" minOccurs="0" maxOccurs="unbounded">
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            A common name for the cipher suite. For example: TLS_DHE_RSA_WITH_AES_128_CCM
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                                <xs:element name="algorithms" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            A list of algorithms related to the cipher suite.
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                    <xs:complexType>
                                                        <xs:sequence>
                                                            <xs:element name="algorithm" type="bom:refType" minOccurs="0" maxOccurs="unbounded">
                                                                <xs:annotation>
                                                                    <xs:documentation>
                                                                        The bom-ref to algorithm cryptographic asset.
                                                                    </xs:documentation>
                                                                </xs:annotation>
                                                            </xs:element>
                                                        </xs:sequence>
                                                    </xs:complexType>
                                                </xs:element>
                                                <xs:element name="identifiers" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            A list of common identifiers for the cipher suite.
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                    <xs:complexType>
                                                        <xs:sequence>
                                                            <xs:element name="identifier" type="xs:string" minOccurs="0" maxOccurs="unbounded">
                                                                <xs:annotation>
                                                                    <xs:documentation>
                                                                        Cipher suite identifier. Examples include 0xC0 and 0x9E.
                                                                    </xs:documentation>
                                                                </xs:annotation>
                                                            </xs:element>
                                                        </xs:sequence>
                                                    </xs:complexType>
                                                </xs:element>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="ikev2TransformTypes" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The IKEv2 transform types supported (types 1-4), defined in RFC7296 section 3.3.2,
                                    and additional properties.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="encr" type="bom:refType" minOccurs="0" maxOccurs="unbounded">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Transform Type 1: encryption algorithms
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="prf" type="bom:refType" minOccurs="0" maxOccurs="unbounded">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Transform Type 2: pseudorandom functions
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="integ" type="bom:refType" minOccurs="0" maxOccurs="unbounded">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Transform Type 3: integrity algorithms
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="ke" type="bom:refType" minOccurs="0" maxOccurs="unbounded">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Transform Type 4: Key Exchange Method (KE) per RFC9370, formerly called Diffie-Hellman Group (D-H)
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="esn" type="xs:boolean" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Specifies if an Extended Sequence Number (ESN) is used.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="auth" type="bom:refType" minOccurs="0" maxOccurs="unbounded">
                                        <xs:annotation>
                                            <xs:documentation>
                                                IKEv2 Authentication method
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="cryptoRef" type="bom:refType" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>A protocol-related cryptographic assets</xs:documentation>
                            </xs:annotation>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="oid" type="xs:string" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The object identifier (OID) of the cryptographic asset.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="declarationsType">
        <xs:sequence>
            <xs:element name="assessors" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The list of assessors evaluating claims and determining conformance to requirements and confidence in that assessment.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="assessor" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>
                                    The assessor who evaluates claims and determines conformance to requirements and confidence in that assessment.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="thirdParty" type="xs:boolean" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The boolean indicating if the assessor is outside the organization generating claims. A value of false indicates a self assessor.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="organization" type="bom:organizationalEntity" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The entity issuing the assessment.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                </xs:sequence>
                                <xs:attribute name="bom-ref" type="bom:refType">
                                    <xs:annotation>
                                        <xs:documentation>
                                            An optional identifier which can be used to reference the object elsewhere in the BOM.
                                            Every bom-ref must be unique within the BOM.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:anyAttribute namespace="##other" processContents="lax">
                                    <xs:annotation>
                                        <xs:documentation>User-defined attributes may be used on this element as long as they
                                            do not have the same name as an existing attribute used by the schema.</xs:documentation>
                                    </xs:annotation>
                                </xs:anyAttribute>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="attestations" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The list of attestations asserted by an assessor that maps requirements to claims.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="attestation" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>
                                    An attestation asserted by an assessor that maps requirements to claims.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="summary" type="xs:string" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The short description explaining the main points of the attestation.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="assessor" type="bom:refLinkType" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The `bom-ref` to the assessor asserting the attestation.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="map" minOccurs="0" maxOccurs="unbounded">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The grouping of requirements to claims and the attestors declared conformance and confidence thereof.
                                            </xs:documentation>
                                        </xs:annotation>
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="requirement" type="bom:refLinkType" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            The `bom-ref` to the requirement being attested to.
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                                <xs:element name="claims" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            The list of `bom-ref` to the claims being attested to.
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                    <xs:complexType>
                                                        <xs:sequence>
                                                            <xs:element name="claim" type="bom:refLinkType" minOccurs="0" maxOccurs="unbounded">
                                                                <xs:annotation>
                                                                    <xs:documentation>
                                                                        The `bom-ref` to the claim being attested to.
                                                                    </xs:documentation>
                                                                </xs:annotation>
                                                            </xs:element>
                                                        </xs:sequence>
                                                    </xs:complexType>
                                                </xs:element>
                                                <xs:element name="counterClaims" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            The list of `bom-ref` to the counter claims being attested to.
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                    <xs:complexType>
                                                        <xs:sequence>
                                                            <xs:element name="counterClaim" type="bom:refLinkType" minOccurs="0" maxOccurs="unbounded">
                                                                <xs:annotation>
                                                                    <xs:documentation>
                                                                        The `bom-ref` to the counter claim being attested to.
                                                                    </xs:documentation>
                                                                </xs:annotation>
                                                            </xs:element>
                                                        </xs:sequence>
                                                    </xs:complexType>
                                                </xs:element>
                                                <xs:element name="conformance" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            The conformance of the claim meeting a requirement.
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                    <xs:complexType>
                                                        <xs:sequence>
                                                            <xs:element name="score">
                                                                <xs:annotation>
                                                                    <xs:documentation>
                                                                        The conformance of the claim between and inclusive of 0 and 1, where 1 is 100% conformance.
                                                                    </xs:documentation>
                                                                </xs:annotation>
                                                                <xs:simpleType>
                                                                    <xs:restriction base="xs:decimal">
                                                                        <xs:minInclusive value="0"/>
                                                                        <xs:maxInclusive value="1"/>
                                                                    </xs:restriction>
                                                                </xs:simpleType>
                                                            </xs:element>
                                                            <xs:element name="rationale" type="xs:string" minOccurs="0" maxOccurs="1">
                                                                <xs:annotation>
                                                                    <xs:documentation>
                                                                        The rationale for the score of conformance.
                                                                    </xs:documentation>
                                                                </xs:annotation>
                                                            </xs:element>
                                                            <xs:element name="mitigationStrategies" minOccurs="0" maxOccurs="1">
                                                                <xs:annotation>
                                                                    <xs:documentation>
                                                                        The list of  `bom-ref` to the evidence provided describing the
                                                                        mitigation strategies. Each mitigation strategy should include an
                                                                        explanation of how any weaknesses in the evidence will be mitigated.
                                                                    </xs:documentation>
                                                                </xs:annotation>
                                                                <xs:complexType>
                                                                    <xs:sequence>
                                                                        <xs:element name="mitigationStrategy" type="bom:refLinkType" minOccurs="0" maxOccurs="unbounded"/>
                                                                    </xs:sequence>
                                                                </xs:complexType>
                                                            </xs:element>
                                                        </xs:sequence>
                                                    </xs:complexType>
                                                </xs:element>
                                                <xs:element name="confidence" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            The confidence of the claim meeting the requirement.
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                    <xs:complexType>
                                                        <xs:sequence>
                                                            <xs:element name="score">
                                                                <xs:annotation>
                                                                    <xs:documentation>
                                                                        The confidence of the claim between and inclusive of 0 and 1, where 1 is 100% confidence.
                                                                    </xs:documentation>
                                                                </xs:annotation>
                                                                <xs:simpleType>
                                                                    <xs:restriction base="xs:decimal">
                                                                        <xs:minInclusive value="0"/>
                                                                        <xs:maxInclusive value="1"/>
                                                                    </xs:restriction>
                                                                </xs:simpleType>
                                                            </xs:element>
                                                            <xs:element name="rationale" type="xs:string" minOccurs="0" maxOccurs="1">
                                                                <xs:annotation>
                                                                    <xs:documentation>
                                                                        The rationale for the confidence score.
                                                                    </xs:documentation>
                                                                </xs:annotation>
                                                            </xs:element>
                                                        </xs:sequence>
                                                    </xs:complexType>
                                                </xs:element>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                    <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Allows any undeclared elements as long as the elements are placed in a different namespace.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:any>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="claims" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The list of claims.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="claim" minOccurs="0" maxOccurs="unbounded">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="target" type="bom:refLinkType" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The `bom-ref` to a target representing a specific system, application,
                                                API, module, team, person, process, business unit, company, etc...
                                                that this claim is being applied to.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="predicate" type="xs:string" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The specific statement or assertion about the target.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="mitigationStrategies" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The list of  `bom-ref` to the evidence provided describing the
                                                mitigation strategies. Each mitigation strategy should include an
                                                explanation of how any weaknesses in the evidence will be mitigated.
                                            </xs:documentation>
                                        </xs:annotation>
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="mitigationStrategy" type="bom:refLinkType" minOccurs="0" maxOccurs="unbounded"/>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                    <xs:element name="reasoning" type="xs:string" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The written explanation of why the evidence provided substantiates the claim.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="evidence" type="bom:refLinkType" minOccurs="0" maxOccurs="unbounded">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The list of `bom-ref` to evidence that supports this claim.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="counterEvidence" type="bom:refLinkType" minOccurs="0" maxOccurs="unbounded">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The list of `bom-ref` to counterEvidence that supports this claim.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="externalReferences" type="bom:externalReferences" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>Provides the ability to document external references related to the claim the BOM describes.</xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Allows any undeclared elements as long as the elements are placed in a different namespace.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:any>
                                </xs:sequence>
                                <xs:attribute name="bom-ref" type="bom:refType">
                                    <xs:annotation>
                                        <xs:documentation>
                                            An optional identifier which can be used to reference the object elsewhere
                                            in the BOM. Every bom-ref must be unique within the BOM.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:anyAttribute namespace="##other" processContents="lax">
                                    <xs:annotation>
                                        <xs:documentation>User-defined attributes may be used on this element as long as they
                                            do not have the same name as an existing attribute used by the schema.</xs:documentation>
                                    </xs:annotation>
                                </xs:anyAttribute>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="evidence" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The list of evidence
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="evidence" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>
                                    The list of evidence
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="propertyName" type="xs:string" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The reference to the property name as defined in the [CycloneDX Property Taxonomy](https://github.com/CycloneDX/cyclonedx-property-taxonomy/).
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="description" type="xs:string" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The written description of what this evidence is and how it was created.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="data" minOccurs="0" maxOccurs="unbounded">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The output or analysis that supports claims.
                                            </xs:documentation>
                                        </xs:annotation>
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            The name of the data.
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                                <xs:element name="contents" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            The contents or references to the contents of the data being described.
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                    <xs:complexType>
                                                        <xs:sequence>
                                                            <xs:element name="attachment" type="bom:attachedTextType" minOccurs="0" maxOccurs="1">
                                                                <xs:annotation>
                                                                    <xs:documentation>An optional way to include textual or encoded data.</xs:documentation>
                                                                </xs:annotation>
                                                            </xs:element>
                                                            <xs:element name="url" type="xs:anyURI" minOccurs="0" maxOccurs="1">
                                                                <xs:annotation>
                                                                    <xs:documentation>The URL to where the data can be retrieved.</xs:documentation>
                                                                </xs:annotation>
                                                            </xs:element>
                                                        </xs:sequence>
                                                    </xs:complexType>
                                                </xs:element>
                                                <xs:element name="classification" type="xs:string" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            Data classification tags data according to its type, sensitivity, and value if altered, stolen, or destroyed.
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                                <xs:element name="sensitiveData" minOccurs="0" maxOccurs="unbounded">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            A description of any sensitive data.
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                                <xs:element name="governance" type="bom:dataGovernance" minOccurs="0" maxOccurs="1" />
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                    <xs:element name="created" type="xs:dateTime" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>The date and time (timestamp) when the evidence was created.</xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="expires" type="xs:dateTime" minOccurs="0">
                                        <xs:annotation>
                                            <xs:documentation>The optional date and time (timestamp) when the evidence is no longer valid.</xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="author" type="bom:organizationalContact" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>The author of the evidence.</xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="reviewer" type="bom:organizationalContact" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>The reviewer of the evidence.</xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                                        <xs:annotation>
                                            <xs:documentation>
                                                Allows any undeclared elements as long as the elements are placed in a different namespace.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:any>
                                </xs:sequence>
                                <xs:attribute name="bom-ref" type="bom:refType">
                                    <xs:annotation>
                                        <xs:documentation>
                                            An optional identifier which can be used to reference the object elsewhere
                                            in the BOM. Every bom-ref must be unique within the BOM.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:anyAttribute namespace="##other" processContents="lax">
                                    <xs:annotation>
                                        <xs:documentation>User-defined attributes may be used on this element as long as they
                                            do not have the same name as an existing attribute used by the schema.</xs:documentation>
                                    </xs:annotation>
                                </xs:anyAttribute>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="targets" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The list of targets which claims are made against.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="organizations" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The list of organizations which claims are made against.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="organization" type="bom:organizationalEntity" minOccurs="0" maxOccurs="unbounded" />
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="components" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The list of components which claims are made against.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="component" type="bom:component" minOccurs="0" maxOccurs="unbounded" />
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:element name="services" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The list of services which claims are made against.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="service" type="bom:service" minOccurs="0" maxOccurs="unbounded" />
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="affirmation" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        A concise statement affirmed by an individual regarding all declarations, often used for third-party auditor acceptance or recipient acknowledgment.
                        It includes a list of authorized signatories who assert the validity of the document on behalf of the organization.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="statement" type="xs:string" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The brief statement affirmed by an individual regarding all declarations.
                                    This could be an affirmation of acceptance by a third-party auditor or receiving
                                    individual of a file. For example: "I certify, to the best of my knowledge, that all information is correct."
                                </xs:documentation>
                            </xs:annotation>
                        </xs:element>
                        <xs:element name="signatories" minOccurs="0" maxOccurs="1">
                            <xs:annotation>
                                <xs:documentation>
                                    The list of signatories authorized on behalf of an organization to assert validity of this document.
                                </xs:documentation>
                            </xs:annotation>
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="signatory" minOccurs="0" maxOccurs="unbounded">
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="name" type="xs:string" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            The signatory's name.
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                                <xs:element name="role" type="xs:string" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            The signatory's role within an organization.
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                                <xs:element name="organization" type="bom:organizationalEntity" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            The signatory's organization.
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                                <xs:element name="externalReference" type="bom:externalReference" minOccurs="0" maxOccurs="1">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            An External reference provide a way to document systems, sites, and information that may be relevant, but are not included with the BOM. They may also establish specific relationships within or external to the BOM.
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                </xs:element>
                                                <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                                                    <xs:annotation>
                                                        <xs:documentation>
                                                            Allows any undeclared elements as long as the elements are placed in a different namespace.
                                                        </xs:documentation>
                                                    </xs:annotation>
                                                </xs:any>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                </xs:sequence>
                            </xs:complexType>
                        </xs:element>
                        <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                            <xs:annotation>
                                <xs:documentation>
                                    Allows any undeclared elements as long as the elements are placed in a different namespace.
                                </xs:documentation>
                            </xs:annotation>
                        </xs:any>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="definitionsType">
        <xs:annotation>
            <xs:documentation>
                A collection of reusable objects that are defined and may be used elsewhere in the BOM.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="standards" type="bom:standardsType" minOccurs="0" maxOccurs="1"/>
        </xs:sequence>
    </xs:complexType>

    <xs:complexType name="standardsType">
        <xs:annotation>
            <xs:documentation>
                The list of standards which may consist of regulations, industry or organizational-specific standards, maturity models, best practices, or any other requirements which can be evaluated against or attested to.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
            <xs:element name="standard" type="bom:standard"/>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:anyAttribute namespace="##any" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="standard">
        <xs:annotation>
            <xs:documentation>
                A standard may consist of regulations, industry or organizational-specific standards, maturity models, best practices, or any other requirements which can be evaluated against or attested to.
            </xs:documentation>
        </xs:annotation>
        <xs:sequence>
            <xs:element name="name" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        The name of the standard. This will often be a shortened, single name of the standard.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="version" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        The version of the standard.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="description" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        The description of the standard.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="owner" type="xs:string">
                <xs:annotation>
                    <xs:documentation>
                        The owner of the standard, often the entity responsible for its release.
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:element name="requirements" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The list of requirements comprising the standard.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="requirement" minOccurs="0" maxOccurs="unbounded">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="identifier" type="xs:string" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The unique identifier used in the standard to identify a specific requirement. This should match what is in the standard and should not be the requirements bom-ref.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="title" type="xs:string" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The title of the requirement.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="text" type="xs:string" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The textual content of the requirement.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="descriptions" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The supplemental text that provides additional guidance or context to the requirement, but is not directly part of the requirement.
                                            </xs:documentation>
                                        </xs:annotation>
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="description" type="xs:string" minOccurs="0" maxOccurs="unbounded"/>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                    <xs:element name="openCre" minOccurs="0" maxOccurs="unbounded">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The Common Requirements Enumeration (CRE) identifier(s). CRE is a structured and standardized framework for uniting security standards and guidelines. CRE links each section of a resource to a shared topic identifier (a Common Requirement). Through this shared topic link, all resources map to each other. Use of CRE promotes clear and unambiguous communication among stakeholders.
                                            </xs:documentation>
                                        </xs:annotation>
                                        <xs:simpleType>
                                            <xs:restriction base="xs:string">
                                                <xs:pattern value="CRE:[0-9]+-[0-9]+"/>
                                            </xs:restriction>
                                        </xs:simpleType>
                                    </xs:element>
                                    <xs:element name="parent" type="bom:refLinkType" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The optional `bom-ref` to a parent requirement. This establishes a hierarchy of requirements. Top-level requirements must not define a parent. Only child requirements should define parents.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="properties" type="bom:propertiesType" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>Provides the ability to document properties in a name/value store.
                                                This provides flexibility to include data not officially supported in the standard
                                                without having to use additional namespaces or create extensions. Property names
                                                of interest to the general public are encouraged to be registered in the
                                                CycloneDX Property Taxonomy - https://github.com/CycloneDX/cyclonedx-property-taxonomy.
                                                Formal registration is optional.</xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="externalReferences" type="bom:externalReferences" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>Provides the ability to document external references related to the BOM or
                                                to the project the BOM describes.</xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                </xs:sequence>
                                <xs:attribute name="bom-ref" type="bom:refType">
                                    <xs:annotation>
                                        <xs:documentation>
                                            An optional identifier which can be used to reference the object elsewhere
                                            in the BOM. Every bom-ref must be unique within the BOM.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:anyAttribute namespace="##other" processContents="lax">
                                    <xs:annotation>
                                        <xs:documentation>User-defined attributes may be used on this element as long as they
                                            do not have the same name as an existing attribute used by the schema.</xs:documentation>
                                    </xs:annotation>
                                </xs:anyAttribute>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="levels" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>
                        The list of levels associated with the standard. Some standards have different levels of compliance.
                    </xs:documentation>
                </xs:annotation>
                <xs:complexType>
                    <xs:sequence>
                        <xs:element name="level" minOccurs="0" maxOccurs="unbounded">
                            <xs:complexType>
                                <xs:sequence>
                                    <xs:element name="identifier" type="xs:string" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The identifier used in the standard to identify a specific level.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="title" type="xs:string" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The title of the level.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="description" type="xs:string" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The description of the level.
                                            </xs:documentation>
                                        </xs:annotation>
                                    </xs:element>
                                    <xs:element name="requirements" minOccurs="0" maxOccurs="1">
                                        <xs:annotation>
                                            <xs:documentation>
                                                The list of requirement `bom-ref`s that comprise the level.
                                            </xs:documentation>
                                        </xs:annotation>
                                        <xs:complexType>
                                            <xs:sequence>
                                                <xs:element name="requirement" type="bom:refLinkType" minOccurs="0" maxOccurs="unbounded"/>
                                            </xs:sequence>
                                        </xs:complexType>
                                    </xs:element>
                                </xs:sequence>
                                <xs:attribute name="bom-ref" type="bom:refType">
                                    <xs:annotation>
                                        <xs:documentation>
                                            An optional identifier which can be used to reference the object elsewhere
                                            in the BOM. Every bom-ref must be unique within the BOM.
                                        </xs:documentation>
                                    </xs:annotation>
                                </xs:attribute>
                                <xs:anyAttribute namespace="##other" processContents="lax">
                                    <xs:annotation>
                                        <xs:documentation>User-defined attributes may be used on this element as long as they
                                            do not have the same name as an existing attribute used by the schema.</xs:documentation>
                                    </xs:annotation>
                                </xs:anyAttribute>
                            </xs:complexType>
                        </xs:element>
                    </xs:sequence>
                </xs:complexType>
            </xs:element>
            <xs:element name="externalReferences" type="bom:externalReferences" minOccurs="0" maxOccurs="1">
                <xs:annotation>
                    <xs:documentation>Provides the ability to document external references related to the BOM or
                        to the project the BOM describes.</xs:documentation>
                </xs:annotation>
            </xs:element>
            <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                <xs:annotation>
                    <xs:documentation>
                        Allows any undeclared elements as long as the elements are placed in a different namespace.
                    </xs:documentation>
                </xs:annotation>
            </xs:any>
        </xs:sequence>
        <xs:attribute name="bom-ref" type="bom:refType">
            <xs:annotation>
                <xs:documentation>
                    An optional identifier which can be used to reference the object elsewhere
                    in the BOM. Every bom-ref must be unique within the BOM.
                </xs:documentation>
            </xs:annotation>
        </xs:attribute>
        <xs:anyAttribute namespace="##other" processContents="lax">
            <xs:annotation>
                <xs:documentation>User-defined attributes may be used on this element as long as they
                    do not have the same name as an existing attribute used by the schema.</xs:documentation>
            </xs:annotation>
        </xs:anyAttribute>
    </xs:complexType>

    <xs:complexType name="tagsType">
        <xs:sequence minOccurs="0" maxOccurs="unbounded">
            <xs:element name="tag" type="xs:normalizedString">
                <xs:annotation>
                    <xs:documentation>Textual strings that aid in discovery, search, and retrieval of the associated
                        object. Tags often serve as a way to group or categorize similar or related objects by various
                        attributes.

                        Examples include:
                        "json-parser", "object-persistence", "text-to-image", "translation", and "object-detection"
                    </xs:documentation>
                </xs:annotation>
            </xs:element>
        </xs:sequence>
    </xs:complexType>

    <xs:element name="bom">
        <xs:complexType>
            <xs:sequence>
                <xs:element name="metadata" type="bom:metadata" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Provides additional information about a BOM.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="components" type="bom:componentsType" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>A list of software and hardware components.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="services" type="bom:servicesType" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>A list of services. This may include microservices, function-as-a-service, and other types of network or intra-process services.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="externalReferences" type="bom:externalReferences" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Provides the ability to document external references related to the BOM or
                            to the project the BOM describes.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="dependencies" type="bom:dependenciesType" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Provides the ability to document dependency relationships.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="compositions" type="bom:compositionsType" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Compositions describe constituent parts (including components, services, and dependency relationships) and their completeness. The completeness of vulnerabilities expressed in a BOM may also be described.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="properties" type="bom:propertiesType" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Provides the ability to document properties in a name/value store.
                            This provides flexibility to include data not officially supported in the standard
                            without having to use additional namespaces or create extensions. Property names
                            of interest to the general public are encouraged to be registered in the
                            CycloneDX Property Taxonomy - https://github.com/CycloneDX/cyclonedx-property-taxonomy.
                            Formal registration is optional.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="vulnerabilities" type="bom:vulnerabilitiesType" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Vulnerabilities identified in components or services.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="annotations" type="bom:annotationsType" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Comments made by people, organizations, or tools about any object with
                            a bom-ref, such as components, services, vulnerabilities, or the BOM itself. Unlike
                            inventory information, annotations may contain opinion or commentary from various
                            stakeholders. Annotations may be inline (with inventory) or externalized via BOM-Link,
                            and may optionally be signed.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="formulation" type="bom:formulationType" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>Describes how a component or service was manufactured or deployed. This is
                            achieved through the use of formulas, workflows, tasks, and steps, which declare the precise
                            steps to reproduce along with the observed formulas describing the steps which transpired
                            in the manufacturing process.</xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="declarations" type="bom:declarationsType" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>
                            The list of declarations which describe the conformance to standards. Each declaration may
                            include attestations, claims, and evidence.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:element name="definitions" type="bom:definitionsType" minOccurs="0" maxOccurs="1">
                    <xs:annotation>
                        <xs:documentation>
                            A collection of reusable objects that are defined and may be used elsewhere in the BOM.
                        </xs:documentation>
                    </xs:annotation>
                </xs:element>
                <xs:any namespace="##other" processContents="lax" minOccurs="0" maxOccurs="unbounded">
                    <xs:annotation>
                        <xs:documentation>
                            Allows any undeclared elements as long as the elements are placed in a different namespace.
                        </xs:documentation>
                    </xs:annotation>
                </xs:any>
            </xs:sequence>
            <xs:attribute name="version" type="xs:positiveInteger" default="1">
                <xs:annotation>
                    <xs:documentation>Whenever an existing BOM is modified, either manually or through automated
                        processes, the version of the BOM SHOULD be incremented by 1. When a system is presented with
                        multiple BOMs with identical serial numbers, the system SHOULD use the most recent version of the BOM.
                        The default version is '1'.</xs:documentation>
                </xs:annotation>
            </xs:attribute>
            <xs:attribute name="serialNumber" type="bom:urnUuid">
                <xs:annotation>
                    <xs:documentation>Every BOM generated SHOULD have a unique serial number, even if the contents of
                        the BOM have not changed over time. If specified, the serial number must conform to RFC-4122.
                        Use of serial numbers are recommended.</xs:documentation>
                </xs:annotation>
            </xs:attribute>
            <xs:anyAttribute namespace="##any" processContents="lax">
                <xs:annotation>
                    <xs:documentation>User-defined attributes may be used on this element as long as they
                        do not have the same name as an existing attribute used by the schema.</xs:documentation>
                </xs:annotation>
            </xs:anyAttribute>
        </xs:complexType>
        <xs:unique name="bom-ref">
            <xs:selector xpath=".//*"/>
            <xs:field xpath="@bom-ref"/>
        </xs:unique>
    </xs:element>
</xs:schema>
