{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "http://cyclonedx.org/schema/bom-1.2b.schema.json", "type": "object", "title": "CycloneDX Software Bill-of-Material Specification", "$comment": "CycloneDX JSON schema is published under the terms of the Apache License 2.0.", "required": ["bomFormat", "specVersion"], "properties": {"bomFormat": {"$id": "#/properties/bomFormat", "type": "string", "title": "BOM Format", "description": "Specifies the format of the BOM. This helps to identify the file as CycloneDX since BOMs do not have a filename convention nor does JSON schema support namespaces.", "enum": ["CycloneDX"]}, "specVersion": {"$id": "#/properties/specVersion", "type": "string", "title": "CycloneDX Specification Version", "description": "The version of the CycloneDX specification a BOM is written to (starting at version 1.2)", "examples": ["1.2"]}, "serialNumber": {"$id": "#/properties/serialNumber", "type": "string", "title": "BOM Serial Number", "description": "Every BOM generated should have a unique serial number, even if the contents of the BOM being generated have not changed over time. The process or tool responsible for creating the BOM should create random UUID's for every BOM generated.", "default": "", "examples": ["urn:uuid:3e671687-395b-41f5-a30f-a58921a69b79"], "pattern": "^urn:uuid:[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"}, "version": {"$id": "#/properties/version", "type": "integer", "title": "BOM Version", "description": "The version allows component publishers/authors to make changes to existing BOMs to update various aspects of the document such as description or licenses. When a system is presented with multiple BOMs for the same component, the system should use the most recent version of the BOM. The default version is '1' and should be incremented for each version of the BOM that is published. Each version of a component should have a unique BOM and if no changes are made to the BOMs, then each BOM will have a version of '1'.", "default": 1, "examples": [1]}, "metadata": {"$id": "#/properties/metadata", "$ref": "#/definitions/metadata", "title": "BOM Metadata", "description": "Provides additional information about a BOM."}, "components": {"$id": "#/properties/components", "type": "array", "items": {"$ref": "#/definitions/component"}, "uniqueItems": true, "title": "Components"}, "services": {"$id": "#/properties/services", "type": "array", "items": {"$ref": "#/definitions/service"}, "uniqueItems": true, "title": "Services"}, "externalReferences": {"$id": "#/properties/externalReferences", "type": "array", "items": {"$ref": "#/definitions/externalReference"}, "title": "External References", "description": "External references provide a way to document systems, sites, and information that may be relevant but which are not included with the BOM."}, "dependencies": {"$id": "#/properties/dependencies", "type": "array", "items": {"$ref": "#/definitions/dependency"}, "uniqueItems": true, "title": "Dependencies", "description": "Provides the ability to document dependency relationships."}}, "definitions": {"refType": {"$comment": "Identifier-DataType for interlinked elements.", "type": "string"}, "metadata": {"type": "object", "title": "BOM Metadata Object", "properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp", "description": "The date and time (timestamp) when the document was created."}, "tools": {"type": "array", "title": "Creation Tools", "description": "The tool(s) used in the creation of the BOM.", "items": {"$ref": "#/definitions/tool"}}, "authors": {"type": "array", "title": "Authors", "description": "The person(s) who created the BOM. Authors are common in BOMs created through manual processes. BOMs created through automated means may not have authors.", "items": {"$ref": "#/definitions/organizationalContact"}}, "component": {"title": "Component", "description": "The component that the BOM describes.", "$ref": "#/definitions/component"}, "manufacture": {"title": "Manufacture", "description": "The organization that manufactured the component that the BOM describes.", "$ref": "#/definitions/organizationalEntity"}, "supplier": {"title": "Supplier", "description": " The organization that supplied the component that the BOM describes. The supplier may often be the manufacture, but may also be a distributor or repackager.", "$ref": "#/definitions/organizationalEntity"}}}, "tool": {"type": "object", "title": "Tool", "description": "The tool used to create the BOM.", "properties": {"vendor": {"type": "string", "format": "string", "title": "<PERSON><PERSON>", "description": "The date and time (timestamp) when the document was created."}, "name": {"type": "string", "format": "string", "title": "Tool Name", "description": "The date and time (timestamp) when the document was created."}, "version": {"type": "string", "format": "string", "title": "Tool Version", "description": "The date and time (timestamp) when the document was created."}, "hashes": {"$id": "#/definitions/tool/properties/hashes", "type": "array", "items": {"$ref": "#/definitions/hash"}, "title": "<PERSON>hes", "description": "The hashes of the tool (if applicable)."}}}, "organizationalEntity": {"type": "object", "title": "Organizational Entity Object", "description": "", "properties": {"name": {"type": "string", "title": "Name", "description": "The name of the organization", "default": "", "examples": ["Example Inc."], "pattern": "^(.*)$"}, "url": {"type": "array", "title": "URL", "description": "The URL of the organization. Multiple URLs are allowed.", "default": "", "examples": ["https://example.com"], "pattern": "^(.*)$"}, "contact": {"type": "array", "title": "Contact", "description": "A contact at the organization. Multiple contacts are allowed.", "items": {"$ref": "#/definitions/organizationalContact"}}}}, "organizationalContact": {"type": "object", "title": "Organizational Contact Object", "description": "", "properties": {"name": {"type": "string", "title": "Name", "description": "The name of a contact", "default": "", "examples": ["Contact name"], "pattern": "^(.*)$"}, "email": {"type": "string", "title": "Email Address", "description": "The email address of the contact. Multiple email addresses are allowed.", "default": "", "examples": ["<EMAIL>"], "pattern": "^(.*)$"}, "phone": {"type": "string", "title": "Phone", "description": "The phone number of the contact. Multiple phone numbers are allowed.", "default": "", "examples": ["************"], "pattern": "^(.*)$"}}}, "component": {"type": "object", "title": "Component Object", "required": ["type", "name", "version"], "properties": {"type": {"type": "string", "enum": ["application", "framework", "library", "container", "operating-system", "device", "firmware", "file"], "title": "Component Type", "description": "Specifies the type of component. For software components, classify as application if no more specific appropriate classification is available or cannot be determined for the component.", "default": "", "examples": ["library"], "pattern": "^(.*)$"}, "mime-type": {"type": "string", "title": "Mime-Type", "description": "The optional mime-type of the component. When used on file components, the mime-type can provide additional context about the kind of file being represented such as an image, font, or executable. Some library or framework components may also have an associated mime-type.", "default": "", "examples": ["image/jpeg"], "pattern": "^[-+a-z0-9.]+/[-+a-z0-9.]+$"}, "bom-ref": {"$ref": "#/definitions/refType", "title": "BOM Reference", "description": "An optional identifier which can be used to reference the component elsewhere in the BOM. Every bom-ref should be unique.", "default": "", "pattern": "^(.*)$"}, "supplier": {"title": "Component Supplier", "description": " The organization that supplied the component. The supplier may often be the manufacture, but may also be a distributor or repackager.", "$ref": "#/definitions/organizationalEntity"}, "author": {"type": "string", "title": "Component Author", "description": "The person(s) or organization(s) that authored the component", "default": "", "examples": ["Acme Inc"], "pattern": "^(.*)$"}, "publisher": {"type": "string", "title": "Component Publisher", "description": "The person(s) or organization(s) that published the component", "default": "", "examples": ["Acme Inc"], "pattern": "^(.*)$"}, "group": {"type": "string", "title": "Component Group", "description": "The grouping name or identifier. This will often be a shortened, single name of the company or project that produced the component, or the source package or domain name. Whitespace and special characters should be avoided. Examples include: apache, org.apache.commons, and apache.org.", "default": "", "examples": ["com.acme"], "pattern": "^(.*)$"}, "name": {"type": "string", "title": "Component Name", "description": "The name of the component. This will often be a shortened, single name of the component. Examples: commons-lang3 and jquery", "default": "", "examples": ["tomcat-catalina"], "pattern": "^(.*)$"}, "version": {"type": "string", "title": "Component Version", "description": "The component version. The version should ideally comply with semantic versioning but is not enforced.", "default": "", "examples": ["9.0.14"], "pattern": "^(.*)$"}, "description": {"type": "string", "title": "Component Description", "description": "Specifies a description for the component", "default": "", "pattern": "^(.*)$"}, "scope": {"type": "string", "enum": ["required", "optional", "excluded"], "title": "Component <PERSON>", "description": "Specifies the scope of the component. If scope is not specified, 'required' scope should be assumed by the consumer of the BOM", "default": "required", "pattern": "^(.*)$"}, "hashes": {"type": "array", "title": "Component Hashes", "items": {"$ref": "#/definitions/hash"}}, "licenses": {"type": "array", "title": "Component License(s)", "items": {"properties": {"license": {"$ref": "#/definitions/license"}, "expression": {"type": "string", "title": "SPDX License Expression", "examples": ["Apache-2.0 AND (MIT OR GPL-2.0-only)", "GPL-3.0-only WITH Classpath-exception-2.0"], "pattern": "^(.*)$"}}, "oneOf": [{"required": ["license"]}, {"required": ["expression"]}]}}, "copyright": {"type": "string", "title": "Component Copyright", "description": "An optional copyright notice informing users of the underlying claims to copyright ownership in a published work.", "examples": ["Acme Inc"], "pattern": "^(.*)$"}, "cpe": {"type": "string", "title": "Component Common Platform Enumeration (CPE)", "description": "DEPRECATED - DO NOT USE. This will be removed in a future version. Specifies a well-formed CPE name. See https://nvd.nist.gov/products/cpe", "examples": ["cpe:2.3:a:acme:component_framework:-:*:*:*:*:*:*:*"], "pattern": "^(.*)$"}, "purl": {"type": "string", "title": "Component Package URL (purl)", "default": "", "examples": ["pkg:maven/com.acme/tomcat-catalina@9.0.14?packaging=jar"], "pattern": "^(.*)$"}, "swid": {"$ref": "#/definitions/swid", "title": "SWID Tag", "description": "Specifies metadata and content for ISO-IEC 19770-2 Software Identification (SWID) Tags."}, "modified": {"type": "boolean", "title": "Component Modified From Original", "description": "DEPRECATED - DO NOT USE. This will be removed in a future version. Use the pedigree element instead to supply information on exactly how the component was modified. A boolean value indicating is the component has been modified from the original. A value of true indicates the component is a derivative of the original. A value of false indicates the component has not been modified from the original."}, "pedigree": {"type": "object", "title": "Component Pedigree", "description": "Component pedigree is a way to document complex supply chain scenarios where components are created, distributed, modified, redistributed, combined with other components, etc. Pedigree supports viewing this complex chain from the beginning, the end, or anywhere in the middle. It also provides a way to document variants where the exact relation may not be known.", "properties": {"ancestors": {"type": "array", "title": "Ancestors", "description": "Describes zero or more components in which a component is derived from. This is commonly used to describe forks from existing projects where the forked version contains a ancestor node containing the original component it was forked from. For example, Component A is the original component. Component B is the component being used and documented in the BOM. However, Component B contains a pedigree node with a single ancestor documenting Component A - the original component from which Component B is derived from.", "items": {"$ref": "#/definitions/component"}}, "descendants": {"type": "array", "title": "Descendants", "description": "Descendants are the exact opposite of ancestors. This provides a way to document all forks (and their forks) of an original or root component.", "items": {"$ref": "#/definitions/component"}}, "variants": {"type": "array", "title": "Variants", "description": "Variants describe relations where the relationship between the components are not known. For example, if Component A contains nearly identical code to Component B. They are both related, but it is unclear if one is derived from the other, or if they share a common ancestor.", "items": {"$ref": "#/definitions/component"}}, "commits": {"type": "array", "title": "Commits", "description": "A list of zero or more commits which provide a trail describing how the component deviates from an ancestor, descendant, or variant.", "items": {"$ref": "#/definitions/commit"}}, "patches": {"type": "array", "title": "<PERSON><PERSON>", "description": ">A list of zero or more patches describing how the component deviates from an ancestor, descendant, or variant. Patches may be complimentary to commits or may be used in place of commits.", "items": {"$ref": "#/definitions/patch"}}, "notes": {"type": "string", "title": "Notes", "description": "Notes, observations, and other non-structured commentary describing the components pedigree.", "pattern": "^(.*)$"}}}, "externalReferences": {"type": "array", "items": {"$ref": "#/definitions/externalReference"}, "title": "External References"}, "components": {"$id": "#/definitions/component/properties/components", "type": "array", "items": {"$ref": "#/definitions/component"}, "uniqueItems": true, "title": "Components"}}}, "swid": {"type": "object", "title": "SWID Tag", "description": "Specifies metadata and content for ISO-IEC 19770-2 Software Identification (SWID) Tags.", "required": ["tagId", "name"], "properties": {"tagId": {"type": "string", "title": "Tag ID", "description": "Maps to the tagId of a SoftwareIdentity."}, "name": {"type": "string", "title": "Name", "description": "Maps to the name of a SoftwareIdentity."}, "version": {"type": "string", "title": "Version", "default": "0.0", "description": "Maps to the version of a SoftwareIdentity."}, "tagVersion": {"type": "integer", "title": "Tag Version", "default": 0, "description": "Maps to the tagVersion of a SoftwareIdentity."}, "patch": {"type": "boolean", "title": "Patch", "default": false, "description": "Maps to the patch of a SoftwareIdentity."}, "text": {"title": "Attachment text", "description": "Specifies the metadata and content of the SWID tag.", "$ref": "#/definitions/attachment"}, "url": {"type": "string", "title": "URL", "default": "The URL to the SWID file.", "pattern": "^(.*)$"}}}, "attachment": {"type": "object", "title": "Attachment", "description": "Specifies the metadata and content for an attachment.", "required": ["content"], "properties": {"contentType": {"type": "string", "title": "Content-Type", "description": "Specifies the content type of the text. Defaults to text/plain if not specified.", "default": "text/plain"}, "encoding": {"type": "string", "title": "Encoding", "description": "Specifies the optional encoding the text is represented in.", "enum": ["base64"], "default": "", "pattern": "^(.*)$"}, "content": {"type": "string", "title": "Attachment Text", "description": "The attachment data"}}}, "hash": {"type": "object", "title": "Hash Objects", "required": ["alg", "content"], "properties": {"alg": {"$ref": "#/definitions/hash-alg"}, "content": {"$ref": "#/definitions/hash-content"}}}, "hash-alg": {"type": "string", "enum": ["MD5", "SHA-1", "SHA-256", "SHA-384", "SHA-512", "SHA3-256", "SHA3-384", "SHA3-512", "BLAKE2b-256", "BLAKE2b-384", "BLAKE2b-512", "BLAKE3"], "title": "Hash Algorithm", "default": "", "pattern": "^(.*)$"}, "hash-content": {"type": "string", "title": "Hash Content (value)", "default": "", "examples": ["3942447fac867ae5cdb3229b658f4d48"], "pattern": "^([a-fA-F0-9]{32}|[a-fA-F0-9]{40}|[a-fA-F0-9]{64}|[a-fA-F0-9]{96}|[a-fA-F0-9]{128})$"}, "license": {"type": "object", "title": "License Object", "oneOf": [{"required": ["id"]}, {"required": ["name"]}], "properties": {"id": {"$ref": "spdx.SNAPSHOT.schema.json", "title": "License ID (SPDX)", "description": "A valid SPDX license ID", "examples": ["Apache-2.0"]}, "name": {"type": "string", "title": "License Name", "description": "If SPDX does not define the license used, this field may be used to provide the license name", "default": "", "examples": ["Acme Software License"], "pattern": "^(.*)$"}, "text": {"title": "License text", "description": "An optional way to include the textual content of a license.", "$ref": "#/definitions/attachment"}, "url": {"type": "string", "title": "License URL", "description": "The URL to the license file. If specified, a 'license' externalReference should also be specified for completeness", "examples": ["https://www.apache.org/licenses/LICENSE-2.0.txt"], "pattern": "^(.*)$"}}}, "commit": {"type": "object", "title": "Commit", "description": "Specifies an individual commit", "properties": {"uid": {"type": "string", "title": "UID", "description": "A unique identifier of the commit. This may be version control specific. For example, Subversion uses revision numbers whereas git uses commit hashes.", "pattern": "^(.*)$"}, "url": {"type": "string", "title": "URL", "description": "The URL to the commit. This URL will typically point to a commit in a version control system.", "format": "iri-reference"}, "author": {"title": "Author", "description": "The author who created the changes in the commit", "$ref": "#/definitions/identifiableAction"}, "committer": {"title": "Committer", "description": "The person who committed or pushed the commit", "$ref": "#/definitions/identifiableAction"}, "message": {"type": "string", "title": "Message", "description": "The text description of the contents of the commit", "pattern": "^(.*)$"}}}, "patch": {"type": "object", "title": "Patch", "description": "Specifies an individual patch", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["unofficial", "monkey", "backport", "cherry-pick"], "title": "Type", "description": "Specifies the purpose for the patch including the resolution of defects, security issues, or new behavior or functionality"}, "diff": {"title": "Diff", "description": "The patch file (or diff) that show changes. Refer to https://en.wikipedia.org/wiki/Diff", "$ref": "#/definitions/diff"}, "resolves": {"type": "array", "items": {"$ref": "#/definitions/issue"}, "title": "Resolves", "description": "A collection of issues the patch resolves"}}}, "diff": {"type": "object", "title": "Diff", "description": "The patch file (or diff) that show changes. Refer to https://en.wikipedia.org/wiki/Diff", "properties": {"text": {"title": "Diff text", "description": "Specifies the optional text of the diff", "$ref": "#/definitions/attachment"}, "url": {"type": "string", "title": "URL", "description": "Specifies the URL to the diff", "pattern": "^(.*)$"}}}, "issue": {"type": "object", "title": "Diff", "description": "The patch file (or diff) that show changes. Refer to https://en.wikipedia.org/wiki/Diff", "required": ["type"], "properties": {"type": {"type": "string", "enum": ["defect", "enhancement", "security"], "title": "Type", "description": "Specifies the type of issue"}, "id": {"type": "string", "title": "ID", "description": "The identifier of the issue assigned by the source of the issue", "pattern": "^(.*)$"}, "name": {"type": "string", "title": "Name", "description": "The name of the issue", "pattern": "^(.*)$"}, "description": {"type": "string", "title": "Description", "description": "A description of the issue", "pattern": "^(.*)$"}, "source": {"type": "object", "title": "Source", "description": "The source of the issue where it is documented", "properties": {"name": {"type": "string", "title": "Name", "description": "The name of the source. For example 'National Vulnerability Database', 'NVD', and 'Apache'", "pattern": "^(.*)$"}, "url": {"type": "string", "title": "URL", "description": "The url of the issue documentation as provided by the source", "pattern": "^(.*)$"}}}, "references": {"type": "array", "title": "References", "description": "A collection of URL's for reference. Multiple URLs are allowed.", "default": "", "examples": ["https://example.com"], "pattern": "^(.*)$"}}}, "identifiableAction": {"type": "object", "title": "Identifiable Action", "description": "Specifies an individual commit", "properties": {"timestamp": {"type": "string", "format": "date-time", "title": "Timestamp", "description": "The timestamp in which the action occurred"}, "name": {"type": "string", "title": "Name", "description": "The name of the individual who performed the action", "pattern": "^(.*)$"}, "email": {"type": "string", "format": "idn-email", "title": "E-mail", "description": "The email address of the individual who performed the action"}}}, "externalReference": {"type": "object", "title": "External Reference", "description": "Specifies an individual external reference", "required": ["url", "type"], "properties": {"url": {"type": "string", "title": "URL", "description": "The URL to the external reference", "pattern": "^(.*)$"}, "comment": {"type": "string", "title": "Comment", "description": "An optional comment describing the external reference", "pattern": "^(.*)$"}, "type": {"type": "string", "title": "Type", "description": "Specifies the type of external reference. There are built-in types to describe common references. If a type does not exist for the reference being referred to, use the \"other\" type.", "enum": ["vcs", "issue-tracker", "website", "advisories", "bom", "mailing-list", "social", "chat", "documentation", "support", "distribution", "license", "build-meta", "build-system", "other"]}}}, "dependency": {"type": "object", "title": "Dependency", "description": "Defines the direct dependencies of a component. Components that do not have their own dependencies MUST be declared as empty elements within the graph. Components that are not represented in the dependency graph MAY have unknown dependencies. It is RECOMMENDED that implementations assume this to be opaque and not an indicator of a component being dependency-free.", "required": ["ref"], "properties": {"ref": {"$ref": "#/definitions/refType", "format": "string", "title": "Reference", "description": "References a component by the components bom-ref attribute"}, "dependsOn": {"type": "array", "uniqueItems": true, "items": {"$ref": "#/definitions/refType"}, "title": "Depends On", "description": "The bom-ref identifiers of the components that are dependencies of this dependency object."}}}, "service": {"type": "object", "title": "Service Object", "required": ["name"], "properties": {"bom-ref": {"$ref": "#/definitions/refType", "title": "BOM Reference", "description": "An optional identifier which can be used to reference the service elsewhere in the BOM. Every bom-ref should be unique.", "default": "", "pattern": "^(.*)$"}, "provider": {"title": "Provider", "description": "The organization that provides the service.", "$ref": "#/definitions/organizationalEntity"}, "group": {"type": "string", "title": "Service Group", "description": "The grouping name, namespace, or identifier. This will often be a shortened, single name of the company or project that produced the service or domain name. Whitespace and special characters should be avoided.", "default": "", "examples": ["com.acme"], "pattern": "^(.*)$"}, "name": {"type": "string", "title": "Service Name", "description": "The name of the service. This will often be a shortened, single name of the service.", "default": "", "examples": ["ticker-service"], "pattern": "^(.*)$"}, "version": {"type": "string", "title": "Service Version", "description": "The service version.", "default": "", "examples": ["1.0.0"], "pattern": "^(.*)$"}, "description": {"type": "string", "title": "Service Description", "description": "Specifies a description for the service", "default": "", "pattern": "^(.*)$"}, "endpoints": {"type": "array", "title": "Endpoints", "description": "The endpoint URIs of the service. Multiple endpoints are allowed.", "default": "", "examples": ["https://example.com/api/v1/ticker"], "pattern": "^(.*)$"}, "authenticated": {"type": "boolean", "title": "Authentication Required", "description": "A boolean value indicating if the service requires authentication. A value of true indicates the service requires authentication prior to use. A value of false indicates the service does not require authentication."}, "x-trust-boundary": {"type": "boolean", "title": "Crosses Trust Boundary", "description": "A boolean value indicating if use of the service crosses a trust zone or boundary. A value of true indicates that by using the service, a trust boundary is crossed. A value of false indicates that by using the service, a trust boundary is not crossed."}, "data": {"type": "array", "items": {"$ref": "#/definitions/dataClassification"}, "title": "Data Classification", "description": "Specifies the data classification."}, "licenses": {"type": "array", "title": "Component License(s)", "items": {"properties": {"license": {"$ref": "#/definitions/license"}, "expression": {"type": "string", "title": "SPDX License Expression", "examples": ["Apache-2.0 AND (MIT OR GPL-2.0-only)", "GPL-3.0-only WITH Classpath-exception-2.0"], "pattern": "^(.*)$"}}, "oneOf": [{"required": ["license"]}, {"required": ["expression"]}]}}, "externalReferences": {"type": "array", "items": {"$ref": "#/definitions/externalReference"}, "title": "External References"}, "services": {"$id": "#/definitions/service/properties/services", "type": "array", "items": {"$ref": "#/definitions/service"}, "uniqueItems": true, "title": "Services"}}}, "dataClassification": {"type": "object", "title": "Hash Objects", "required": ["flow", "classification"], "properties": {"flow": {"$ref": "#/definitions/dataFlow"}, "classification": {"type": "string"}}}, "dataFlow": {"type": "string", "enum": ["inbound", "outbound", "bi-directional", "unknown"], "title": "Data flow direction", "default": "", "pattern": "^(.*)$"}}}