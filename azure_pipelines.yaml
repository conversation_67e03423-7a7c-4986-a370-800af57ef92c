trigger:
- development

pool:
  vmImage: 'ubuntu-latest'

variables:
  # --- Variables for the Login Step ---
  # These should be set in the Azure DevOps Pipeline Variables UI.
  # IMPORTANT: Make sure to set ACR_PASSWORD as a secret (click the lock icon).
  # ACR_USERNAME: # e.g., 'mlomiddleware' (if using admin user)
  # ACR_PASSWORD: # The password for the admin user

  # --- Variables for the rest of the pipeline ---
  ACR_SERVER_URL: 'mlomiddleware.azurecr.io'
  IMAGE_NAME: 'ava-plus-experience-studio-server-dev'
  RESOURCE_GROUP: 'vizcheck' 
  WEBAPP_RESOURCE_GROUP: 'AscendionGenAI'

steps:
# STEP 1: Login to ACR (This step is successful)
- script: |
    echo "$(ACR_PASSWORD)" | docker login $(ACR_SERVER_URL) -u $(ACR_USERNAME) --password-stdin
    echo "ACR login successful."
  displayName: 'Login to ACR with Username and Password'
  env:
    ACR_USERNAME: $(ACR_USERNAME)
    ACR_PASSWORD: $(ACR_PASSWORD)
    ACR_SERVER_URL: $(ACR_SERVER_URL)

# STEP 2 (Alternative): Manually build, tag, and push the image
- script: |
    # Define the full image name with the registry URL and a unique tag
    IMAGE_FULL_NAME="$(ACR_SERVER_URL)/$(IMAGE_NAME):$(Build.BuildId)"
    
    echo "Building image and tagging as: ${IMAGE_FULL_NAME}"
    
    # Build the image and apply the tag
    docker build -t "${IMAGE_FULL_NAME}" .
    
    echo "Pushing image: ${IMAGE_FULL_NAME}"
    
    # Push the image to the container registry
    docker push "${IMAGE_FULL_NAME}"
  displayName: 'Build and Push with Docker commands'
  env:
    # We need to pass these variables to the script again
    ACR_SERVER_URL: $(ACR_SERVER_URL)
    IMAGE_NAME: $(IMAGE_NAME)
    Build.BuildId: $(Build.BuildId)

# STEP 3 (Corrected): Deploy to Azure Web App
- task: AzureCLI@2
  displayName: 'Deploy to Azure Web App using Managed Identity'
  inputs:
    azureSubscription: $(WEB_APP_AZURE_SUBSCRIPTION)
    scriptType: 'bash'
    scriptLocation: 'inlineScript'
    inlineScript: |
      echo "Enabling Managed Identity pull from ACR for the Web App..."
      # CORRECTED: Using the correct resource group for the web app
      az webapp config appsettings set --resource-group avaplus --name ava-plus-experience-studio-api-dev --settings DOCKER_REGISTRY_SERVER_URL=https://$(ACR_SERVER_URL)

      echo "Deploying image with tag: $(Build.BuildId)"
      # CORRECTED: Using the correct resource group AND the newer, non-deprecated option
      az webapp config container set \
        --name ava-plus-experience-studio-api-dev \
        --resource-group avaplus \
        --container-image-name $(ACR_SERVER_URL)/$(IMAGE_NAME):$(Build.BuildId)