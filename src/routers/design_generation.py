import asyncio
import datetime
import json
import os
import uuid


from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException, Request
from fastapi.params import Query
from fastapi.responses import JSONResponse

from src.core.sse_pubsub import RedisStreamPublisher
from src.dependencies import get_current_user
from src.model.request_schemas import (
    AppProgressEnum,
    CodeGenerationRequest,
    ProjectStatusEnum,
)
from src.routers.code_generation import (
    build_and_deploy,
    persist_data_to_db,
    publish_and_get_state,
    update_conversation_with_metadata,
)
from src.services.database.project_service import ProjectDatabaseService
from src.services.design_generation.design_generation_service import (
    DesignGenerationService,
)
from src.services.generation.generation_factory import GenerationFactory

# Azure services imports
from src.services.azure.ado_helper import AdoHelper
from src.services.update_media import process_media_requests
from src.utils.design_system_parser import convert_tailwind_to_design_tokens
from src.settings.settings import Settings
from src.utils.decorators.avaplus_decorator import handle_exceptions_and_log
from src.utils.logger import AppLogger
from src.utils.helpers.name_formatter import format_repository_name

router = APIRouter()


# Create helper functions for lazy initialization
def get_design_generator():
    settings = Settings()
    provider = GenerationFactory.get_generation("avaplus")
    return DesignGenerationService(provider, settings.da_creds)


def get_settings():
    return Settings()


logger = AppLogger(__name__).get_logger()

# Create a new project in the database
project_db_service = ProjectDatabaseService()


def extract_design_tokens_from_code_blocks(code_blocks):
    """Extract design tokens from code blocks containing CSS and Tailwind config."""
    try:
        logger.info(
            f"Starting design token extraction from {len(code_blocks)} code blocks"
        )
        files_dict = {}
        for block in code_blocks:
            if isinstance(block, dict) and "fileName" in block and "content" in block:
                files_dict[block["fileName"]] = block["content"]

        # logger.info(f"Extracted {files_dict} files from code blocks")
        if "src/index.css" in files_dict and "tailwind.config.ts" in files_dict:
            # logger.info(f"Extracting design tokens from CSS and Tailwind config: types {type(files_dict['src/index.css'])}, {type(files_dict['tailwind.config.ts'])}")
            design_tokens = convert_tailwind_to_design_tokens(files_dict)

            # Ensure design_tokens is a dictionary
            if isinstance(design_tokens, str):
                try:
                    design_tokens = json.loads(design_tokens)
                except json.JSONDecodeError as e:
                    logger.error(f"Failed to decode design tokens JSON: {e}")
                    return {}

            if isinstance(design_tokens, dict) and "design_tokens" in design_tokens:
                token_data = design_tokens["design_tokens"]
                color_count = len(token_data.get("colors", []))
                typography_count = len(token_data.get("typography", []))
                spacing_count = len(token_data.get("spacing", []))
                logger.info(
                    f"Design tokens extracted - Colors: {color_count}, Typography: {typography_count}, Spacing: {spacing_count}"
                )
            else:
                logger.warning("No design tokens found in extracted data")

            return design_tokens
        else:
            missing_files = []
            if "src/index.css" not in files_dict:
                missing_files.append("src/index.css")
            if "tailwind.config.ts" not in files_dict:
                missing_files.append("tailwind.config.ts")
            logger.warning(
                f"Missing required files for token extraction: {missing_files}"
            )
            return {}
    except Exception as e:
        logger.error(f"Design token extraction failed: {e}")
        return {}


async def run_path_a(
    request_id: str,
    visual_interaction_design: str,
    project_id: str,
    request: dict,
    states: list,
    redis_stream_publisher: RedisStreamPublisher,
):
    """Runs step 2 sequentially."""
    logger.info(f"Path A - CSS & Config Generation started | Request ID: {request_id}")
    logger.debug(
        f"Framework: {request.get('framework')}, Design Library: {request.get('design_library')}"
    )

    # STEP 2: CSS & CONFIG Generator
    try:
        # logger.info(f"Calling CSS & CONFIG Generator Agent | Request ID: {request_id}")
        design_start_time = datetime.datetime.now()

        state = {
            "status": ProjectStatusEnum.IN_PROGRESS,
            "progress": AppProgressEnum.DESIGN_SYSTEM_MAPPED,
            "log": f"Agent : Design System Architect | {request['framework']} | {request['design_library']}\nAction: Creating comprehensive design system with color palettes, typography, and component styles",
            "progress_description": f"Generating professional design system foundation using {request['design_library']} components. Creating consistent color schemes, typography scales, spacing systems, and reusable style patterns for cohesive user interface design.",
            "event_type": "initial-code-gen",
            "metadata": [],
        }
        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )

        # Process the raw response from the Prompt Enhancer
        css_config_raw_response: str = (
            await get_design_generator().EE_MLO_CSS_CONFIG_GENERATOR(
                visual_interaction_design
            )
        )

        if not css_config_raw_response:
            logger.error(
                f"CSS & CONFIG Generator returned empty response | Request ID: {request_id}"
            )
            raise HTTPException(
                status_code=500,
                detail="CSS & CONFIG Generator Agent returned empty response",
            )

        logger.info(
            f"CSS & CONFIG Generator response received ✅ | Request ID: {request_id}"
        )
        # DebugFileLogger.log_to_file(f"path_a_raw_response_{request_id}.txt", css_config_raw_response)

        # Parse the raw response to extract CSS, Config, and Detailed Steps
        parsed_response: json = get_design_generator().parse_text_context(
            css_config_raw_response
        )

        css_config_code: list = parsed_response.get("code_blocks", [])
        detailed_steps_css_config: dict = parsed_response.get("narrative_steps", {})

        # Extract design tokens from the generated CSS and config files
        design_tokens = extract_design_tokens_from_code_blocks(css_config_code)
        # DebugFileLogger.log_to_file(f"path_a_design_tokens_{request_id}.json", design_tokens)
        # steps
        # DebugFileLogger.log_to_file(f"path_a_detailed_steps_{request_id}.json", detailed_steps_css_config)

        # Add COMPLETED state for design system generation
        # design_token_count = 0
        # if design_tokens and "design_tokens" in design_tokens:
        #     token_data = design_tokens["design_tokens"]
        #     design_token_count = sum(
        #         len(tokens) if isinstance(tokens, list) else 0
        #         for tokens in token_data.values()
        #     )

        state = {
            "status": ProjectStatusEnum.COMPLETED,
            "progress": AppProgressEnum.DESIGN_SYSTEM_MAPPED,
            "log": f"Agent : Design System Architect | {request['design_library']} Design System\nAction: Design system generation completed successfully",
            "progress_description": f"{detailed_steps_css_config['step_3']}",
            "event_type": "initial-code-gen",
            "metadata": [
                {
                    "type": "artifact",
                    "data": {
                        "data": design_tokens,
                        "type": "json",
                    },
                },
                {"type": "files", "data": css_config_code},
            ],
        }

        logger.info(
            f"Path A - CSS & CONFIG Generation completed | Request ID: {request_id}"
        )
        return css_config_code, detailed_steps_css_config

    except Exception as e:
        logger.error(f"Path A - CSS & CONFIG Generation failed: {str(e)}")
        state["status"] = ProjectStatusEnum.FAILED

        raise HTTPException(status_code=500, detail=f"Error in Step 2: {str(e)}")

    finally:
        end_time = datetime.datetime.now()

        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )
        duration_seconds = (end_time - design_start_time).total_seconds()
        logger.info(
            f"Path A completed in {duration_seconds/60:.2f} minutes | Request ID: {request_id}"
        )


async def run_path_b(
    request_id: str,
    project_info: str,
    app_tsx: str,
    component_required: str,
    layout_component_required: str,
    user_journey: str,
    project_id: str,
    states: list,
    redis_stream_publisher: RedisStreamPublisher,
):
    """Run step 3 (Component Generation) with parallel calls for each component."""
    logger.info(f"Path B - Component Generation started | Request ID: {request_id}")
    logger.debug(
        f"Components required: {list(component_required.keys()) if component_required else []}"
    )
    component_start_time = datetime.datetime.now()

    # Helper function to generate a single component
    async def generate_single_component(
        component_name: str,  # Name of the component
        component_description: str,  # Specific requirement/description for this component
        full_components_requirements_context: str,  # The original, complete component_required JSON string
    ):
        """Generate code for a single component."""
        logger.info(
            f"Generating component: {component_name} | Request ID: {request_id}"
        )

        component_payload = (
            f"**1. <project-overview>\n{project_info}\n</project-overview>\n"
            f"**2. <app_tsx_content>\n{app_tsx}\n</app_tsx_content>\n"
            f"**3. <component-to-generate-name>\n{component_name}\n</component-to-generate-name>\n"
            f"**4. <component-to-generate-description>\n{component_description}\n</component-to-generate-description>\n"
            f"**5. <already-generated-components>\n{full_components_requirements_context}\n</already-generated-components>\n"
            f"**6. <user-journey-mapping>\n{user_journey}\n</user-journey-mapping>\n"
        )

        # DebugFileLogger.log_to_file(f"component_payload_{component_name}_{request_id}.txt", component_payload)

        component_raw_data: str = (
            await get_design_generator().EE_MLO_COMPONENT_CODE_GENERATOR(
                component_payload
            )
        )

        if not component_raw_data:
            logger.error(
                f"Component Generator returned empty response for: {component_name} | Request ID: {request_id}"
            )
            return None

        # DebugFileLogger.log_to_file(f"component_raw_{component_name}_{request_id}.txt", component_raw_data)

        parsed_response: dict = get_design_generator().parse_text_context(
            component_raw_data
        )
        component_code_blocks: list = parsed_response.get("code_blocks", [])
        narrative_steps: dict = parsed_response.get("narrative_steps", {})

        logger.info(
            f"Component {component_name} generated ✅ | Request ID: {request_id}"
        )
        # DebugFileLogger.log_to_file(f"component_blocks_{component_name}_{request_id}.json", component_code_blocks)

        return component_code_blocks, narrative_steps

    # STEP 3: Components Generation (Parallel)
    try:
        logger.info(f"Component Generator started | Request ID: {request_id}")

        individual_component_tasks = component_required

        if not individual_component_tasks:
            logger.error("No component requirements found")
            raise HTTPException(
                status_code=400,
                detail="Component requirements are empty, malformed, or could not be parsed.",
            )

        logger.info(
            f"Generating {len(individual_component_tasks.keys())} components in parallel: {list(individual_component_tasks.keys())}"
        )

        state = {
            "progress": AppProgressEnum.COMPONENTS_CREATED,
            "status": ProjectStatusEnum.IN_PROGRESS,
            "log": f"Agent : Component Engineer | {len(individual_component_tasks)} Components | Sequential Generation\nAction: Building interactive UI components with design system integration",
            "progress_description": f"Creating {', '.join([name for name, _ in individual_component_tasks.items()])} custom components for your application. Each component is built with responsive design, accessibility features, and seamless integration with the established design system.",
            "event_type": "initial-code-gen",
            "metadata": [],
        }
        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )

        # Create tasks for parallel execution
        component_gen_tasks = []
        for comp_name, comp_desc in individual_component_tasks.items():
            # Create a modified context by removing the current key
            modified_context = {
                key: value
                for key, value in component_required.items()
                if key != comp_name
            }
            final_context = {**modified_context, **layout_component_required}
            # Create the task with the modified context
            task = generate_single_component(
                component_name=comp_name,
                component_description=comp_desc,
                full_components_requirements_context=final_context,
            )
            component_gen_tasks.append(task)

        # Execute tasks concurrently and collect results
        task_results = await asyncio.gather(
            *component_gen_tasks, return_exceptions=True
        )

        # 4. Process results
        valid_results = [
            result
            for result in task_results
            if isinstance(result, tuple) and len(result) == 2
        ]

        # Log and handle exceptions or unexpected results
        for result in task_results:
            if isinstance(result, Exception):
                logger.error(f"Error in Page Code Generator task: {str(result)}")
            elif not isinstance(result, tuple) or len(result) != 2:
                logger.error(f"Unexpected result format: {result}")

        # Extract response codes and detailed steps
        all_component_response_code = [result[0] for result in valid_results]
        all_detailed_steps_component_narratives = [
            result[1] for result in valid_results
        ]

        # # DebugFileLogger.log_to_file("component_code.json", all_component_response_code)
        # DebugFileLogger.log_to_file("component_detailed_steps.json", all_detailed_steps_component_narratives)

        # Check if no components were generated
        if not all_component_response_code:
            logger.error("Component Generator Agent failed to generate any components.")
            raise HTTPException(
                status_code=500,
                detail="Component Generator Agent produced no component code.",
            )

        logger.debug(
            "All component generation tasks completed ✅. Aggregating results."
        )

        # Aggregate all page codes
        all_component_code = [
            code
            for response_code in all_component_response_code
            if response_code
            for code in response_code
        ]

        # Log empty responses for specific pages
        for i, response_code in enumerate(all_component_response_code):
            if not response_code:
                page_name = list(individual_component_tasks.keys())[i]
                logger.error(f"Empty response for page {page_name}")

        # Check if no page codes were generated
        if not all_component_code:
            logger.error("All page generation tasks failed")
            raise HTTPException(
                status_code=500, detail="Page Code Generator failed for all pages"
            )

        # detailed summary of all the narrative steps we will take step_3 of all the narratiave steps and save as a f string
        if all_detailed_steps_component_narratives:
            final_detailed_steps_summary = {
                "step_1": "\n".join(
                    [
                        f"* {narrative.get('step_1', 'No details provided')}"
                        for narrative in all_detailed_steps_component_narratives
                        if "step_1" in narrative
                    ]
                ),
                "step_2": "\n".join(
                    [
                        f"* {narrative.get('step_2', 'No details provided')}"
                        for narrative in all_detailed_steps_component_narratives
                        if "step_2" in narrative
                    ]
                ),
                "step_3": "\n".join(
                    [
                        f"* {narrative.get('step_3', 'No details provided')}"
                        for narrative in all_detailed_steps_component_narratives
                        if "step_3" in narrative
                    ]
                ),
                "step_4": "\n".join(
                    [
                        f"* {narrative.get('step_4', 'No details provided')}"
                        for narrative in all_detailed_steps_component_narratives
                        if "step_4" in narrative
                    ]
                ),
            }

        # DebugFileLogger.log_to_file(f"path_b_detailed_steps_{request_id}.txt", final_detailed_steps_summary["step_3"])
        # final_detailed_steps_summary["step_3"] = final_detailed_steps_summary["step_3"].replace("\\n", "")

        # Add COMPLETED state for component generation
        state = states.pop()
        state["progress"] = AppProgressEnum.COMPONENTS_CREATED
        state["status"] = ProjectStatusEnum.COMPLETED
        state["log"] = (
            f"Agent : Component Engineer | {len(individual_component_tasks)} Components Generated\nAction: Component generation completed successfully"
        )
        state["progress_description"] = f"{final_detailed_steps_summary['step_3']}"
        state["event_type"] = "initial-code-gen"
        state["metadata"] = [{"type": "files", "data": all_component_code}]

        logger.info(
            f"Path B - Component Generation completed | Generated {len(all_component_code)} code blocks"
        )
        return (all_component_code, final_detailed_steps_summary)

    except Exception as e:
        state["status"] = ProjectStatusEnum.FAILED
        logger.error(f"Path B - Component Generation failed: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error in Step 3: {str(e)}")

    finally:
        end_time = datetime.datetime.now()
        duration_seconds = (end_time - component_start_time).total_seconds()

        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )
        logger.info(
            f"Path B completed in {duration_seconds/60:.2f} minutes | Request ID: {request_id}"
        )


async def run_path_c(
    request_id: str,
    project_info: str,
    app_tsx: str,
    layout_required: str,
    component_required: str,
    user_journey: str,
    global_structure: str,
    project_id: str,
    states: list,
    redis_stream_publisher: RedisStreamPublisher,
):
    """Runs step 4 sequentially, receiving data from Path A and Path B."""
    logger.info(f"START | Design | Path C (Step 4) | Request ID: {request_id}")

    # STEP 4: Layout Generator
    try:
        logger.info("START | Design | STEP 4 | Agent: Layout Generator")

        layout_start_time = datetime.datetime.now()

        state = {
            "progress": AppProgressEnum.LAYOUT_ANALYZED,
            "status": ProjectStatusEnum.IN_PROGRESS,
            "log": f"Agent : Layout Architect | {len(layout_required)} Layout Components | Structural Design\nAction: Creating application layout structure with navigation and content organization",
            "progress_description": f"Creating {', '.join([name for name, _ in layout_required.items()])} layout components that define your application's structure.",
            "event_type": "initial-code-gen",
            "metadata": [],
        }

        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )

        individual_layout_components = layout_required
        if isinstance(individual_layout_components, str):
            individual_layout_components = json.loads(individual_layout_components)
        logger.info(
            f"Identified {len(individual_layout_components.keys())} Layout Components tasks for sequential generation: {', '.join([name for name, _ in individual_layout_components.items()])}"
        )

        # STEP 4: Layout Generation
        layout_payload = (
            f"**1. <project-overview>\n{project_info}\n</project-overview>\n"
            f"**2. <app_tsx_content>\n{app_tsx}\n</app_tsx_content>\n"
            f"**3. <layout-components-to-generate>\n{layout_required}\n</layout-components-to-generate>\n"
            f"**4. <already-generated-components>\n{component_required}\n</already-generated-components>\n"  # Context of all components
            f"**5. <user-journey-mapping>\n{user_journey}\n</user-journey-mapping>\n"
        )

        layout_raw_data: str = (
            await get_design_generator().EE_MLO_LAYOUT_CODE_GENERATOR(layout_payload)
        )

        if not layout_raw_data:
            logger.error("Layout Generator Agent returned False/None")
            raise HTTPException(
                status_code=500, detail="Layout Generator Agent returned empty response"
            )

        logger.debug("Layout Generator Agent response successfully received ✅ ")
        logger.info("END | Design | STEP 4 | Agent: Layout Generator")
        # DebugFileLogger.log_to_file(f"layout_raw_response_{request_id}.txt", layout_raw_data)
        # DebugFileLogger.log_to_file("logger_files/component.json", components_data)

        # Parse the raw response to extract component data
        logger.debug("START | Parsing Layout Generator response")
        parsed_response: json = get_design_generator().parse_text_context(
            layout_raw_data
        )

        layout_response_code: list = parsed_response.get("code_blocks", [])
        detailed_steps_layout: dict = parsed_response.get("narrative_steps", {})

        logger.debug("END | Parsing Layout Generator Response")

        # logger.info(f" Hi {detailed_steps_layout['step_3']}. \nSuccessfully generated:\n {', '.join([name for name, _ in individual_layout_components.items()])} layout components that define your application structure. Created responsive navigation, content areas, and organizational elements with optimal user experience design.")
        # logger.info(type(f" Hi {detailed_steps_layout['step_3']}. \nSuccessfully generated:\n {', '.join([name for name, _ in individual_layout_components.items()])} layout components that define your application structure. Created responsive navigation, content areas, and organizational elements with optimal user experience design."))
        # steps
        # DebugFileLogger.log_to_file(
        #     f"path_c_detailed_steps_{request_id}.json", detailed_steps_layout
        # )

        # Add COMPLETED state for layout generation
        # Put the layout in artifact metadata
        state = states.pop()
        state["progress"] = AppProgressEnum.LAYOUT_ANALYZED
        state["status"] = ProjectStatusEnum.COMPLETED
        state["log"] = (
            f"Agent : Layout Architect | {len(individual_layout_components)} Layout Components Generated\nAction: Layout generation completed successfully"
        )
        state["progress_description"] = f"{detailed_steps_layout['step_3']}"
        state["event_type"] = "initial-code-gen"
        state["metadata"] = [
            {
                "type": "artifact",
                "data": {"type": "text", "data": f"{global_structure}"},
            },
            {"type": "files", "data": layout_response_code},
        ]

        logger.info(
            f"END | Design | STEP 4 | Agent: Layout Generator ✅ | Generated {len(layout_response_code)} Total Layout Components."
        )
        return (layout_response_code, detailed_steps_layout)

    except Exception as e:
        state["status"] = ProjectStatusEnum.FAILED
        logger.error(f"Error in Layout Generator Agent (Step 4): {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error in Step 4: {str(e)}")

    finally:
        end_time = datetime.datetime.now()
        duration_seconds = (end_time - layout_start_time).total_seconds()
        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )
        logger.info(
            f"END | Design | Path C (Step 4) | Total Duration For Layout Generation ⏰: {(duration_seconds)/60:.2f} minutes"
        )


@handle_exceptions_and_log(logger)
@router.post("/design-generation")
async def generate_design_generation(
    request: CodeGenerationRequest,
    background_tasks: BackgroundTasks,
    request_obj: Request,
    user: str = Depends(get_current_user),
    user_signature: str = Query(...),
):  # Use MockReactCodeRequest or your actual ReactCodeRequest
    """
    Generate a design based on the provided request, with parallel execution paths.
    """

    project_id = await project_db_service.create_project(
        project_name=request.project_name,
        project_description=request.project_description,
        # project_type=request.project_type,
        user_id=user,
        project_state=request.project_state,
    )

    # Store project settings
    await project_db_service.create_or_update_project_settings(
        project_id=project_id,
        device=request.platform,
        framework=request.framework,
        design_system=request.design_library,
        generation_type=request.project_type,
    )

    request_id = str(uuid.uuid4())

    redis_publisher = request_obj.app.state.redis_stream_publisher
    request_data: dict = request.model_dump()
    request_data["user_input"] = request_data["project_description"]
    request_data["userSignature"] = user_signature

    logger.info("START | Design | Full Design generation process initiated")
    logger.debug(
        f"-- Input: Framework={request.framework}, Design Library={request.design_library}"
    )

    background_tasks.add_task(
        process_design_generation,
        request_data,
        project_id,
        user,
        user_signature,
        request_id,
        redis_publisher,
    )
    return JSONResponse(
        status_code=202, content={"job_id": request_id, "project_id": project_id}
    )


@handle_exceptions_and_log(logger)
async def process_design_generation(
    request_data,
    project_id,
    user,
    user_signature,
    request_id,
    start_time,
    redis_stream_publisher: RedisStreamPublisher,
):
    # Get instances when needed
    design_generator = get_design_generator()

    state = {}
    states = []
    # ... rest of function
    """
    Main background task for the design generation process.
    This function now uses a single top-level try/except block to ensure
    that any failure, at any step, is caught and the project status is
    updated to FAILED in the database.
    """
    logger.info(
        f"Design Generation Process started | Request ID: {request_id} | Project ID: {project_id}"
    )
    logger.debug(
        f"Framework: {request_data.get('framework')}, Design Library: {request_data.get('design_library')}"
    )
    # DebugFileLogger.log_to_file(f"process_start_{request_id}.json"

    # It will be updated progressively through the `try` block.
    states = []

    event_type = "initial-code-gen"

    state = {
        "status": ProjectStatusEnum.PENDING,
        "progress": AppProgressEnum.OVERVIEW,
        "log": f"Agent : Project Analyzer | {request_data['framework']} | {request_data['design_library']}\nAction: Analyzing project requirements and initializing workspace",
        "progress_description": f"Analyzing your project requirements for {request_data['framework']} application with {request_data['design_library']} design system. Setting up development environment and preparing code generation pipeline.",
        "metadata": [],
        "event_type": event_type,
    }
    states = [state]

    await project_db_service.create_project_status(
        project_id,
        request_id,
        states[0]["status"],
        states[0]["log"],
        states[0]["progress"],
        states[0]["progress_description"],
        json.dumps(states[0]["metadata"]),
    )

    await redis_stream_publisher.publish_project_update(request_id, state)

    logger.debug(f"-- Request ID: {request_id}")
    logger.info("START | App Generation | Code generation process initiated")
    logger.debug(
        f"-- Input: Framework={request_data['framework']}, Design Library={request_data['design_library']}"
    )
    project_brief = await design_generator.generate_project_brief(request_data)
    await project_db_service.update_project_name(
        project_id, project_brief["name"], project_brief["description"]
    )

    # Format repository name using the utility function
    repo_name, final_name_part = format_repository_name(
        project_brief["name"], str(user), project_id
    )

    states.pop()
    state = {
        "status": ProjectStatusEnum.IN_PROGRESS,
        "progress": AppProgressEnum.OVERVIEW,
        "log": f"Agent : Project Architect | {request_data['framework']} | {request_data['design_library']}\nAction: Project analysis completed, generating development roadmap",
        "progress_description": f"Project '{project_brief['name']}' successfully analyzed. Created comprehensive development plan for {request_data['framework']} application with {request_data['design_library']} design system. Ready to begin code generation process.",
        "metadata": [
            {
                "type": "artifact",
                "data": {"type": "text", "data": project_brief["overview"]},
            },
            {
                "type": "ref_code",
                "data": f"{project_brief['name']}",
            },
        ],
        "event_type": event_type,
    }

    try:
        # STEP 1: Prompt Enhancer (Common Prerequisite)
        state = {
            "status": ProjectStatusEnum.IN_PROGRESS,
            "progress": AppProgressEnum.OVERVIEW,
            "log": f"Agent : Project Architect | {request_data['framework']} | {request_data['design_library']}\nAction: Project analysis completed, generating development roadmap",
            "progress_description": f"Project '{project_brief['name']}' successfully analyzed. Created comprehensive development plan for {request_data['framework']} application with {request_data['design_library']} design system. Ready to begin code generation process.",
            "metadata": [
                {
                    "type": "artifact",
                    "data": {"type": "text", "data": project_brief["overview"]},
                },
                {
                    "type": "ref_code",
                    "data": f"{project_brief['name']}",
                },
            ],
            "event_type": event_type,
        }
        await publish_and_get_state(redis_stream_publisher, request_id, state, states)
        enhancer_start_time = datetime.datetime.now()
        try:
            logger.info(f"Step 1 - Prompt Enhancer started | Request ID: {request_id}")

            prompt_payload = (
                f"**1. <project-name>\n{project_brief['name']}\n</project-name>\n"
                f"**2. <user-input>\n{request_data['project_description']}\n</user-input>\n"
            )

            final_json_payload = {
                "requirements": prompt_payload,
                "userSignature": user_signature,
            }

            enhanced_prompt_data: str = await design_generator.EE_MLO_PROMPT_ENHANCER(
                final_json_payload
            )

            if not enhanced_prompt_data:
                logger.error(
                    f"Prompt Enhancer returned empty response | Request ID: {request_id}"
                )
                raise HTTPException(
                    status_code=500,
                    detail="Prompt Enhancer failed to generate a response.",
                )

            logger.info(
                f"Step 1 - Prompt Enhancer completed | Request ID: {request_id}"
            )
            # DebugFileLogger.log_to_file(f"step1_prompt_enhancer_{request_id}.txt", enhanced_prompt_data)

            logger.info(f"Extracting application info | Request ID: {request_id}")
            (
                project_info,
                visual_text_info,
                layout_component_required_info,
                components_required_info,
                layout_info,
                page_type_info,
                user_journey_info,
                target_audience,
                global_structure,
            ) = design_generator.application_info(enhanced_prompt_data)
            information = project_info.removeprefix("## Project Description")
            # logger.info(f"Extracted project information: {information}... | Request ID: {request_id}")

            # State update to show the project info, target audience, and user journey mapping data
            payload_data_artifacts = f"## {project_brief['name']}\n{information}\n \n {target_audience}\n \n {user_journey_info}"
            state = {
                "status": ProjectStatusEnum.COMPLETED,
                "progress": AppProgressEnum.OVERVIEW,
                "log": f"Agent : Application Info Extractor | {request_data['framework']} | {request_data['design_library']}\nAction: Extracted application information and user journey",
                "progress_description": information,
                "event_type": event_type,
                "metadata": [
                    {
                        "type": "artifact",
                        "data": {"type": "text", "data": payload_data_artifacts},
                    },
                    {"type": "ref_code", "data": f"{project_brief['name']}"},
                ],
            }

        except Exception as e:
            state["status"] = ProjectStatusEnum.FAILED
            logger.error(
                f"Step 1 - Prompt Enhancer failed: {str(e)} | Request ID: {request_id}"
            )
            raise HTTPException(status_code=500, detail=f"Error in Step 1: {str(e)}")

        finally:
            end_time = datetime.datetime.now()
            states = await publish_and_get_state(
                redis_stream_publisher, request_id, state, states
            )
            duration_seconds = (end_time - enhancer_start_time).total_seconds()
            logger.info(f"Step 1 duration: {duration_seconds/60:.2f} minutes")

        # Extract application info from enhanced prompt

        state = {
            "status": ProjectStatusEnum.IN_PROGRESS,
            "progress": AppProgressEnum.SEED_PROJECT_INITIALIZED,
            "log": f"Agent : Infrastructure Manager | {request_data['framework']} | {request_data['design_library']}\nAction: Setting up your repository and configuring deployment pipeline",
            "progress_description": f"Development infrastructure successfully configured. Repository created with {request_data['framework']} template and {request_data['design_library']} integration. Deployment pipeline established for continuous delivery. Project foundation ready for code generation.",
            "event_type": event_type,
            "metadata": [],
        }

        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )

        logger.info(f"Infrastructure setup started | Request ID: {request_id}")

        logger.info(f"Infrastructure setup started | Request ID: {request_id}")

        # Azure DevOps implementation - create seed project
        settings = get_settings()
        template_name = f"{request_data['framework'].capitalize()}_{request_data['design_library'].capitalize()}css_Template"

        # Initialize Azure DevOps helper
        ado_helper = AdoHelper(repo_name, "azure-pipelines.yaml")

        swa_app_name = (
            f"ExperienceStudio-{str(user)[-4:]}"
            if settings.devConfig.DEPLOY_PER_USER == "true"
            else "sample-swa-test-2"
        )
        # Create Azure DevOps repository from template (seed project)
        repo_result = await ado_helper.create_repository_and_deploy(
            repository_name=repo_name,
            from_seed=True,
            seed_repository_name=template_name,
            swa_app_name=swa_app_name,
        )

        # Extract commit hash and repository details
        commit_sha = repo_result["commit_hash"]
        repo_details = repo_result["repository"]
        # pipeline_details = repo_result["pipeline"]
        # build_info = repo_result["build"]

        logger.info(f"Seed project created - Repo: {repo_name}, Commit: {commit_sha}")
        if repo_details:
            await project_db_service.create_or_update_repository_details(
                project_id=project_id,
                vcs_provider="azure_devops",
                clone_url=repo_details["remote_url"],  # HTTPS clone URL
                deployment_provider="azure_static_web_apps",
                deployed_url=None,  # Will be updated later when deployment is complete
            )
        # Note: Azure pipeline will automatically create Static Web App with random URL
        # URL will be available in Azure portal after deployment completes

        state = {
            "status": ProjectStatusEnum.COMPLETED,
            "progress": AppProgressEnum.SEED_PROJECT_INITIALIZED,
            "log": "Senior Code Architect | Repository created successfully",
            "progress_description": f"Created repository {repo_name} and initialized with template code",
            "metadata": [
                {
                    "type": "repository",
                    "name": repo_name,
                    "url": repo_details["web_url"] if repo_details else None,
                    "clone_url": repo_details["remote_url"] if repo_details else None,
                    "commit_sha": commit_sha,
                },
            ],
            "project_id": project_id,  # Include project_id in the state
        }

        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )

        # Sequential execution of code generation paths
        logger.info(
            f"Sequential execution started (Paths A -> B -> C) | Request ID: {request_id}"
        )

        # Log extracted information
        logger.info(
            f"Extracted - Layout Components: {list(layout_component_required_info.keys()) if layout_component_required_info else []}"
        )
        logger.info(
            f"Extracted - Components: {list(components_required_info.keys()) if components_required_info else []}"
        )
        logger.info(
            f"Extracted - Pages: {list(page_type_info.keys()) if page_type_info else []}"
        )

        # Debug logging for extracted data
        # DebugFileLogger.log_to_file(f"extracted_project_info_{request_id}.txt", project_info)
        # DebugFileLogger.log_to_file(f"extracted_visual_info_{request_id}.txt", visual_text_info)
        # DebugFileLogger.log_to_file(f"extracted_layout_components_{request_id}.json", layout_component_required_info)
        # DebugFileLogger.log_to_file(f"extracted_components_{request_id}.json", components_required_info)
        # DebugFileLogger.log_to_file(f"extracted_pages_{request_id}.json", page_type_info)
        # DebugFileLogger.log_to_file(f"extracted_user_journey_{request_id}.txt", user_journey_info)

        page_types_dict = page_type_info
        layouts_dict = layout_info
        logger.info(f"Generating App.tsx file | Request ID: {request_id}")
        app_tsx_file = design_generator.generate_app_tsx_from_page_types(
            page_types_dict
        )
        app_tsx_content = app_tsx_file[0]["content"]
        # DebugFileLogger.log_to_file(f"generated_app_tsx_{request_id}.tsx", app_tsx_content)

        # STEP 2: Run Path A (CSS & Config Generation) - First
        logger.info(f"Running Path A (CSS & Config) | Request ID: {request_id}")
        css_config_code, detailed_steps_css_config = await run_path_a(
            request_id,
            visual_text_info,
            project_id,
            request_data,
            states,
            redis_stream_publisher,
        )
        logger.info(
            f"Path A completed - Generated {len(css_config_code)} CSS/Config files"
        )
        # DebugFileLogger.log_to_file(f"path_a_css_config_{request_id}.json", css_config_code)
        # DebugFileLogger.log_to_file(f"path_a_steps_{request_id}.json", detailed_steps_css_config)

        # STEP 3: Run Path B (Component Generation) - Second, with data from Path A
        logger.info(
            f"Running Path B (Components) with Path A data | Request ID: {request_id}"
        )
        component_response_code, detailed_steps_component = await run_path_b(
            request_id,
            project_info,
            app_tsx_content,
            components_required_info,
            layout_component_required_info,
            user_journey_info,
            project_id,
            states,
            redis_stream_publisher,
        )
        logger.info(
            f"Path B completed - Generated {len(component_response_code)} component files"
        )
        # DebugFileLogger.log_to_file(f"path_b_components_{request_id}.json", component_response_code)
        # DebugFileLogger.log_to_file(f"path_b_steps_{request_id}.json", detailed_steps_component)

        # STEP 4: Run Path C (Layout Generation) - Third, with data from Path A and Path B
        logger.info(
            f"Running Path C (Layout) with Path A & B data | Request ID: {request_id}"
        )
        layout_response_code, detailed_steps_layout = await run_path_c(
            request_id,
            project_info,
            app_tsx_content,
            layout_component_required_info,
            components_required_info,
            user_journey_info,
            global_structure,
            project_id,
            states,
            redis_stream_publisher,
        )
        logger.info(
            f"Path C completed - Generated {len(layout_response_code)} layout files"
        )
        # DebugFileLogger.log_to_file(f"path_c_layouts_{request_id}.json", layout_response_code)
        # DebugFileLogger.log_to_file(f"path_c_steps_{request_id}.json", detailed_steps_layout)

        total_pages = len(page_types_dict.keys())
        total_components = len(component_response_code) + len(layout_response_code)

        # Total components code will be the layout and individual codes combined
        component_response_code = component_response_code + layout_response_code
        logger.info(
            f"Sequential execution completed - Total components generated: {len(component_response_code)} | Request ID: {request_id}"
        )

        # Function for generating a single page
        async def generate_single_page(
            project_info,
            app_tsx,
            page_name,
            page_description,
            layout_components,
            user_journey_info,
            component_response_code,
            request_id,
        ):
            """Generate code for a single page using the Page Code Generator API."""
            logger.info(f"Generating page: {page_name} | Request ID: {request_id}")

            # Pre process the component_response_code to pass only the components if they are in layout_components
            layout_set = set(layout_components)
            # DebugFileLogger.log_to_file(f"layout_set_{page_name}_{request_id}.json", list(layout_set))

            # List to store the final, filtered components
            filtered_components = []

            # Iterate over each component in the response
            for component in component_response_code:
                # Get the full filename (e.g., "Header.tsx") from the path
                base_name = os.path.basename(component["fileName"])

                # Get the component name by removing the extension (e.g., "Header")
                component_name = os.path.splitext(base_name)[0]

                # Check if the extracted component name is in our layout set
                if component_name in layout_set:
                    filtered_components.append(component)

            # DebugFileLogger.log_to_file(f"filtered_component_response_code_{page_name}_{request_id}.json", filtered_components)
            page_data = (
                f"**1. <project_info>\n{project_info}\n</project_info>\n"
                f"**2. <app_tsx_content>\n{app_tsx}\n<\app_tsx_content>\n"
                f'**3. <page_type_info>\n{{"{page_name}": "{page_description}"}}\n</page_type_info>\n'
                f'**4. <layout_info>\n{{"{page_name}": {layout_components}}}\n</layout_info>\n'
                f"**5. <user-journey-mapping>\n{user_journey_info}\n</user-journey-mapping>\n"
                f"**6. <custom_component_code>\n{filtered_components}\n</custom_component_code>\n"
            )

            # DebugFileLogger.log_to_file(f"page_payload_{page_name}_{request_id}.txt", page_data)

            page_raw_response = await design_generator.EE_MLO_PAGE_CODE_GENERATOR(
                page_data
            )

            if not page_raw_response:
                logger.error(
                    f"Page Code Generator returned empty response for: {page_name} | Request ID: {request_id}"
                )
                raise HTTPException(
                    status_code=500,
                    detail="Page Code Generator Agent returned empty response",
                )

            # DebugFileLogger.log_to_file(f"page_raw_{page_name}_{request_id}.txt", page_raw_response)

            parsed_response = design_generator.parse_text_context(page_raw_response)

            page_response_code: list = parsed_response.get("code_blocks", [])
            detailed_steps_page: dict = parsed_response.get("narrative_steps", {})
            image_response: list = design_generator.parse_image_block(page_raw_response)

            final_image_response: dict = image_response[0]["description"]

            # DebugFileLogger.log_to_file(f"image_response_{page_name}.json", image_response[0]["description"])

            logger.info(f"Page {page_name} generated ✅ | Request ID: {request_id}")
            # DebugFileLogger.log_to_file(f"page_blocks_{page_name}_{request_id}.json", page_response_code)

            return page_response_code, detailed_steps_page, final_image_response

        # STEP 5: Page Code Generator (Parallel)
        try:
            logger.info(f"Page Code Generator started | Request ID: {request_id}")

            page_start_time = datetime.datetime.now()
            state = {
                "progress": AppProgressEnum.PAGES_GENERATED,
                "status": ProjectStatusEnum.IN_PROGRESS,
                "log": f"Agent : Page Developer | {len(page_types_dict)} Pages | Complete Application Assembly\nAction: Assembling full-featured pages with component integration and user workflows",
                "progress_description": f"Creating {', '.join([name for name, _ in page_types_dict.items()])} complete application pages. Integrating all components and layouts into functional user interfaces with proper routing, state management, and interactive features.",
                "event_type": event_type,
                "metadata": [],
            }
            states = await publish_and_get_state(
                redis_stream_publisher, request_id, state, states
            )

            logger.info(
                f"Generating {len(page_types_dict)} pages in parallel: {list(page_types_dict.keys())}"
            )

            # Create tasks for parallel execution
            page_tasks = []
            for page_name, page_description in page_types_dict.items():
                layout_components = layouts_dict.get(page_name, [])
                task = generate_single_page(
                    project_info,
                    app_tsx_content,
                    page_name,
                    page_description,
                    layout_components,
                    user_journey_info,
                    component_response_code,
                    request_id,
                )
                page_tasks.append(task)

            # Execute tasks concurrently and collect results
            page_results = await asyncio.gather(*page_tasks, return_exceptions=True)

            # Initialize variables to store results
            page_response_code = []
            detailed_steps_page = []
            all_page_code = []
            all_image_response = []

            # Process each result
            for i, result in enumerate(page_results):
                page_name = list(page_types_dict.keys())[i]

                if isinstance(result, Exception):
                    logger.error(
                        f"Error in Page Code Generator task for page {page_name}: {str(result)}"
                    )
                    continue

                if isinstance(result, tuple) and len(result) == 3:
                    response_code, steps_page, image_response = result
                    page_response_code.append(response_code)
                    detailed_steps_page.append(steps_page)
                    all_image_response.append(image_response)

                    # Extend all_page_code with valid response_code
                    if response_code:
                        all_page_code.extend(response_code)
                    else:
                        logger.error(f"Empty response for page {page_name}")
                else:
                    logger.error(
                        f"Unexpected result format for page {page_name}: {result}"
                    )

            # This is the function for processing media data.
            link_change_start_time = datetime.datetime.now()

            async def get_processed_media_links(dynamic_input_list: list) -> list:
                """
                A dedicated calling function that takes a dynamic list of dictionaries,
                invokes the media processor, and returns the results.

                Args:
                    dynamic_input_list (list): A list containing dictionaries that define
                                            the media to be processed. The structure must
                                            match the input format of the processor.

                Returns:
                    list: The processed list with placeholder URLs replaced by real media links.
                """
                if not isinstance(dynamic_input_list, list) or not dynamic_input_list:
                    logger.error("Input must be a non-empty list.")
                    return []

                logger.info(
                    f"Starting media processing job for {len(dynamic_input_list)} page(s)."
                )

                try:
                    # --- This is the core of the calling function ---
                    # It calls the imported async function and awaits its result.
                    processed_results = await process_media_requests(dynamic_input_list)

                    # 3. DO SOMETHING WITH THE RESULT
                    if processed_results:
                        processed_results = json.dumps(processed_results, indent=2)
                        # logger.info(f"New Image Links Json has been generated: {processed_results}")
                    # You can return the processed results or do something else with them.
                    logger.info("Successfully processed the media requests.")
                    return processed_results

                except Exception as e:
                    logger.critical(
                        f"A critical error occurred in the media processing pipeline: {e}"
                    )
                    # Depending on your needs, you might want to return an empty list,
                    # the original data, or raise the exception further.
                    return []

            # Call the function with the valid JSON image data
            valid_links = await get_processed_media_links(all_image_response)
            # DebugFileLogger.log_to_file(f"processed_image_links_{request_id}.json", valid_links)

            # Replace placeholder URLs in the page response code with actual media links
            all_page_code = DesignGenerationService.replace_image_links(
                valid_links, all_page_code
            )
            logger.info(
                f"Replaced placeholder URLs in page response code with actual media links | Request ID: {request_id}"
            )
            link_change_end_time = datetime.datetime.now()
            link_change_duration = (
                link_change_end_time - link_change_start_time
            ).total_seconds()
            logger.info(
                f"Media link processing completed in {link_change_duration/60:.2f} minutes | Request ID: {request_id}"
            )

            if detailed_steps_page:
                final_detailed_steps_summary_page = {
                    "step_1": "\n".join(
                        [
                            f"* {narrative.get('step_1', 'No details provided')}"
                            for narrative in detailed_steps_page
                            if "step_1" in narrative
                        ]
                    ),
                    "step_2": "\n".join(
                        [
                            f"* {narrative.get('step_2', 'No details provided')}"
                            for narrative in detailed_steps_page
                            if "step_2" in narrative
                        ]
                    ),
                    "step_3": "\n".join(
                        [
                            f"* {narrative.get('step_3', 'No details provided')}"
                            for narrative in detailed_steps_page
                            if "step_3" in narrative
                        ]
                    ),
                    "step_4": "\n".join(
                        [
                            f"* {narrative.get('step_4', 'No details provided')}"
                            for narrative in detailed_steps_page
                            if "step_4" in narrative
                        ]
                    ),
                }

            # Log results to debug files
            # DebugFileLogger.log_to_file("page_code.json", page_response_code)
            # DebugFileLogger.log_to_file(f"detailed_page_steps_{request_id}.json", final_detailed_steps_summary_page["step_3"])
            # final_detailed_steps_summary_page["step_3"] = (final_detailed_steps_summary_page["step_3"].replace("\\n", ""))

            # Check if all tasks failed
            if not all_page_code:
                logger.error("All page generation tasks failed")
                raise HTTPException(
                    status_code=500, detail="Page Code Generator failed for all pages"
                )

            logger.info(
                f"Page Code Generator completed - Generated {len(all_page_code)} code blocks | Request ID: {request_id}"
            )

            # Add COMPLETED state for page generation
            state = states.pop()
            state["progress"] = AppProgressEnum.PAGES_GENERATED
            state["status"] = ProjectStatusEnum.COMPLETED
            state["log"] = (
                f"Agent : Page Developer | {len(page_response_code)} Pages Generated\nAction: Page generation completed successfully"
            )
            state["progress_description"] = (
                f"{final_detailed_steps_summary_page['step_3']}"
            )
            state["event_type"] = event_type
            state["metadata"] = [{"type": "files", "data": all_page_code}]
            state["event_type"] = "initial-code-gen"

            logger.info(
                f"Total components: {total_components}, Total pages: {total_pages}"
            )

            page_response_code = all_page_code

        except Exception as e:
            state["status"] = ProjectStatusEnum.FAILED
            logger.error(f"Page Code Generator failed: {str(e)}")
            raise HTTPException(status_code=500, detail=f"Error in Step 5: {str(e)}")

        finally:
            end_time = datetime.datetime.now()
            duration_seconds = (end_time - page_start_time).total_seconds()
            states = await publish_and_get_state(
                redis_stream_publisher, request_id, state, states
            )
            logger.info(
                f"Page generation completed in {duration_seconds/60:.2f} minutes"
            )

        # Combine all code blocks into a single response
        logger.info(f"Combining all generated code blocks | Request ID: {request_id}")
        merged_response_code = (
            css_config_code
            + component_response_code
            + page_response_code
            + app_tsx_file
        )

        # logger.debug(f"Total Number of Code Blocks Generated: \n{merged_response_code}\n")
        # DebugFileLogger.log_to_file("merged_response_code.json", merged_response_code)
        # merged_response_details = {
        #     "css_config": detailed_steps_css_config,
        #     "component": detailed_steps_component,
        #     "page": detailed_steps_page,
        # }

        # Azure DevOps implementation - commit generated code
        azure_files_to_commit = []
        for file_info in merged_response_code:
            azure_files_to_commit.append(
                {
                    "fileName": file_info.get(
                        "fileName", file_info.get("filename", "")
                    ),
                    "content": file_info.get("content", ""),
                }
            )

        await build_and_deploy(
            states,
            merged_response_code,
            repo_name,
            project_id,
            request_id,
            request_data,
            ado_helper,
            redis_stream_publisher,
            "initial-code-gen",
        )
        await persist_data_to_db(states[-1], project_id, request_id)
        await update_conversation_with_metadata(
            project_id=project_id,
            conversation_metadata=states,
            project_db_service=project_db_service,
        )
        # return CodeResponse(status_code=200, code=json.dumps(merged_response_code))

        # Finalize and return the response
        end_time = datetime.datetime.now()
        elapsed_time = end_time - enhancer_start_time

        logger.info(f"Design generation process completed | Request ID: {request_id}")
        logger.info(
            f"Total time: {elapsed_time.total_seconds():.2f} seconds ({elapsed_time.total_seconds() / 60:.2f} minutes)"
        )
        # logger.info(f"Project ID: {project_id} | Live URL: {live_url}")

        # # Log final summary to debug file
        # final_summary = {
        #     "request_id": request_id,
        #     "project_id": project_id,
        #     "user": user,
        #     "start_time": str(start_time),
        #     "end_time": str(end_time),
        #     "duration_seconds": elapsed_time.total_seconds(),
        #     "duration_minutes": elapsed_time.total_seconds() / 60,
        #     "live_url": live_url,
        #     "total_files_generated": len(merged_response_code),
        #     "deployment_status": deployment_status.get("state", "Unknown")
        # }
        # DebugFileLogger.log_to_file(f"final_summary_{request_id}.json", final_summary)

    except HTTPException as http_exc:
        logger.error(
            f"HTTP Error during design generation | Request ID: {request_id} | Status: {http_exc.status_code} | Detail: {http_exc.detail}"
        )
        raise http_exc
    except Exception as e:
        state = {
            "progress": AppProgressEnum.FAILED,
            "status": ProjectStatusEnum.FAILED,
            "log": f"Agent : Page Developer | Unexpected error while creating your project : {e}",
            "progress_description": "Oh no! Looks like something went wrong. Please try again",
            "event_type": event_type,
            "is_final": True,
            "metadata": [],
        }
        await redis_stream_publisher.publish_project_update(request_id, state)
        await project_db_service.update_project_status(
            project_id,
            request_id,
            ProjectStatusEnum.FAILED,
            f"Unexpected error while creating your project : {e}",
            AppProgressEnum.FAILED,
            "Oh no! Looks like something went wrong. Please try again",
            json.dumps(state["metadata"]),
        )
        logger.error(
            f"Unexpected error during design generation | Request ID: {request_id} | Error: {str(e)}",
            exc_info=True,
        )
        raise HTTPException(
            status_code=500, detail=f"An unexpected server error occurred: {str(e)}"
        )
