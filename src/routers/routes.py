import datetime
import io
import uuid
from typing import Any

from fastapi import APIRouter, File, HTTPException, UploadFile
from fastapi.params import Query
from fastapi.responses import StreamingResponse, JSONResponse

# from fastapi import Query
from src.services.azure.ado_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from src.services.database.project_service import (
    ProjectDatabaseService as ProjectService,
)

from src.model.request_schemas import (
    AppTypeEnum,
    ConversationAPIRequest,
    DARequest,
    ProjectBuildSwitchRequest,
    SmartPromptRequest,
)
from src.services.blob_service import AzureBlobService
from src.services.code_generation.code_generation import CodeGeneration
from src.services.code_generation.strategies.da_services import DAServices
from src.services.database.db_operations import (
    get_or_create_user,
)
from src.services.generation.generation_factory import GenerationFactory
from src.settings.settings import Settings, settings_loader
from src.utils.debug_file_logger import DebugFileLogger
from src.utils.decorators.avaplus_decorator import handle_exceptions_and_log
from src.utils.logger import AppLogger

router = APIRouter()
blob_service = AzureBlobService()


# Create a function to get these instances when needed
def get_code_generator():
    setting = Settings()
    provider = GenerationFactory.get_generation("avaplus")
    strategy = DAServices(provider, setting.da_creds)
    return CodeGeneration(strategy)


def get_project_service():
    return ProjectService()


# Setup for conversation API
logger = AppLogger(__name__).get_logger()


@router.get("/health")
def health_check():
    return JSONResponse(content={"status": "ok"}, status_code=200)


@router.post("/enhance")
async def enhance_prompt(request: SmartPromptRequest):
    # Get instances when needed
    setting = Settings()
    provider = GenerationFactory.get_generation("avaplus")

    mode = (
        "EE_MLO_NEW_SMART_PROMPT"
        if request.type.capitalize() == AppTypeEnum.APP
        else "EE_MLO_NEW_SMART_PROMPT_WIREFRAME"
    )
    try:
        da_request = DARequest(
            mode=mode,
            promptOverride=False,
            useCaseIdentifier=mode + setting.da_creds.DA_USECASE_IDENTIFIER,
            userSignature=request.userSignature,
            image=None
            if request.image == [] or request.image == [""]
            else request.image,
            prompt=request.prompt,
        ).model_dump()
        smart_prompt = await provider.generate(da_request)
        # print("smart_prompt: ", smart_prompt)
        return JSONResponse(status_code=200, content=smart_prompt)
    except Exception as e:
        print("Error:", e)
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/upload-image/")
async def upload_image(file: UploadFile = File(...)):
    filename = file.filename
    file_content = await file.read()
    result = blob_service.upload_image(file_content, filename)
    return result


@router.get("/download-image/{filename}")
async def download_image(filename: str):
    try:
        image_data = blob_service.download_image(filename)
        return StreamingResponse(io.BytesIO(image_data), media_type="image/jpeg")
    except HTTPException as e:
        return {"detail": e.detail}


@router.get("/project")
async def get_user_projects(
    user_signature: str = Query(...), num_projects: int = Query(...)
):
    try:
        project_service = get_project_service()
        user_id = await project_service.get_or_create_user(user_signature)
        projects = await project_service.get_project_list(user_id)

        return {"status_code": 200, "projects": projects}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/user")
async def get_user_id(email: str = Query(...)):
    user_id = await get_or_create_user(email)
    return JSONResponse(status_code=200, content=str(user_id))


@handle_exceptions_and_log(logger)
@router.post("/conversation")
async def process_conversation_api(
    request: ConversationAPIRequest,
    user_signature: str = Query(...),
) -> JSONResponse:
    logger.info(f"Conversation API request received from {user_signature}")

    try:
        code_generator = get_code_generator()
        # Convert the request to a dictionary for the service
        request_dict = request.model_dump()

        # Log the request for debugging
        logger.debug(f"Conversation request: {request_dict}")
        DebugFileLogger.log_to_file(
            f"api_conversation_request_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            request_dict,
        )

        # Process the conversation using the DA service
        response = await code_generator.process_conversation(request_dict)

        # Log the response for debugging
        logger.debug("Conversation response received")
        DebugFileLogger.log_to_file(
            f"api_conversation_response_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
            response,
        )

        logger.info("Conversation API request processed successfully")
        return JSONResponse(status_code=200, content=response)

    except Exception as e:
        logger.error(f"Error processing conversation request: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to process conversation: {str(e)}"
        )


@router.get("/project/{project_id}")
async def get_complete_project_data(
    project_id: str,
    user_signature: str = Query(..., description="User's email or identifier"),
):
    """
    Get complete project data including basic details, settings, repository details,
    and conversation history.

    Args:
        project_id: ID of the project
        user_signature: User's email or identifier

    Returns:
        Dictionary containing all project data
    """
    try:
        # Validate user has access to this project
        user_id = await get_project_service().get_or_create_user(user_signature)
        project = await get_project_service().get_project_details(project_id)

        if not project:
            raise HTTPException(status_code=404, detail="Project not found")

        if str(project["created_by"]) != str(user_id):
            raise HTTPException(status_code=403, detail="Access denied")

        # Get complete project data
        project_data = await ProjectService.get_complete_project_data(project_id)

        # Convert UUID and datetime objects to strings
        project_data = json_serialize_uuid(project_data)

        return JSONResponse(status_code=200, content=project_data)

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error retrieving complete project data: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to retrieve project data: {str(e)}"
        )


@router.get("/project/version")
async def switch_build(
    request: ProjectBuildSwitchRequest,
):
    """
    Get complete project data including basic details, settings, repository details,
    and conversation history.

    Args:
        project_id: ID of the project
        user_signature: User's email or identifier

    Returns:
        Dictionary containing all project data
    """
    try:
        ado_helper = AdoHelper()
        project_repo_details = await get_project_service().get_repository_details(
            request.project_id
        )
        clone_url = project_repo_details["clone_url"]
        repo_name = clone_url.split("/")[-1]
        # Get complete project data
        pipeline_name = f"CI-{repo_name}"
        # Validate user has access to this project
        await ado_helper.trigger_build_for_commit(pipeline_name, request.commit_id)
        polled_data = await ado_helper.poll_build_status(
            request.commit_id, repo_name, pipeline_name
        )
        final_status = polled_data.get("status")
        final_result = polled_data.get("result")

        return JSONResponse(
            status_code=200, content={"status": final_status, "result": final_result}
        )

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error retrieving complete project data: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to retrieve project data: {str(e)}"
        )


def json_serialize_uuid(obj: Any) -> Any:
    """
    Recursively convert UUID and datetime objects to strings in a nested structure.
    Works with dictionaries, lists, and individual values.
    """
    if isinstance(obj, dict):
        return {k: json_serialize_uuid(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [json_serialize_uuid(item) for item in obj]
    elif isinstance(obj, uuid.UUID):
        return str(obj)
    elif isinstance(obj, datetime.datetime):
        return obj.isoformat()
    elif isinstance(obj, datetime.date):
        return obj.isoformat()
    else:
        return obj


@router.post("/reload-settings")
async def reload_settings():
    """Reload application settings from database API"""
    try:
        # Force reload settings from API
        settings_loader._api_settings = None
        await settings_loader.load_api_settings()

        # Reload all settings classes
        from src.settings.settings import Settings

        settings_instance = Settings()
        settings_instance.reload_settings()

        return JSONResponse(
            status_code=200,
            content={"status": "success", "message": "Settings reloaded successfully"},
        )
    except Exception as e:
        logger.error(f"Error reloading settings: {str(e)}")
        raise HTTPException(
            status_code=500, detail=f"Failed to reload settings: {str(e)}"
        )
