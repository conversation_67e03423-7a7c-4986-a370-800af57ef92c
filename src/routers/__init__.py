# API route definitions.

from fastapi import APIRouter

# from src.dependencies import get_current_user
# from src.security.azure_auth import verify_token

from .code_generation import router as code_generation_router
from .code_download import router as download_router
from .design_analysis import router as design_analysis_router
from .design_generation import router as design_generation_router
from .wireframe_generation import router as wireframe_generation_router
from .routes import router as default_routes
from .streaming_routes import streaming_router


api_router = APIRouter()

api_router.include_router(
    code_generation_router,
    prefix="/code-generation",
    tags=["code-generation"],
    # dependencies=[Depends(get_current_user)],
)
api_router.include_router(
    download_router,
    prefix="/download",
    tags=["download"],
    # dependencies=[Depends(get_current_user)],
)
api_router.include_router(
    design_generation_router,
    prefix="/design-generation",
    tags=["design-generation"],
    # dependencies=[Depends(verify_token)],
)
api_router.include_router(
    design_analysis_router,
    prefix="/design-analysis",
    tags=["design-analysis"],
    # dependencies=[Depends(verify_token)],
)

api_router.include_router(
    wireframe_generation_router,
    prefix="/wireframe-generation",
    tags=["wireframe-generation"],
    # dependencies=[Depends(verify_token)],
)

api_router.include_router(
    default_routes,
    tags=["common"],
    # dependencies=[Depends(verify_token)],
)

api_router.include_router(
    streaming_router,
    prefix="/stream",  # Optional prefix for all routes in this router
    tags=["Streaming"],
)
