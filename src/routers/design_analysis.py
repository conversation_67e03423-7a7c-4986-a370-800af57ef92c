from fastapi import APIRouter

from src.model.request_schemas import DesignAnalyzerRequest
from src.services.design_analysis.design_analyzer import get_design_analysis

router = APIRouter()


@router.post("/design-analysis/design/analyse")
async def design_analysis(payload: DesignAnalyzerRequest):
    # print(payload.tests)
    analysis = await get_design_analysis(payload)
    if analysis is None:
        return {"status_code": 500, "error": "Unable to process the request currently!"}
    return {"status_code": 200, "data": analysis}
