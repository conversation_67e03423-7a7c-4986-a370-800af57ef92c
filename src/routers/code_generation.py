import asyncio
import datetime
import json
from typing import <PERSON>ple
import uuid

import aiofiles
from fastapi import APIRouter, BackgroundTasks, HTTPException, Query, Request
from fastapi.params import Depends
from fastapi.responses import JSONResponse


from src.dependencies import get_current_user
from src.model.request_schemas import (
    AppProgressEnum,
    CodeGenerationRequest,
    CodeRegenerationRequest,
    ProjectStatusEnum,
)
from src.services.code_generation.code_generation import CodeGeneration
from src.services.code_generation.strategies.da_services import DAServices
from src.services.generation.generation_factory import GenerationFactory
from src.services.database.project_service import ProjectDatabaseService

# Azure services imports
from src.services.azure.ado_helper import AdoHelper
from src.services.React_prompt_helper import ReactPromptHelper
from src.core.sse_pubsub import RedisStreamPublisher
from src.settings.settings import Settings
from src.utils.debug_file_logger import DebugFileLogger
from src.utils.decorators.avaplus_decorator import handle_exceptions_and_log
from src.utils.helpers.name_formatter import format_repository_name
from src.utils.logger import AppLogger
from src.utils.code_regeneration.post_processor import (
    merge_code_lists,
    extract_code_from_api_response,
)
from src.utils.design_token_extractor import (
    extract_design_tokens,
    create_design_token_metadata,
)
from src.utils.code_generation_stats import calculate_code_generation_stats

# from starlette.background import BackgroundTask


STREAMING_QUEUES = {}

router = APIRouter()

# Remove module-level instantiations
# settings = Settings()
# provider = GenerationFactory.get_generation("avaplus")
# strategy = DAServices(provider, settings.da_creds)
# code_generator = CodeGeneration(strategy)

logger = AppLogger(__name__).get_logger()

# Global Redis connection pool (only created if SSE_BACKEND is 'redis')
redis_pool = None

# Create an instance of the service
project_db_service = ProjectDatabaseService()


# Create helper functions for lazy initialization
def get_settings():
    return Settings()


def get_code_generator():
    settings = Settings()
    provider = GenerationFactory.get_generation("avaplus")
    strategy = DAServices(provider, settings.da_creds)
    return CodeGeneration(strategy)


def safe_json_parse(json_string: str, field_name: str = "code") -> list:
    """
    Safely parse JSON string with proper error handling and cleaning.

    Args:
        json_string: The JSON string to parse
        field_name: The field to extract from the parsed JSON

    Returns:
        List from the specified field, or empty list if parsing fails
    """
    try:
        # First attempt: try parsing as-is
        data = json.loads(json_string)
        result = data.get(field_name, [])

        if not isinstance(result, list):
            logger.warning(
                f"✗ Field '{field_name}' is not a list, converting to empty list"
            )
            return []

        return result

    except json.JSONDecodeError as e:
        logger.warning(f"✗ Initial JSON parse failed: {str(e)}")

        try:
            # Second attempt: clean control characters and try again
            cleaned = (
                json_string.replace("\n", "\\n")
                .replace("\r", "\\r")
                .replace("\t", "\\t")
            )
            data = json.loads(cleaned)
            result = data.get(field_name, [])

            if not isinstance(result, list):
                logger.warning(f"✗ Field '{field_name}' is not a list after cleaning")
                return []

            logger.info("✓ JSON parsed successfully after cleaning control characters")
            return result

        except json.JSONDecodeError as e2:
            logger.error(f"✗ Failed to parse JSON even after cleaning: {str(e2)}")
            logger.debug(
                f"✗ Problematic JSON around position {e2.pos}: {json_string[max(0, e2.pos-50):e2.pos+50]}"
            )
            return []


@handle_exceptions_and_log(logger)
@router.post("/generate/app")
async def code_generation(
    request: CodeGenerationRequest,
    background_tasks: BackgroundTasks,
    request_obj: Request,
    user: str = Depends(get_current_user),
    user_signature: str = Query(...),
) -> JSONResponse:
    """Generate a complete React application from a UI design image."""
    try:
        start_time = datetime.datetime.now()

        # Create a new project in the database
        project_id = await project_db_service.create_project(
            project_name=request.project_name,
            project_description=request.project_description,
            # project_type=request.project_type,
            user_id=user,
            project_state=request.project_state,
        )

        # Store project settings
        await project_db_service.create_or_update_project_settings(
            project_id=project_id,
            device=request.platform,
            framework=request.framework,
            design_system=request.design_library,
            generation_type=request.project_type,
        )

        # Generate a unique request ID
        request_id = str(uuid.uuid4())
        logger.debug(f"Request ID: {request_id}")

        logger.info("START | App Generation | Code generation process initiated")
        logger.debug(
            f"Input: Framework={request.framework}, Design Library={request.design_library}"
        )

        request_dict = request.model_dump()
        request_dict.update(
            {"userSignature": user_signature, "user_input": request.project_description}
        )

        redis_publisher = request_obj.app.state.redis_stream_publisher

        background_tasks.add_task(
            process_code_generation,
            request_dict,
            project_id,
            user,
            user_signature,
            request_id,
            start_time,
            redis_publisher,  # Pass the publisher instance
        )

        return JSONResponse(
            status_code=202, content={"job_id": request_id, "project_id": project_id}
        )
    except Exception as e:
        logger.error(f"Error initiating code generation: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to initiate code generation: {str(e)}",
        )


@handle_exceptions_and_log(logger)
async def process_code_generation(
    request,
    project_id,
    user,
    user_signature,
    request_id,
    start_time,
    redis_stream_publisher: RedisStreamPublisher,
):
    # Get instances when needed
    settings = get_settings()
    code_generator = get_code_generator()

    state = {}
    states = []
    try:
        state = {
            "status": ProjectStatusEnum.PENDING,
            "progress": AppProgressEnum.OVERVIEW,
            "log": "Senior Code Architect | Initializing your project workspace",
            "progress_description": "Excellent choice! I'm carefully analyzing your requirements and preparing to craft something truly exceptional for you",
            "event_type": "initial-code-gen",
            "metadata": [],
        }

        states = prepare_states(states, state)
        await project_db_service.create_project_status(
            project_id,
            request_id,
            states[0]["status"],
            states[0]["log"],
            states[0]["progress"],
            states[0]["progress_description"],
            json.dumps(states[0]["metadata"]),
        )
        # await redis_stream_publisher.publish_project_update(request_id, state)

        # Canny Edge Processing and appending to request
        # image_uris = request["image"]
        # request["image"] = process_edges_image(image_uris)

        project_brief = await code_generator.generate_project_brief(request)
        await project_db_service.update_project_name(
            project_id, project_brief["name"], project_brief["description"]
        )

        state = {
            "status": ProjectStatusEnum.IN_PROGRESS,
            "progress": AppProgressEnum.OVERVIEW,
            "log": "Lead Project Strategist | Crafting your comprehensive project blueprint",
            "progress_description": f"Perfect! I've thoroughly analyzed your vision and here's what I'm building for you:\n\n{project_brief['overview']}\n\nI'm excited to bring this vision to life with clean, scalable code!",
            "event_type": "initial-code-gen",
            "metadata": [
                {
                    "type": "artifact",
                    "data": {"type": "text", "data": project_brief["overview"]},
                },
                {
                    "type": "ref_code",
                    "data": f"{project_brief['name']}",
                },
            ],
        }

        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )

        logger.info("-- Task: Starting code generation process")

        async def log_picker_callback(log_collector):
            nonlocal states
            state = {
                "status": log_collector["project_status"],
                "progress": log_collector["app_progress"],
                "log": log_collector["log"],
                "progress_description": log_collector["progress_description"],
                "event_type": "initial-code-gen",
                "metadata": log_collector["metadata"],
            }
            states = await publish_and_get_state(
                redis_stream_publisher, request_id, state, states
            )

        # 1. Generate PRD
        logger.info("PRD | Generating product requirements")
        detailed_prd = await code_generator.generate_prd(request)
        DebugFileLogger.log_to_file(f"prd_{request_id}.json", detailed_prd)
        logger.debug(f"PRD | Complete | Time: {datetime.datetime.now() - start_time}")

        state = states.pop()
        state["status"] = ProjectStatusEnum.COMPLETED
        state["metadata"] = [
            {
                "type": "artifact",
                "data": {"type": "text", "data": json.dumps(detailed_prd)},
            },
        ]
        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )

        state = {
            "status": ProjectStatusEnum.IN_PROGRESS,
            "progress": AppProgressEnum.SEED_PROJECT_INITIALIZED,
            "log": f"Cloud Infrastructure Specialist | Provisioning your {request['framework']} environment",
            "progress_description": f"Setting up your premium development environment with enterprise-grade tools and configurations:\n\nTechnology Stack: {request['framework']} with {request['design_library']}\nInfrastructure: Azure DevOps integration with automated workflows\nDesign System: {request['design_library']} component library\nTooling: Pre-configured build pipeline and development tools\n\nYour foundation is being crafted following industry best practices and modern development standards!",
            "event_type": "initial-code-gen",
            "metadata": [],
        }

        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )

        # gh_repo_connector = GithubRepoConnector()
        # repo_name = f"mlo-{str(user)[-4:]}-{project_id[-4:]}"
        # gh_repo_data = await gh_repo_connector.create_repo_from_template(
        #     settings.github_config.GH_ORG,
        #     f"{request['framework'].capitalize()}_{request['design_library'].capitalize()}_Template",
        #     repo_name,
        #     settings.github_config.GH_ORG,
        # )
        # netlify_connector = NetlifyConnector(settings.netlify_config)

        # gh_full_name = gh_repo_data["full_name"]
        # gh_repo_id = gh_repo_data["id"]
        # gh_default_branch = gh_repo_data["default_branch"]
        # gh_is_private = gh_repo_data["private"]
        # gh_owner_login = gh_repo_data["owner"]["login"]

        # netlify_site_data = None

        # netlify_site_data = await netlify_connector.create_site_and_link_repo(
        #     github_repo_full_name=gh_full_name,
        #     github_repo_id=gh_repo_id,
        #     github_default_branch=gh_default_branch,
        #     is_private_github_repo=gh_is_private,
        #     build_command="npm run build",
        #     publish_directory="dist",
        #     env_vars={"NODE_VERSION": "22"},
        #     custom_site_name=repo_name,
        #     github_repo_owner_for_installation_lookup=gh_owner_login,
        #     installation_id=settings.github_config.GH_INSTALLATION_ID,
        # )

        # live_url = netlify_site_data.get("ssl_url") or netlify_site_data.get("url")

        repo_name, final_name_part = format_repository_name(
            project_brief["name"], str(user), project_id
        )
        # Initialize Azure DevOps helper
        ado_helper = AdoHelper(repo_name, "azure-pipelines.yaml")

        settings = get_settings()
        swa_app_name = (
            f"ExperienceStudio-{str(user)[-4:]}"
            if settings.devConfig.DEPLOY_PER_USER == "true"
            else "sample-swa-test-2"
        )

        # Create Azure DevOps repository from template (seed project)
        repo_result = await ado_helper.create_repository_and_deploy(
            repo_name,
            True,
            f"{request['framework'].capitalize()}_{request['design_library'].capitalize()}css_Template",
            swa_app_name=swa_app_name,
        )

        # Extract commit hash and repository details
        commit_sha = repo_result["commit_hash"]
        repo_details = repo_result["repository"]
        # pipeline_details = repo_result["pipeline"]
        # build_info = repo_result["build"]

        # Store repository details in database
        if repo_details:
            await project_db_service.create_or_update_repository_details(
                project_id=project_id,
                vcs_provider="azure_devops",
                clone_url=repo_details["remote_url"],  # HTTPS clone URL
                deployment_provider="azure_static_web_apps",
                deployed_url=None,  # Will be updated later when deployment is complete
            )

        # Update state with repository information
        state = {
            "status": ProjectStatusEnum.COMPLETED,
            "progress": AppProgressEnum.SEED_PROJECT_INITIALIZED,
            "log": "Senior Code Architect | Repository created successfully",
            "progress_description": f"Created repository {repo_name} and initialized with template code",
            "metadata": [
                {
                    "type": "repository",
                    "name": repo_name,
                    "url": repo_details["web_url"] if repo_details else None,
                    "clone_url": repo_details["remote_url"] if repo_details else None,
                    "commit_sha": commit_sha,
                },
            ],
            "project_id": project_id,  # Include project_id in the state
        }

        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )

        state = {
            "status": ProjectStatusEnum.IN_PROGRESS,
            "progress": AppProgressEnum.FILE_QUEUE,
            "log": f"Senior Component Architect | Mapping your {request['framework']} ecosystem",
            "progress_description": f"Analyzing your requirements to identify the optimal {request['framework']} components and file structure. I'm strategically planning each module to create a cohesive, maintainable, and scalable architecture that will serve your project well into the future.",
            "event_type": "initial-code-gen",
            "metadata": [],
        }

        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )

        # 2. Identify files
        identified_files = await code_generator.identify_files(request, detailed_prd)
        DebugFileLogger.log_to_file(
            f"identified_files_{request_id}.json", identified_files
        )
        logger.debug(
            f"Files | Identified | Time: {datetime.datetime.now() - start_time}"
        )

        async with aiofiles.open("reactFiles.json", "r") as f:
            react_files = json.loads(await f.read())

        # 3.1. Extract design system files
        design_system_files = {
            "src/index.css": react_files.get("src/index.css", ""),
            "tailwind.config.ts": react_files.get("tailwind.config.ts", ""),
        }

        state = states.pop()
        state["status"] = ProjectStatusEnum.COMPLETED
        state["metadata"] = [
            {"type": "file_names", "data": identified_files.get("filesToGenerate")}
        ]

        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )

        state = {
            "status": ProjectStatusEnum.IN_PROGRESS,
            "progress": AppProgressEnum.DESIGN_SYSTEM_MAPPED,
            "log": f"Lead Design System Engineer | Crafting your {request['design_library']} design foundation",
            "progress_description": f"Creating a comprehensive, cohesive design system using {request['design_library']}. I'm carefully curating colors, typography, spacing, and interactive components that will make your application both visually stunning and highly functional. Every design decision is being made with user experience and accessibility in mind.",
            "event_type": "initial-code-gen",
            "metadata": [],
        }

        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )

        # 3.2. Generate design system
        design_system_code = await code_generator.generate_design_system(
            detailed_prd, request, design_system_files
        )
        logger.info("Design | System generated ✓")
        DebugFileLogger.log_to_file(
            f"design_system_{request_id}.json", design_system_code
        )
        logger.debug(
            f"Design | Complete | Time: {datetime.datetime.now() - start_time}"
        )
        state = states.pop()
        state["status"] = ProjectStatusEnum.COMPLETED
        state["progress_description"] = (
            f"I have selected a Design System for your UI \n**Color Palette**\n{detailed_prd['designSystem']['colorPalette']['notes']} \n**Typography**\n{detailed_prd['designSystem']['typography']['notes']}\n**Spacing**\n{detailed_prd['designSystem']['spacing']['notes']}\n"
        )

        # Extract design tokens from the generated design system
        design_tokens = await extract_design_tokens(design_system_code, request_id)

        # Update state with design token metadata
        state["metadata"] = [create_design_token_metadata(design_tokens)]

        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )

        # 4. Categorize files
        categorized_files = ReactPromptHelper._categorize_files(
            identified_files.get("filesToGenerate", [])
        )
        DebugFileLogger.log_to_file(
            f"file_categories_{request_id}.json", categorized_files
        )

        # 7. Generate code
        code = await code_generator.generate_code(
            detailed_prd,
            design_system_code,
            identified_files,
            request,
            log_picker_callback,
        )

        DebugFileLogger.log_to_file(f"react_code_{request_id}.json", code)
        logger.info("Code | Generation complete ✓")
        logger.debug(f"Code | Complete | Time: {datetime.datetime.now() - start_time}")

        if not code:
            logger.error("Code | Empty output X")
            raise HTTPException(status_code=500, detail="Failed to generate React code")

        # Calculate and log code generation statistics
        total_files, generated_files, success_rate, error_files, duration = (
            calculate_code_generation_stats(identified_files, code, start_time)
        )

        if error_files:
            logger.warning(f"Errors | {len(error_files)} files contain errors X")
            logger.debug(f"Error files: {error_files}")

        await build_and_deploy(
            states,
            code,
            repo_name,
            project_id,
            request_id,
            request,
            ado_helper,
            redis_stream_publisher,
            "initial-code-gen",
        )

        await persist_data_to_db(states[-1], project_id, request_id)
        # Update the conversation with the initial metadata
        await update_conversation_with_metadata(
            project_id=project_id,
            conversation_metadata=states,
            project_db_service=project_db_service,
        )
        # Write a function to update the deployed_url in repository detauls table

    except Exception as e:
        logger.error("Unexpected error while creating your project :", e)
        await redis_stream_publisher.publish_project_update(request_id, state)
        await project_db_service.update_project_status(
            project_id,
            request_id,
            ProjectStatusEnum.FAILED,
            f"Unexpected error while creating your project : {e}",
            AppProgressEnum.FAILED,
            "Oh no! Looks like something went wrong. Please try again",
            json.dumps(state["metadata"]),
        )


@handle_exceptions_and_log(logger)
@router.post("/regenerate/code")
async def regenerate_code(
    request: CodeRegenerationRequest,
    background_tasks: BackgroundTasks,
    request_obj: Request,
    user_signature: str = Query(..., description="User's email or identifier"),
    project_id: str = Query(..., description="Project ID"),
    request_id: str = Query(..., description="Request ID"),
) -> JSONResponse:
    """
    Process a code regeneration request using the EE_MLO_REGENERATION mode with SSE support.

    Receives a request with original code and user request, processes it in the background,
    and provides real-time updates via SSE.

    Args:
        request: The request containing the prompt (stringified JSON with
                 original code and user request) and optionally image.
        background_tasks: FastAPI background tasks for async processing.
        request_obj: FastAPI request object to access app state.
        user_signature: User's email or identifier from query parameter.
        project_id: Project ID from query parameter.
        request_id: Request ID from query parameter.

    Returns:
        JSONResponse with job_id and project_id for SSE tracking.
    """

    logger.info(f"✓ Code regeneration request received from {user_signature}")
    logger.debug(f"Request contains image: {bool(request.image)}")

    try:
        # Get the user details

        redis_publisher: RedisStreamPublisher = (
            request_obj.app.state.redis_stream_publisher
        )

        # Add background task for processing regeneration
        background_tasks.add_task(
            process_code_regeneration,
            request,
            project_id,
            request_id,
            user_signature,
            redis_publisher,
        )

        return JSONResponse(
            status_code=202, content={"job_id": request_id, "project_id": project_id}
        )

    except Exception as e:
        logger.error(f"Error initiating code regeneration: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Failed to initiate code regeneration: {str(e)}",
        )


async def process_code_regeneration(
    request,
    project_id,
    request_id,
    user_signature,
    redis_stream_publisher: RedisStreamPublisher,
):
    """
    Background task to process code regeneration with SSE updates.
    """
    try:
        code_generator = get_code_generator()
        user = await project_db_service.get_or_create_user(user_signature)

        project = await project_db_service.get_project_details(project_id)

        repo_name, final_name_part = format_repository_name(
            project["project_name"], user, project_id
        )

        # Get project status to retrieve existing states
        history = await project_db_service.get_project_status_history(
            project_id=project_id, status_id=request_id
        )

        states = history

        event_type = "code-regen"
        start_time = datetime.datetime.now()

        state = {
            "status": ProjectStatusEnum.IN_PROGRESS,
            "progress": AppProgressEnum.CODE_GENERATION,
            "log": "Code Regeneration Agent | Regenerating code based on your request",
            "progress_description": "I've received your request and I'm preparing to regenerate the code. I'll get started right away.",
            "event_type": event_type,
            "metadata": [],
        }

        states = prepare_states(states, state)
        await redis_stream_publisher.publish_project_update(request_id, state)

        # Parse the original prompt JSON to get the original code and user request
        original_code = safe_json_parse(request.prompt, "code")
        user_request = json.loads(request.prompt).get("user_request")

        # Prepare the request dictionary for the service layer
        service_request_dict = {
            "prompt": request.prompt,
            "userSignature": user_signature,
            "image": request.image,
        }

        request = request.model_dump()

        logger.debug("✓ Calling code generator service...")

        # Process the request using the code generator service
        api_response = await code_generator.regenerate_code(service_request_dict)

        # Use the post-processor to extract the code list from the potentially complex API response
        logger.debug("✓ Starting code extraction from API response...")
        extracted_code_list = extract_code_from_api_response(api_response)

        # Merge the original code with the new code, prioritizing new code
        logger.debug("✓ Starting code merge process...")
        updated_code_list = merge_code_lists(original_code, extracted_code_list)

        logger.info(f"✓ Code regeneration complete - {len(updated_code_list)} files")
        end_time = datetime.datetime.now()
        logger.debug(f"Time taken: {end_time - start_time}")

        # Create a summary of changes for the AI response
        changed_files = []
        for file in updated_code_list:
            file_name = file.get("fileName", "")
            # Check if this file was in the original code
            original_file = next(
                (f for f in original_code if f.get("fileName") == file_name), None
            )
            if original_file:
                if original_file.get("content") != file.get("content"):
                    changed_files.append(file_name)
            else:
                # This is a new file
                changed_files.append(file_name + " (new)")

        state = states.pop()
        state["status"] = ProjectStatusEnum.COMPLETED
        state["log"] = "Code Regeneration Agent | Regeneration successful"
        state["progress_description"] = (
            "Great news! I've successfully regenerated the code as per your request. Let me know if there's anything else you need."
        )
        state["metadata"] = [{"type": "files", "data": updated_code_list}]
        state["event_type"] = event_type

        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )

        ado_helper = AdoHelper(repo_name, "azure-pipelines.yaml")

        commit_hash = await build_and_deploy(
            states,
            updated_code_list,
            repo_name,
            project_id,
            request_id,
            request,
            ado_helper,
            redis_stream_publisher,
            event_type,
        )

        await handle_conversation_interaction(
            project_id=project_id,
            user_query=user_request,
            ai_response=state["progress_description"],
            capsule_data=commit_hash,
            ui_metadata=state["metadata"],
            project_db_service=project_db_service,
        )

        await persist_data_to_db(states[-1], project_id, request_id)

    except Exception as e:
        logger.error(f"Error during code regeneration: {str(e)}", exc_info=True)
        # Error state
        state = {
            "status": ProjectStatusEnum.FAILED,
            "progress": AppProgressEnum.FAILED,
            "log": "Code Regeneration Agent | Encountered an unexpected issue",
            "progress_description": "I encountered an unexpected issue while regenerating your code. Please try again, and I'll work to resolve this more effectively on the next attempt.",
            "metadata": [],
        }
        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )
        await project_db_service.update_project_status(
            project_id,
            request_id,
            ProjectStatusEnum.FAILED,
            state["log"],
            state["progress"],
            state["progress_description"],
            json.dumps(state["metadata"]),
        )


@handle_exceptions_and_log(logger)
@router.post("/error/build")
async def fix_build(
    request: CodeRegenerationRequest,
    background_tasks: BackgroundTasks,
    request_obj: Request,
    project_id: str = Query(...),
    request_id: str = Query(...),
    user_signature: str = Query(...),
):
    try:
        # Get the user details

        redis_publisher: RedisStreamPublisher = (
            request_obj.app.state.redis_stream_publisher
        )

        background_tasks.add_task(
            process_error,
            request,
            project_id,
            request_id,
            user_signature,
            redis_publisher,
        )
        return JSONResponse(
            status_code=202, content={"job_id": request_id, "project_id": project_id}
        )
    except Exception as e:
        await project_db_service.update_project_status(
            project_id,
            request_id,
            ProjectStatusEnum.FAILED,
            f"Unexpected error while updating your project : {e}",
            AppProgressEnum.FAILED,
            "Oh no! Looks like something went wrong. Please try again",
            json.dumps([]),
        )


async def process_error(
    request,
    project_id,
    request_id,
    user_signature,
    redis_stream_publisher,
):
    """
    Enhanced process_error function with detailed SSE state updates.
    """
    try:
        code_generator = get_code_generator()
        user = await project_db_service.get_or_create_user(user_signature)

        project = await project_db_service.get_project_details(project_id)

        repo_name, final_name_part = format_repository_name(
            project["project_name"], user, project_id
        )

        history = await project_db_service.get_project_status_history(
            project_id=project_id, status_id=request_id
        )

        states = history

        is_regeneration = any(
            s.get("progress") == AppProgressEnum.DEPLOY
            and s.get("status") == ProjectStatusEnum.COMPLETED
            for s in states
        )
        event_type = "code-regen" if is_regeneration else "initial-code-gen"

        # Extract user request from the prompt JSON
        user_request = safe_json_parse(request.prompt, "user_request")

        log_message = "Regenerating code based on your request"
        log_message = (
            f"Error Fix Agent | {log_message} "
            if event_type == "code-regen"
            else f"Lead Developer | {log_message}"
        )

        if event_type == "code-regen":
            state = {
                "status": ProjectStatusEnum.IN_PROGRESS,
                "progress": AppProgressEnum.CODE_GENERATION,
                "log": log_message,
                "progress_description": "I've received your request and I'm preparing to regenerate the code. I'll get started right away.",
                "event_type": event_type,
                "metadata": [],
            }

        else:
            state = {
                "status": ProjectStatusEnum.IN_PROGRESS,
                "progress": AppProgressEnum.PAGES_GENERATED,
                "log": log_message,
                "progress_description": "I've received your request and I'm preparing to regenerate the code. I'll get started right away.",
                "event_type": event_type,
                "metadata": [],
            }

        states = prepare_states(states, state)
        await redis_stream_publisher.publish_project_update(request_id, state)

        # Parse the original prompt JSON to get the original code and user request
        original_code = safe_json_parse(request.prompt, "code")
        user_request = json.loads(request.prompt).get("user_request")
        # user_request = safe_json_parse(request.prompt, "user_request")
        # build_error = safe_json_parse(request.prompt, "build_error", "")

        # Prepare the request dictionary for the service layer
        service_request_dict = {
            "prompt": request.prompt,
            "userSignature": user_signature,
            "image": None,
        }

        request = request.model_dump()

        logger.debug("✓ Calling code generator service for error fixing...")

        # Process the request using the code generator service
        api_response = await code_generator.regenerate_code(service_request_dict)

        # Use the post-processor to extract the code list from the potentially complex API response
        logger.debug("✓ Starting code extraction from API response...")
        extracted_code_list = extract_code_from_api_response(api_response)

        # Merge the original code with the new code, prioritizing new code
        logger.debug("✓ Starting code merge process...")
        updated_code_list = merge_code_lists(original_code, extracted_code_list)

        logger.info(f"✓ Error fixes applied - {len(updated_code_list)} files updated")

        # Create a summary of changes for the AI response
        changed_files = []
        for file in updated_code_list:
            file_name = file.get("fileName", "")
            # Check if this file was in the original code
            original_file = next(
                (f for f in original_code if f.get("fileName") == file_name), None
            )
            if original_file:
                if original_file.get("content") != file.get("content"):
                    changed_files.append(file_name)
            else:
                # This is a new file
                changed_files.append(file_name + " (new)")

        state = states.pop()
        state["status"] = ProjectStatusEnum.COMPLETED
        if event_type == "code-regen":
            state["log"] = (
                "Error Fix Agent | Success! I've identified and resolved the issue, ensuring everything is back on track."
            )
        else:
            state["log"] = (
                "Success! I've identified and resolved the issue, ensuring everything is back on track."
            )

        if event_type == "initial-code-gen":
            state["progress_description"] = (
                "Success! I've identified and resolved the issue, ensuring everything is back on track."
            )

        state["metadata"] = [{"type": "files", "data": updated_code_list}]

        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )

        ado_helper = AdoHelper(repo_name, "azure-pipelines.yaml")

        # Initialize GitHub connector and commit files
        commit_hash = await build_and_deploy(
            states,
            updated_code_list,
            repo_name,
            project_id,
            request_id,
            request,
            ado_helper,
            redis_stream_publisher,
            event_type,
        )
        await persist_data_to_db(states[-1], project_id, request_id)

        if event_type != "initial-code-gen":
            await handle_conversation_interaction(
                project_id=project_id,
                user_query=user_request,
                ai_response=state["progress_description"],
                capsule_data=commit_hash,
                ui_metadata=state["metadata"],
                project_db_service=project_db_service,
            )

        elif event_type == "initial-code-gen":
            await update_conversation_with_metadata(
                project_id=project_id,
                conversation_metadata=states,
                project_db_service=project_db_service,
            )

    except Exception as e:
        logger.error(
            f"Error during build error fixing process: {str(e)}", exc_info=True
        )
        # Error state
        state = {
            "status": ProjectStatusEnum.FAILED,
            "progress": AppProgressEnum.FAILED,
            "log": f"Error Fix Agent | Error during fix process: {str(e)}",
            "progress_description": "Something went wrong while trying to fix the build errors. Please try again",
            "event_type": event_type,
            "is_final": True,
            "metadata": [],
        }
        states = await publish_and_get_state(
            redis_stream_publisher, request_id, state, states
        )

        await project_db_service.update_project_status(
            project_id,
            request_id,
            ProjectStatusEnum.FAILED,
            state["log"],
            state["progress"],
            state["progress_description"],
            json.dumps(state["metadata"]),
        )


def prepare_states(states, state):
    if len(states) > 0 and states[-1]["progress"] == state["progress"]:
        states.pop()

    states.append(state)

    return states


async def persist_data_to_db(state, project_id, request_id):
    await project_db_service.update_project_status(
        project_id,
        request_id,
        state["status"],
        state["log"],
        state["progress"],
        state["progress_description"],
        json.dumps(state["metadata"]),
    )


async def build_and_deploy(
    states,
    code,
    repo_name,
    project_id,
    request_id,
    request,
    ado_helper,
    redis_stream_publisher,
    event_type,
):
    try:
        if "framework" not in request:
            request["framework"] = "react"
            logger.info("Defaulted missing 'framework' key to 'react'.")
        if "design_library" not in request:
            request["design_library"] = "tailwind"
            logger.info("Defaulted missing 'design_library' key to 'tailwindcss'.")

        logger.info(
            f"Committing {len(code)} generated files to Azure DevOps repository: {repo_name}"
        )

        # Ensure we have the repository name from the initial creation
        if not repo_name:
            logger.error("Repository name is not available for commit operation")
            raise ValueError("Repository name is required for commit operation")

        # Simple commit - just push the generated code to the existing repo
        commit_hash = await ado_helper.commit_multiple_files(
            repo_name=repo_name,
            files_to_commit=code,
            commit_message=f"🚀 Generated the application code with {request['design_library']} components [trigger-pipeline]",
            branch="main",
            operation_type="mixed",  # Mixed mode - detect if files exist and use add/edit accordingly
        )

        if commit_hash:
            logger.info(
                f"✅ Successfully committed generated code. Commit hash: {commit_hash}"
            )
            logger.info(
                f"🔄 Azure Pipeline should now be triggered automatically for repository: {repo_name}"
            )

            log_message = f"Securing your {request['framework']} codebase"

            log_message = (
                f"Code Regeneration Agent | {log_message} "
                if event_type == "code-regen"
                else f"Version Control Specialist | {log_message}"
            )

            state = {
                "status": ProjectStatusEnum.IN_PROGRESS,
                "progress": AppProgressEnum.BUILD,
                "log": log_message,
                "progress_description": "Perfect! Your code has been successfully committed to version control with comprehensive change tracking. Your project is now safely stored with full history and is ready for the build phase.",
                "event_type": event_type,
                "metadata": [{"type": "files", "data": code}],
            }

            states = await publish_and_get_state(
                redis_stream_publisher, request_id, state, states
            )

        else:
            logger.error("❌ Commit operation failed - no commit hash returned")
            raise Exception("Failed to commit code to repository")

    except Exception as e:
        logger.error(f"❌ Failed to commit generated code to Azure DevOps: {str(e)}")
        import traceback

        logger.error(f"Commit error traceback: {traceback.format_exc()}")
        # Re-raise the exception to ensure the process fails if commit fails
        raise Exception(f"Code generation completed but commit failed: {str(e)}")

    # Polling for build status
    if commit_hash and commit_hash != "-1":
        print(f"Polling build status for commit: {commit_hash}")
        build_status_result = await ado_helper.poll_build_status(
            commit_sha=commit_hash,
            repository_name=repo_name,
            timeout_minutes=10,  # e.g. 20 minutes
            poll_interval_seconds=10,
        )

        final_status = build_status_result.get("status")
        final_result = build_status_result.get("result")
        error_logs = build_status_result.get("logs")
        failed_step = build_status_result.get("failed_step")
        live_url = build_status_result.get("live_url")
        build_number = (
            build_status_result.get("build_number") if build_status_result else "N/A"
        )

        # If we have a live URL, update it in the database
        if live_url:
            project_db_service = ProjectDatabaseService()
            await project_db_service.update_repository_deployed_url(
                project_id=project_id, deployed_url=live_url
            )
            logger.info(f"Updated deployed URL in database: {live_url}")

        build_web_url = None
        if (
            build_status_result
            and build_status_result.get("_links")
            and build_status_result["_links"].get("links")
            and build_status_result["_links"]["links"].get("web")
            and build_status_result["_links"]["links"]["web"].get("href")
        ):
            build_web_url = build_status_result["_links"]["links"]["web"]["href"]

        current_metadata = []

        if final_status == "completed" and final_result == "succeeded":
            log_message = f"Build Successful with BUILD ID : {build_number}"
            log_message = (
                f"Code Regeneration Agent | {log_message} "
                if event_type == "code-regen"
                else f"DevOps Specialist | {log_message}"
            )
            state = {
                "status": ProjectStatusEnum.COMPLETED,
                "progress": AppProgressEnum.BUILD,
                "log": log_message,
                "progress_description": "Your application has been successfully built",
                "metadata": [
                    {"type": "ref_code", "data": commit_hash},
                    {"type": "files", "data": code},
                ],
                "event_type": event_type,
            }

            states = await publish_and_get_state(
                redis_stream_publisher, request_id, state, states
            )

            log_message = "Deployment in progress"
            log_message = (
                f"Code Regeneration Agent | {log_message} "
                if event_type == "code-regen"
                else f"DevOps Specialist | {log_message}"
            )

            state = {
                "status": ProjectStatusEnum.IN_PROGRESS,
                "progress": AppProgressEnum.DEPLOY,
                "log": log_message,
                "progress_description": "We are almost there. Deploying your {request['framework']} application to the cloud",
                "metadata": [{"type": "ref_code", "data": live_url}],
                "event_type": event_type,
            }

            states = await publish_and_get_state(
                redis_stream_publisher, request_id, state, states
            )

            await asyncio.sleep(5)
            # log_msg = f"Deployment Successful (Build #{build_number})"
            desc_msg = f"Your application has been successfully built and deployed! You can view it live here: {live_url}."
            if build_web_url:
                desc_msg += f" See Azure DevOps Build: {build_web_url}"
                current_metadata.append(
                    {"type": "ado_build_url", "data": build_web_url}
                )
            if live_url:
                current_metadata.append({"type": "ref_code", "data": live_url})

                log_message = f"Your application has been successfully built and deployed! You can view it live here: {live_url}"
                log_message = (
                    f"Code Regeneration Agent | {log_message} "
                    if event_type == "code-regen"
                    else f"DevOps Specialist | {log_message}"
                )

                state = {
                    "status": ProjectStatusEnum.COMPLETED,
                    "progress": AppProgressEnum.DEPLOY,
                    "log": log_message,
                    "is_final": True,
                    "progress_description": "Your application has been successfully built and deployed! You can view it on the preview tab",
                    "metadata": [{"type": "ref_code", "data": live_url}],
                    "event_type": event_type,
                }

                states = await publish_and_get_state(
                    redis_stream_publisher, request_id, state, states
                )

        else:
            log_message = f"Deployment failed with status: {final_status}, result: {final_result}."
            progress_description = f"The deployment failed at step: '{failed_step}'. Please check the logs for more details."
            if error_logs:
                log_message += "\nSee logs for details."
                progress_description += f"\n\nBuild Error:\n```\n{error_logs}\n```"

            log_message = error_logs
            log_message = (
                f"Code Regeneration Agent | {log_message} "
                if event_type == "code-regen"
                else f"DevOps Specialist | {log_message}"
            )

            state = {
                "status": ProjectStatusEnum.FAILED,
                "progress": AppProgressEnum.BUILD,
                "log": log_message,
                "progress_description": "Oh no! Something went wrong during the build process. Check the logs for more details. Please retry.",
                "event_type": event_type,
                "metadata": [],
            }

            states = await publish_and_get_state(
                redis_stream_publisher, request_id, state, states
            )

        state["event_type"] = event_type
        states = prepare_states(states, state)
        return commit_hash


async def publish_and_get_state(redis_stream_publisher, request_id, state, states):
    await redis_stream_publisher.publish_project_update(request_id, state)
    return prepare_states(states, state)


async def update_conversation_with_metadata(
    project_id: str,
    conversation_metadata: dict,
    project_db_service: ProjectDatabaseService,
) -> str:
    """
    Create or update a conversation with initial UI metadata for a project.

    Args:
        project_id: UUID of the project
        conversation_metadata: Dictionary containing UI metadata for the stepper
        project_db_service: Database service instance to perform the operation

    Returns:
        UUID of the created or updated conversation
    """
    try:
        # Create a new conversation or update existing one
        conversation_id = await project_db_service.create_or_update_conversation(
            project_id=project_id, initial_ui_metadata=conversation_metadata
        )

        logger.info(
            f"Conversation | Created with initial metadata for project {project_id}"
        )
        return conversation_id
    except Exception as e:
        logger.error(f"Conversation | Failed to create with metadata: {str(e)}")
        raise


async def handle_conversation_interaction(
    project_id: str,
    user_query: str,
    ai_response: str,
    capsule_data: str,
    ui_metadata: dict = None,
    project_db_service: ProjectDatabaseService = None,
) -> Tuple[str, str]:
    """
    Handle a conversation interaction by creating or updating a conversation
    and adding user and assistant messages.

    Args:
        project_id: UUID of the project
        user_query: The user's query or request
        ai_response: The AI's response
        ui_metadata: Optional UI state for this conversation point
        project_db_service: Database service instance

    Returns:
        Tuple of (conversation_id, message_id of AI response)
    """
    try:
        # First, get or create the conversation for this project
        conversation_id = await project_db_service.get_conversation_by_project_id(
            project_id
        )

        # Add the user message
        await project_db_service.create_conversation_message(
            conversation_id=conversation_id,
            message_type="user",
            content=user_query,
        )

        # Add the AI response message
        await project_db_service.create_conversation_message(
            conversation_id=conversation_id,
            message_type="assistant",
            content=ai_response,
        )

        await project_db_service.create_conversation_message(
            conversation_id=conversation_id,
            message_type="capsule",
            content=capsule_data,
            ui_metadata=ui_metadata,
        )

        logger.info(
            f"Added user query and AI response to conversation: {conversation_id}"
        )
        return conversation_id

    except Exception as e:
        logger.error(f"Error handling conversation interaction: {str(e)}")
        raise
