from fastapi import APIRouter, Request
from starlette.responses import StreamingResponse
import asyncio
import json
import logging
from typing import Optional

streaming_router = APIRouter()
logger = logging.getLogger(__name__)


@streaming_router.get("/project-status/{request_id}")
async def stream_project_status(
    request_id: str, request: Request, since: Optional[str] = None
):
    """
    This endpoint establishes a Server-Sent Events (SSE) connection.
    It subscribes to updates for a specific `request_id` via the SSEManager
    and streams them to the client. This is highly scalable as it does not
    hold a Redis connection per client.

    Parameters:
        request_id: The unique identifier for the project/request to subscribe to
        since: Optional Redis stream ID to resume from (format: '1684423422651-0')
               Use '0' or '0-0' to get all messages from the beginning of the stream.
    """
    # Normalize the 'since' parameter
    if since == "0":
        since = "0-0"  # Convert to proper Redis stream ID format

    logger.info(
        f"SSE: New connection request for request_id: {request_id}, since: {since}"
    )
    queue = await request.app.state.sse_manager.add_listener(request_id)

    # If 'since' parameter is provided, fetch historical messages first
    if since:
        try:
            # Get messages from the specified ID onwards for this request_id
            historical_messages = await request.app.state.redis_client.xread(
                {request.app.state.sse_manager.stream_name: since},
                count=50,  # Limit to avoid overwhelming the client
            )

            if historical_messages:
                stream_name, stream_messages = historical_messages[0]
                logger.info(
                    f"SSE: Found {len(stream_messages)} historical messages since {since}"
                )

                # Process each historical message
                for msg_id, msg_data in stream_messages:
                    if "data" in msg_data:
                        try:
                            payload = json.loads(msg_data["data"])
                            # Only process messages for this request_id
                            if payload.get("request_id") == request_id:
                                # Add to the queue to be sent to the client
                                await queue.put(payload)
                                logger.debug(
                                    f"SSE: Queued historical message {msg_id} for request_id: {request_id}"
                                )
                        except json.JSONDecodeError:
                            logger.warning(
                                f"SSE: Could not decode historical message data: {msg_data}"
                            )
                    else:
                        logger.warning(
                            f"SSE: Historical message {msg_id} has no 'data' field"
                        )
            else:
                logger.info(f"SSE: No historical messages found since {since}")
        except Exception as e:
            logger.error(f"SSE: Error fetching historical messages: {e}")

    async def event_generator():
        message_count = 0
        # This `try...finally` block is crucial for resource cleanup.
        try:
            # The `while True` loop keeps the connection open to send multiple events.
            while True:
                # CHECK 1: Has the client disconnected?
                # This is vital to prevent orphaned server processes.
                if await request.is_disconnected():
                    logger.info(
                        f"SSE: Client for request_id: {request_id} disconnected after receiving {message_count} messages."
                    )
                    break

                try:
                    # Wait for a message from the central listener's queue.
                    # This does NOT block a Redis connection.
                    logger.debug(
                        f"SSE: Waiting for message for request_id: {request_id}"
                    )
                    message_data = await asyncio.wait_for(queue.get(), timeout=25)
                    logger.info(
                        f"SSE: Received message for request_id: {request_id} - {message_data.get('type', 'unknown-type')}"
                    )

                    # --- START: CORRECTED SSE MESSAGE CONSTRUCTION ---

                    # 1. Prepare the individual fields from your message data
                    event_id = message_data.get("id")
                    event_type = message_data.get("event_type")

                    # The 'data' field should contain the actual payload as a JSON string.
                    # We exclude the SSE-specific fields from this payload.
                    event_payload_dict = {
                        k: v
                        for k, v in message_data.items()
                        if k not in {"event_type", "type", "request_id", "id"}
                    }
                    data_json = json.dumps(event_payload_dict)

                    # 2. Build the SSE message string piece by piece.
                    # This is more robust than a single f-string.
                    sse_message_lines = []
                    if event_id:
                        # Handle if ID comes as bytes from Redis
                        if isinstance(event_id, bytes):
                            event_id = event_id.decode("utf-8")
                        sse_message_lines.append(f"id: {event_id}")

                    if event_type:
                        # The correct SSE field is 'event', not 'event_type'
                        sse_message_lines.append(f"event: {event_type}")

                    sse_message_lines.append(f"data: {data_json}")

                    # 3. Join the lines and add the required double newline terminator
                    sse_message = "\n".join(sse_message_lines) + "\n\n"
                    message_count += 1
                    logger.debug(f"SSE Message {message_count}: {sse_message}")
                    yield sse_message

                    # --- END: CORRECTED SSE MESSAGE CONSTRUCTION ---

                    # After sending the final message, check if we should close the connection.
                    if message_data.get("is_final"):
                        logger.info(
                            f"Final message sent for request_id: {request_id}. Closing SSE connection."
                        )
                        break  # Exit the while loop to close the connection

                except asyncio.TimeoutError:
                    # CHECK 2: Keep-alive ping.
                    # If no message arrives, send a comment to prevent proxies
                    # from closing the connection due to inactivity.
                    logger.debug(
                        f"SSE: Sending keep-alive for request_id: {request_id}"
                    )
                    yield ": keep-alive\n\n"
        finally:
            # CLEANUP: This runs when the client disconnects. We remove the
            # queue from the manager to prevent memory leaks.
            logger.info(
                f"SSE: Cleaning up listener for request_id: {request_id} after {message_count} messages."
            )
            request.app.state.sse_manager.remove_listener(request_id, queue)

    return StreamingResponse(event_generator(), media_type="text/event-stream")


@streaming_router.get("/sse-diagnostics")
async def sse_diagnostics(request: Request):
    """
    Diagnostic endpoint to check the state of the SSE system.
    """
    sse_manager = request.app.state.sse_manager

    # Check if Redis is connected
    redis_connected = False
    try:
        ping_result = await request.app.state.redis_client.ping()
        if ping_result:
            redis_connected = True
        else:
            redis_connected = False
    except Exception as e:
        logger.error(f"Redis connection check failed: {e}")

    # Get active listeners
    active_listeners = {
        request_id: len(queues) for request_id, queues in sse_manager.listeners.items()
    }

    # Check if listener task is running
    listener_task_running = (
        sse_manager._listener_task is not None and not sse_manager._listener_task.done()
    )

    # Get stream info
    stream_info = None
    try:
        stream_info = await request.app.state.redis_client.xinfo_stream(
            sse_manager.stream_name
        )
    except Exception as e:
        logger.error(f"Failed to get stream info: {e}")

    return {
        "redis_connected": redis_connected,
        "listener_task_running": listener_task_running,
        "active_listeners": active_listeners,
        "stream_name": sse_manager.stream_name,
        "stream_info": stream_info,
    }
