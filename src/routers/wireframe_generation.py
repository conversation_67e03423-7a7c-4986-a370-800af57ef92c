import json
import random
from typing import Dict
from fastapi import APIRouter, Depends, HTTPException, Query
from fastapi.responses import JSONResponse

from src.dependencies import get_current_user
from src.model.request_schemas import (
    WireframeGeneratorRequest,
    WireframeRegenerateRequest,
)
from src.routers.code_generation import (
    handle_conversation_interaction,
    update_conversation_with_metadata,
)
from src.services.azure.ado_helper import AdoHelper
from src.services.database.project_service import ProjectDatabaseService
from src.services.wireframe_generation.wireframe_generator import (
    get_project_brief,
    get_wireframe_designs,
    regenerate_wireframe_designs,
    get_wireframe_intro,
)
from src.utils.helpers.name_formatter import format_repository_name
from src.utils.logger import AppLogger
from src.utils.decorators.avaplus_decorator import handle_exceptions_and_log

router = APIRouter()
logger = AppLogger(__name__).get_logger()
# Create an instance of the service
project_db_service = ProjectDatabaseService()


@handle_exceptions_and_log(logger)
@router.post("/generate")
async def generate_wireframe(
    payload: WireframeGeneratorRequest,
    user: str = Depends(get_current_user),
    user_signature: str = Query(...),
) -> JSONResponse:
    try:
        request = payload.model_dump()
        request["userSignature"] = user_signature
        project_brief = await get_project_brief(request)
        logger.info(f"Wireframe | Project brief: {project_brief}")

        project_id = await project_db_service.create_project(
            project_name=project_brief["name"],
            project_description=project_brief["description"],
            user_id=user,
            project_state="ACTIVE",
        )

        await project_db_service.create_or_update_project_settings(
            project_id=project_id,
            device=payload.device,
            framework="other",
            design_system="custom",
            generation_type=payload.project_type,
        )

        # Generate wireframes
        response = await get_wireframe_designs(payload)

        if response is None:
            return JSONResponse(
                status_code=500, content={"error": "Unable to process request"}
            )

        # Commit wireframe files to git
        repo_name = f"wireframe-{str(user)[-4:]}-{project_id[-4:]}"
        ado_helper = AdoHelper(repo_name, "azure-pipelines.yaml")

        repo_details = await ado_helper.create_repository(repo_name)
        # repo_details = repo_response["repository"]

        # wireframe_files = []
        # for idx, html_content in enumerate(response.get("html_files", [])):
        #     wireframe_files.append({
        #         "fileName": f"wireframes/page_{idx}.html",
        #         "content": html_content
        #     })

        commit_hash = await ado_helper.commit_multiple_files(
            repo_name=repo_name,
            files_to_commit=response,
            commit_message="🎨 Initial wireframe generation",
            branch="main",
            operation_type="add",
        )

        await project_db_service.create_or_update_repository_details(
            project_id=project_id,
            vcs_provider="azure_devops",
            clone_url=repo_details["remote_url"],
            deployment_provider="other",
        )

        await update_conversation_with_metadata(
            project_id=project_id,
            conversation_metadata=None,
            project_db_service=project_db_service,
        )

        # Handle conversation interaction (similar to code_generation.py)
        await handle_conversation_interaction(
            project_id=project_id,
            user_query=payload.prompt,
            ai_response="Generated initial wireframe designs based on your requirements",
            capsule_data=commit_hash,
            ui_metadata=response,
            project_db_service=project_db_service,
        )

        return JSONResponse(
            status_code=200,
            content={
                "wireframes": response,
                "commit_hash": commit_hash,
                "project_id": project_id,
            },
        )
    except Exception as e:
        logger.error(f"Wireframe | Endpoint error: {e}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={"error": f"Failed to generate wireframe: {str(e)}"},
        )


@handle_exceptions_and_log(logger)
@router.post("/regenerate")
async def regenerate_wireframe(
    payload: WireframeRegenerateRequest,
    user: str = Depends(get_current_user),
    user_signature: str = Query(...),
    project_id: str = Query(...),
) -> JSONResponse:
    """
    Regenerate wireframe designs based on existing code files and user request.

    Args:
        payload: WireframeRegenerateRequest containing the code files and user request

    Returns:
        JSONResponse: Regenerated wireframe code files or error message
    """
    try:
        logger.info("Wireframe Regenerate | Endpoint called")
        logger.info(
            f"Wireframe Regenerate | User request: {payload.user_request[:100]}..."
        )
        logger.info(f"Wireframe Regenerate | Code files count: {len(payload.code)}")

        response: list[Dict[str, str]] = await regenerate_wireframe_designs(payload)
        response_data = json.loads(response)
        logger.info(f"Wireframe Regenerate | Response: {response}")

        if response is None:
            logger.error("Wireframe Regenerate | Service returned None")
            return JSONResponse(
                status_code=500,
                content={
                    "error": "Unable to process the regeneration request currently!"
                },
            )
        repo_name = format_repository_name(project_id, user, project_id)[0]
        ado_helper = AdoHelper(repo_name, "azure-pipelines.yaml")

        # Write a function to convert the response to the format expected by the ADO helper , response is of the format [{"fileName":"", "content": ""}] but i require it to be of the format {"fileName": "<content>"}
        if not isinstance(response_data, list):
            logger.warning(f"Expected list, got {type(response_data)}")
            return []

        converted_files = []
        for item in response_data:
            if isinstance(item, dict) and "fileName" in item and "content" in item:
                converted_files.append(
                    {"fileName": item["fileName"], "content": item["content"]}
                )
            else:
                logger.warning(f"Skipping invalid item format: {type(item)}")
        # wireframe_files = []
        # for idx, html_content in enumerate(response.get("html_files", [])):
        #     wireframe_files.append({
        #         "fileName": f"wireframes/page_{idx}.html",
        #         "content": html_content
        #     })

        commit_hash = await ado_helper.commit_multiple_files(
            repo_name=repo_name,
            files_to_commit=converted_files,
            commit_message="🎨 Wireframe regeneration",
            branch="main",
        )

        replies = [
            "I’ve updated the wireframe designs to match your latest requirements—let me know if there’s anything else you’d like tweaked!"
            "The wireframe designs have been refreshed based on your new inputs—hope they align with your vision!"
            "I’ve regenerated the wireframe designs with your updated specs—excited to hear your thoughts!"
            "Your updated requirements have been applied to the wireframe designs—let me know if there’s anything else to adjust!"
            "I’ve fine-tuned the wireframe designs to reflect your changes—happy to make further updates if needed!"
        ]
        reply = random.choice(replies)
        await handle_conversation_interaction(
            project_id=project_id,
            user_query=payload.user_request,
            ai_response=reply,
            capsule_data=commit_hash,
            ui_metadata=response,
            project_db_service=project_db_service,
        )

        logger.info("Wireframe Regenerate | Request completed successfully")
        return JSONResponse(status_code=200, content=response)

    except Exception as e:
        logger.error(f"Wireframe Regenerate | Endpoint error: {e}", exc_info=True)
        return JSONResponse(
            status_code=500,
            content={"error": f"Failed to regenerate wireframe: {str(e)}"},
        )


@handle_exceptions_and_log(logger)
@router.post("/intro")
async def intro_wireframe(payload: WireframeRegenerateRequest) -> str:
    """
    Generate intro message for wireframe designs based on user request.

    Args:
        payload: WireframeRegenerateRequest containing the optional code files and user request

    Returns:
        str: A intro message for user about the project or request
    """
    try:
        logger.info("Wireframe Intro | Endpoint called")

        response = await get_wireframe_intro(payload)

        if response is None:
            logger.error("Wireframe Intro | Service returned None")
            raise HTTPException(
                status_code=500, detail="Unable to process the request currently!"
            )

        logger.info("Wireframe Intro | Request completed successfully")
        return response

    except Exception as e:
        logger.error(f"Wireframe Intro | Endpoint error: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to generate wireframe intro: {str(e)}"
        )
