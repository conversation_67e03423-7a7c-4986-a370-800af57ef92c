# in a new file, e.g., utils/redis_stream.py or similar

import asyncio
import redis.asyncio as redis
import json
import logging
from typing import Optional, Dict, Any
import time

from src.model.request_schemas import EnumEncoder


class RedisStreamPublisher:
    """
    A class to handle publishing messages to Redis Streams.
    """

    def __init__(
        self,
        redis_client: redis.Redis,
        shared_stream_name: str,
        default_maxlen: int = 1000,
        default_expire_seconds: int = 3600,  # 1 hour
    ):
        """
        Initializes the RedisStreamPublisher.

        Args:
            redis_client: An initialized redis.asyncio.Redis client instance.
            shared_stream_name: The single stream name to publish all updates to.
            default_maxlen: Default maximum number of entries in the stream (approximate).
            default_expire_seconds: Default expiration time for the stream in seconds.
                                   Set to 0 or None to not set an expiry by default.
        """
        # if not isinstance(redis_client, redis.Redis):
        #     # Ensure we get an async client, not the sync one by mistake
        #     if hasattr(redis_client, 'xadd') and not asyncio.iscoroutinefunction(redis_client.xadd):
        #             raise TypeError("redis_client must be an instance of redis.asyncio.Redis (async version).")
        #     elif not hasattr(redis_client, 'xadd'): # Basic check if it looks like a redis client
        #             raise TypeError("redis_client does not appear to be a valid Redis client instance.")

        self.redis_client = redis_client
        self.shared_stream_name = shared_stream_name
        self.default_maxlen = default_maxlen
        self.default_expire_seconds = default_expire_seconds
        self.logger = logging.getLogger(self.__class__.__name__)
        self.logger.info(
            f"RedisStreamPublisher initialized. Default MAXLEN=~{default_maxlen}, Default EXPIRE={default_expire_seconds}s"
        )

    def _generate_stream_name(self, stream_key_prefix: str, identifier: str) -> str:
        """Helper to consistently generate stream names."""
        return f"{stream_key_prefix}:{identifier}"

    async def publish_message(
        self,
        stream_key_prefix: str,
        identifier: str,
        message_data: Dict[str, Any],
        maxlen: Optional[int] = None,
        expire_seconds: Optional[int] = None,
        message_field_name: str = "data",
        max_retries: int = 3,
    ) -> Optional[str]:
        """
        [DEPRECATED for project updates] Publishes a message to the shared stream.

        This method has been refactored to redirect to `publish_project_update`
        to ensure all messages go to the single shared stream for SSE scalability.

        Args:
            stream_key_prefix: The prefix for the stream key (e.g., "project_updates").
            identifier: A unique identifier to append to the prefix (e.g., request_id).
            message_data: A dictionary containing the message payload.
        """
        self.logger.warning(
            "The 'publish_message' method is deprecated and was called from prefix '%s'. "
            "Redirecting to 'publish_project_update'. Please update the calling code.",
            stream_key_prefix,
        )
        return await self.publish_project_update(
            request_id=identifier, state_data=message_data
        )

    async def publish_project_update(
        self,
        request_id: str,
        state_data: Dict[str, Any],
    ) -> Optional[str]:
        """
        Publishes a project state update to the single, shared Redis stream.
        The entire payload is serialized into a single JSON string.
        """
        # Create the full message object.
        message_to_publish = state_data.copy()
        message_to_publish["request_id"] = request_id
        # 'type' is used by the SSEManager to distinguish from other stream messages.
        message_to_publish["type"] = state_data.get("event_type", "initial-code-gen")

        # Add an event_id if not already present
        if "id" not in message_to_publish:
            # Generate a timestamp-based ID similar to Redis stream IDs (time-sequence format)
            current_time_ms = int(time.time() * 1000)  # Current time in milliseconds
            sequence = 0  # You can increment this if needed for multiple messages in same millisecond
            message_to_publish["id"] = f"{current_time_ms}-{sequence}"

        try:
            serialized_payload = json.dumps(message_to_publish, cls=EnumEncoder)
            redis_payload = {"data": serialized_payload}

            # --- ENHANCED LOGGING ---
            self.logger.info(
                f"PRODUCER: Publishing to stream '{self.shared_stream_name}' for request_id '{request_id}', type: {message_to_publish['type']}"
            )
            # --- END OF LOGGING ---

        except TypeError as e:
            self.logger.error(
                f"PRODUCER: Error serializing message: {e}", exc_info=True
            )
            return None

        # Publish to the single shared stream with retry logic
        max_retries = 3
        for retry in range(max_retries):
            try:
                message_id = await self.redis_client.xadd(
                    name=self.shared_stream_name,
                    fields=redis_payload,
                    maxlen=self.default_maxlen,
                    approximate=True,
                )
                self.logger.info(
                    f"PRODUCER: Successfully published message ID {message_id} to stream {self.shared_stream_name} for request_id {request_id}"
                )

                # Verify the message was actually stored in Redis
                try:
                    # Read the message back to confirm it's there
                    result = await self.redis_client.xrange(
                        self.shared_stream_name, min=message_id, max=message_id
                    )
                    if result:
                        self.logger.info(
                            f"PRODUCER: Verified message {message_id} is in Redis stream"
                        )
                    else:
                        self.logger.warning(
                            f"PRODUCER: Message {message_id} not found in Redis after publishing!"
                        )
                except Exception as verify_err:
                    self.logger.warning(
                        f"PRODUCER: Could not verify message {message_id}: {verify_err}"
                    )

                return message_id
            except (redis.ConnectionError, redis.TimeoutError) as e:
                if retry < max_retries - 1:
                    backoff_time = 0.5 * (2**retry)  # Exponential backoff
                    self.logger.warning(
                        f"Redis connection error (attempt {retry+1}/{max_retries}) publishing to stream {self.shared_stream_name}: {e}. "
                        f"Retrying in {backoff_time:.2f} seconds..."
                    )
                    await asyncio.sleep(backoff_time)
                else:
                    self.logger.warning(
                        f"Redis connection error publishing to stream {self.shared_stream_name}: {e}. "
                        f"Continuing without Redis update."
                    )
            except Exception as e:
                self.logger.error(
                    f"Unexpected error publishing to stream {self.shared_stream_name}: {e}",
                    exc_info=True,
                )
                break
        return None
