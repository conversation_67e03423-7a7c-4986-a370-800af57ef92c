import os
from dotenv import load_dotenv
from typing import Optional, Dict, Any
from src.core.dependencies import get_settings

# Load environment variables from a .env file if present
load_dotenv()


class SettingsLoader:
    _instance = None
    _api_settings: Optional[Dict[str, Any]] = None
    _settings_loaded = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    async def load_api_settings(self):
        """Load settings from API at startup"""
        if not self._settings_loaded:
            try:
                self._api_settings = await get_settings()
                self._settings_loaded = True
            except Exception as e:
                print(f"Failed to load API settings: {e}")
                self._api_settings = {}
                self._settings_loaded = True

    def get_setting(self, key: str, default: str = "") -> str:
        """Get setting from API first, then environment variable"""
        if self._api_settings:
            # Search through the list of config objects
            for config in self._api_settings:
                if config.get("configKey") == key:
                    value = config.get("configValue", default)
                    print(f"Found API setting: {key} = {value}")
                    return value

        # Fallback to environment variable
        env_value = os.getenv(key, default)
        print(f"Using env setting: {key} = {env_value}")
        return env_value


# Global settings loader instance
settings_loader = SettingsLoader()


class AzureSettings:
    def __init__(self):
        self.BLOB_STORAGE_URL: str = settings_loader.get_setting(
            "app.storage.sas.url", ""
        )
        self.STORAGE_SAS_TOKEN: str = settings_loader.get_setting(
            "app.storage.sas.token", ""
        )
        self.AZURE_TEMPLATE_STORAGE_URL: str = settings_loader.get_setting(
            "app.storage.template.sas.url", ""
        )
        self.AZURE_TEMPLATE_STORAGE_TOKEN: str = settings_loader.get_setting(
            "app.storage.template.sas.token", ""
        )

        self._validate()

    def _validate(self):
        if not self.BLOB_STORAGE_URL:
            raise ValueError("AZURE_STORAGE_SAS_URL is not set")
        if not self.STORAGE_SAS_TOKEN:
            raise ValueError("AZURE_STORAGE_SAS_TOKEN is not set")
        if not self.AZURE_TEMPLATE_STORAGE_URL:
            raise ValueError("AZURE_TEMPLATE_STORAGE_URL is not set")
        if not self.AZURE_TEMPLATE_STORAGE_TOKEN:
            raise ValueError("AZURE_TEMPLATE_STORAGE_TOKEN is not set")


class RedisConfig:
    def __init__(self):
        self.REDIS_URL: str = settings_loader.get_setting("app.cache.redis.url", "")
        # Add timeout settings to fix connection issues
        self.REDIS_SOCKET_TIMEOUT: int = int(
            settings_loader.get_setting("app.cache.redis.socket.timeout", "30")
        )
        self.REDIS_SOCKET_CONNECT_TIMEOUT: int = int(
            settings_loader.get_setting("app.cache.redis.socket.connect.timeout", "10")
        )
        self.REDIS_RETRY_ON_TIMEOUT: bool = (
            settings_loader.get_setting(
                "app.cache.redis.socket.retry.timeout", "true"
            ).lower()
            == "true"
        )
        self._validate()

    def _validate(self):
        if not self.REDIS_URL:
            raise ValueError("REDIS_URL is not set")


class DACreds:
    def __init__(self):
        self.DA_USER_SIGNATURE: str = settings_loader.get_setting(
            "app.da.signature", ""
        )
        self.DA_BASE_URL: str = settings_loader.get_setting("app.da.url", "")
        self.DA_API_ENDPOINT: str = settings_loader.get_setting("app.da.endpoint", "")
        self.DA_ACCESS_KEY: str = settings_loader.get_setting("app.da.key", "")
        self.DA_USECASE_IDENTIFIER: str = settings_loader.get_setting(
            "app.da.studio.identifier", ""
        )
        self.DA_API_URL: str = f"{self.DA_BASE_URL}/{self.DA_API_ENDPOINT}"

        self._validate()

    def _validate(self):
        if not self.DA_USER_SIGNATURE:
            raise ValueError("DA_USER_SIGNATURE is not set")
        if not self.DA_BASE_URL:
            raise ValueError("DA_PLATFORM_BASEURL is not set")
        if not self.DA_API_ENDPOINT:
            raise ValueError("DA_PLATFORM_ENDPOINT is not set")
        if not self.DA_ACCESS_KEY:
            raise ValueError("DA_API_KEY is not set")
        if not self.DA_USECASE_IDENTIFIER:
            raise ValueError("DA_USECASE_IDENTIFIER is not set")


class DevConfig:
    def __init__(self):
        self.ENABLE_AUTH: str = settings_loader.get_setting("ENABLE_AUTH", "false")
        self.DEPLOY_PER_USER: str = settings_loader.get_setting(
            "app.config.deploy.per.user", "false"
        )
        self.API_RETRY_WAIT_TIME: int = 2


class AzureADOConfig:
    def __init__(self):
        self.ADO_PAT: str = settings_loader.get_setting(
            "app.integration.azure.devops.pat", ""
        )
        self.ADO_ORG: str = settings_loader.get_setting(
            "app.integration.azure.devops.org", ""
        )
        self.ADO_PROJECT: str = settings_loader.get_setting(
            "app.integration.azure.devops.project", ""
        )
        self._validate()

    def _validate(self):
        if not self.ADO_PAT:
            raise ValueError("ADO_PAT is not set")
        if not self.ADO_ORG:
            raise ValueError("ADO_ORG is not set")
        if not self.ADO_PROJECT:
            raise ValueError("ADO_PROJECT is not set")


class ChromaDBConfig:
    def __init__(self):
        self.CHROMA_HOST: str = settings_loader.get_setting(
            "app.database.chroma.host", "localhost"
        )
        self.CHROMA_PORT: str = settings_loader.get_setting(
            "app.database.chroma.port", "8000"
        )
        self.COLLECTION_NAME: str = settings_loader.get_setting(
            "app.database.chroma.collection", ""
        )
        self._validate()

    def _validate(self):
        if not self.COLLECTION_NAME:
            raise ValueError("COLLECTION_NAME is not set")
        if not self.CHROMA_HOST:
            raise ValueError("CHROMA_HOST is not set")
        if not self.CHROMA_PORT:
            raise ValueError("CHROMA_PORT is not set")


class PostgresDBConfig:
    def __init__(self):
        self.DB_NAME = settings_loader.get_setting("app.database.name", "")
        self.DB_USER = settings_loader.get_setting("app.database.user", "")
        self.DB_PASSWORD = settings_loader.get_setting("app.database.password", "")
        self.DB_HOST = settings_loader.get_setting("app.database.host", "")
        self.DB_PORT = settings_loader.get_setting("app.database.port", "")
        self._validate()

    def _validate(self):
        if not self.DB_NAME:
            raise ValueError("DB_NAME is not set")
        if not self.DB_USER:
            raise ValueError("DB_USER is not set")
        if not self.DB_PASSWORD:
            raise ValueError("DB_PASSWORD is not set")
        if not self.DB_HOST:
            raise ValueError("DB_HOST is not set")
        if not self.DB_PORT:
            raise ValueError("DB_PORT is not set")


class PixabayConfig:
    def __init__(self):
        self.PIXABAY_API_KEY: str = settings_loader.get_setting(
            "app.integration.pixabay.api", ""
        )
        self._validate()

    def _validate(self):
        if not self.PIXABAY_API_KEY:
            raise ValueError("PIXABAY_API_KEY is not set")


class Settings:
    _instance = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        # Don't initialize immediately - wait for explicit call
        if not hasattr(self, "_initialized"):
            self._initialized = False

    def initialize(self):
        """Initialize settings after API data is loaded"""
        if not self._initialized:
            self._load_settings()
            self._initialized = True

    def _load_settings(self):
        """Load all settings - called after API settings are available"""
        self.azure = AzureSettings()
        self.da_creds = DACreds()
        self.devConfig = DevConfig()
        self.azure_ado_config = AzureADOConfig()
        self.redis_config = RedisConfig()
        self.chromadb_config = ChromaDBConfig()
        self.pixabay_config = PixabayConfig()

    def reload_settings(self):
        """Reload all settings after API refresh"""
        self._load_settings()


# Don't instantiate at module level
# settings = Settings()  # Remove this line
