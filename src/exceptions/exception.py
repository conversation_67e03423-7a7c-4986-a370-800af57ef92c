# src/exceptions/custom_exceptions.py
from typing import Optional, List, Union, Any, Dict
from fastapi import HTTPException
from pydantic import ValidationError

from src.model.error_codes import APIErrorCode
from src.model.error_response import ErrorDetail  # For typing


class BaseAPIException(
    HTTPException
):  # Inherit from HTTPException for FastAPI compatibility
    def __init__(
        self,
        status_code: int,
        error_code: APIErrorCode,
        message: Optional[str] = None,
        details: Optional[Union[List[ErrorDetail], str, Dict[str, Any]]] = None,
        headers: Optional[Dict[str, str]] = None,
    ):
        # If no message is provided, use the error_code's value (name) as a default message
        self.message = (
            message
            if message is not None
            else error_code.name.replace("_", " ").title()
        )
        self.error_code = error_code
        self.details = details
        # The content for JSONResponse will be built by the exception handler
        # For HTTPException, 'detail' is what gets sent, but we'll customize it.
        super().__init__(status_code=status_code, detail=self.message, headers=headers)


class DAValueError(BaseAPIException):
    def __init__(
        self,
        message: str = "A business logic validation error occurred.",
        error_code: APIErrorCode = APIErrorCode.BUSINESS_LOGIC_ERROR,
        details: Optional[Union[List[ErrorDetail], str, Dict[str, Any]]] = None,
        status_code: int = 400,  # Default to 400 for DAValueErrors
    ):
        super().__init__(
            status_code=status_code,
            error_code=error_code,
            message=message,
            details=details,
        )


class DAServerError(BaseAPIException):
    def __init__(
        self,
        message: str = "A business logic validation error occurred due to platform instruction being down",
        error_code: APIErrorCode = APIErrorCode.BUSINESS_LOGIC_ERROR,
        details: Optional[Union[List[ErrorDetail], str, Dict[str, Any]]] = None,
        status_code: int = 500,  # Default to 400 for DAValueErrors
    ):
        super().__init__(
            status_code=status_code,
            error_code=error_code,
            message=message,
            details=details,
        )


class DBConnectionError(BaseAPIException):
    def __init__(
        self,
        message: str = "There is something wrong in connecting with the DB.",
        error_code: APIErrorCode = APIErrorCode.DB_CONNECTION_ERROR,
        details: Optional[Union[List[ErrorDetail], str, Dict[str, Any]]] = None,
        status_code: int = 500,  # Default to 400 for DAValueErrors
    ):
        super().__init__(
            status_code=status_code,
            error_code=error_code,
            message=message,
            details=details,
        )


class ResourceNotFoundException(BaseAPIException):
    def __init__(
        self,
        resource_name: str,
        identifier: Any,
        message: Optional[str] = None,
        details: Optional[Union[List[ErrorDetail], str, Dict[str, Any]]] = None,
    ):
        msg = message or f"{resource_name} with identifier '{identifier}' not found."
        super().__init__(
            status_code=404,
            error_code=APIErrorCode.RESOURCE_NOT_FOUND,
            message=msg,
            details=details,
        )


class ValidationAPIError(BaseAPIException):
    def __init__(
        self,
        validation_error: ValidationError,  # Pass the original Pydantic error
        message: str = "Input validation failed.",
    ):
        details_list = []
        for error in validation_error.errors():
            details_list.append(
                ErrorDetail(
                    loc=list(error["loc"]),
                    msg=error["msg"],
                    type=error["type"],
                    ctx=error.get("ctx"),
                )
            )

        super().__init__(
            status_code=422,  # Unprocessable Entity for validation errors
            error_code=APIErrorCode.VALIDATION_ERROR,
            message=message,
            details=details_list,
        )
