from fastapi import HTTPException, Query
from src.services.database.project_service import (
    ProjectDatabaseService as ProjectService,
)


async def get_current_user(user_signature: str = Query(...)):
    try:
        user_id = await ProjectService.get_or_create_user(user_signature)
        return user_id
    except Exception as e:
        raise HTTPException(
            status_code=400, detail=f"User could not be created: {str(e)}"
        )
