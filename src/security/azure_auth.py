from fastapi import HTT<PERSON><PERSON><PERSON><PERSON>, Security
from fastapi.security import <PERSON>Auth<PERSON><PERSON>uthori<PERSON><PERSON><PERSON><PERSON>ear<PERSON>
from msal import ConfidentialClientApplication
from starlette.status import HTTP_401_UNAUTHORIZED

# Import settings from the settings folder
from ..settings.settings import settings

# Load environment variables from settings
CLIENT_ID = settings.azure.CLIENT_ID
CLIENT_SECRET = settings.azure.CLIENT_SECRET
TENANT_ID = settings.azure.TENANT_ID
AUTHORITY = settings.azure.AUTHORITY
SCOPE = settings.azure.SCOPE

oauth2_scheme = OAuth2AuthorizationCodeBearer(
    authorizationUrl=f"{AUTHORITY}/oauth2/v2.0/authorize",
    tokenUrl=f"{AUTHORITY}/oauth2/v2.0/token",
)


def verify_token(token: str = Security(oauth2_scheme)):
    app = ConfidentialClientApplication(
        CLIENT_ID,
        authority=AUTHORITY,
        client_credential=CLIENT_SECRET,
    )

    result = app.acquire_token_silent(SCOPE, account=None)
    if not result:
        result = app.acquire_token_for_client(scopes=SCOPE)

    if "access_token" not in result:
        raise HTTPException(
            status_code=HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

    return result["access_token"]
