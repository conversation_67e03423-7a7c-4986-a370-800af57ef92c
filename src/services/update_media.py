import os
import json
import asyncio
import aiohttp
import time
import itertools

# --- Dependencies for ChromaDB ---
# You will need to install these: pip install chromadb sentence-transformers datasets tqdm

import chromadb
from chromadb.utils import embedding_functions
from datasets import load_dataset
from src.settings.settings import Settings
from tqdm import tqdm
from src.utils.logger import AppLogger

logger = AppLogger("Design Generation Service").get_logger()
settings = Settings()

# --- Classes (ImageSearchService, AsyncPixabayMediaReplacer) remain exactly the same ---
# ... (Keep all your class definitions here, no changes needed) ...

class ImageSearchService:
    """
    A service class to handle ingestion and querying of image-caption data
    in a ChromaDB collection.
    """
    def __init__(self):
        """
        Initializes the service, loads configuration from .env, and connects to ChromaDB.
        """
        logger.info("🚀 Initializing ImageSearchService...")
        chromadb_config = settings.chromadb_config
        self.host = chromadb_config.CHROMA_HOST
        self.port = chromadb_config.CHROMA_PORT
        self.collection_name = chromadb_config.COLLECTION_NAME
        self.ssl = True
        self._connect()

    def _connect(self):
        """Establishes a connection to the ChromaDB server and collection."""
        try:
            embedding_func = embedding_functions.SentenceTransformerEmbeddingFunction(model_name="all-MiniLM-L6-v2")
            
            self.client = chromadb.HttpClient(host=self.host, port=self.port)
            self.collection = self.client.get_or_create_collection(
                name=self.collection_name,
                embedding_function=embedding_func,
                metadata={"hnsw:space": "cosine"}
            )
            logger.info(f"✅ Successfully connected to ChromaDB. Collection '{self.collection_name}' has {self.collection.count()} items.")
        except Exception as e:
            logger.debug(f"❌ Failed to connect to ChromaDB: {e}")
            raise ConnectionError("Could not establish connection to ChromaDB.") from e
        
    def _get_dataset_size(self, hf_dataset_name):
        """
        Returns the total number of items in a Hugging Face dataset.
        This is useful for determining how many items to ingest.
        """
        try:
            dataset = load_dataset(hf_dataset_name, split='train')
            size = len(dataset)
            logger.info(f"Dataset '{hf_dataset_name}' has {size} items.")
            return size
        except Exception as e:
            logger.error(f"Failed to load dataset '{hf_dataset_name}': {e}")
            raise ValueError(f"Could not load dataset '{hf_dataset_name}'.") from e

    def ingest_from_huggingface(self, hf_dataset_name="visheratin/unsplash-caption-questions-init", url_column='url', caption_column='caption'):
        """
        Ingests data from a Hugging Face dataset, with resumable logic.
        """
        start_count = self.collection.count()
        logger.info(f"\n📥 Starting ingestion for '{hf_dataset_name}'. Collection already has {start_count} items.")

        total_items = self._get_dataset_size(hf_dataset_name)
        logger.info(f"Total size of imported dataset: {total_items}")

        if start_count >= total_items:
            logger.info("   - Collection is already full. No new items to ingest.")
            return

        dataset = load_dataset(hf_dataset_name, split='train', streaming=True)
        resumable_dataset = itertools.islice(dataset, start_count, None)

        batch_size = 200
        batch = {"ids": [], "documents": [], "metadatas": []}

        for i, row in enumerate(tqdm(resumable_dataset, total=total_items, initial=start_count, desc="Ingesting Batches")):
            if not row[caption_column] or not row[url_column]:
                continue # Skip rows with missing data
                
            batch["documents"].append(row[caption_column])
            batch["metadatas"].append({url_column: row[url_column]})
            batch["ids"].append(f"id_{start_count + i}")
            
            if len(batch["ids"]) >= batch_size:
                self.collection.add(ids=batch["ids"], documents=batch["documents"], metadatas=batch["metadatas"])
                batch = {"ids": [], "documents": [], "metadatas": []}

        if batch["ids"]:
            self.collection.add(ids=batch["ids"], documents=batch["documents"], metadatas=batch["metadatas"])
        logger.info(f"✅ Ingestion complete! Collection now has {self.collection.count()} items.")

    def find_best_images(self, input_data: dict) -> dict:
        """
        Takes a dictionary of {description: placeholder_url} and returns a new
        dictionary with URLs updated from a ChromaDB semantic search.
        """
        search_queries = list(input_data.keys())
        if not search_queries:
            return {}
            
        logger.info(f"🔍 Performing batch ChromaDB search for {len(search_queries)} queries...")
        results = self.collection.query(query_texts=search_queries, n_results=1)

        updated_images = {}
        for i, query in enumerate(search_queries):
            # Check if the query returned any results
            if results and results.get('metadatas') and results['metadatas'][i]:
                metadata = results['metadatas'][i][0]
                new_url = metadata.get('url', input_data[query]) # Use original as fallback
            else:
                new_url = input_data[query] # If no match, keep original
            updated_images[query] = new_url
            
        return updated_images

class AsyncPixabayMediaReplacer:
    """
    Asynchronously replaces dummy media URLs, with a built-in fallback to a local
    ChromaDB service if the live API fails.
    """
    IMAGE_API_URL = "https://pixabay.com/api/"
    VIDEO_API_URL = "https://pixabay.com/api/videos/"

    def __init__(self, api_key: str, fallback_service:ImageSearchService | None):
        if not api_key:
            raise ValueError("Pixabay API key is required.")
        self.api_key = api_key
        self.video_keywords = ["video"]
        self.fallback_service = fallback_service
        if self.fallback_service:
            logger.info("✅ Orchestrator configured with ChromaDB fallback service.")

    def prepare_task_list(self, original_input_data: list) -> list:
        tasks_to_run = []
        for page_group in original_input_data:
            for page_name, items_dict in page_group.items():
                media_type = "video" if any(k in page_name.lower() for k in self.video_keywords) else "image"
                for caption, dummy_url in items_dict.items():
                    tasks_to_run.append({"caption": caption, "dummy_url": dummy_url, "media_type": media_type})
        return tasks_to_run

    async def _fetch_media_from_api(self, session: aiohttp.ClientSession, query: str, media_type: str) -> str | None:
        base_url = self.IMAGE_API_URL if media_type == "image" else self.VIDEO_API_URL
        params = {"key": self.api_key, "q": query, "per_page": 3, "safesearch": "true"}
        if media_type == 'image':
            params['orientation'] = 'horizontal'
        try:
            async with session.get(base_url, params=params) as response:
                response.raise_for_status()
                data = await response.json()
                if data.get("hits"):
                    return data["hits"][0]["largeImageURL"] if media_type == "image" else data["hits"][0]["videos"]["medium"]["url"]
                return None
        except aiohttp.ClientError as e:
            raise e

    async def fetch_all_media(self, prepared_tasks: list) -> dict:
        final_mapping = {}
        
        async with aiohttp.ClientSession() as session:
            coroutines = [self._fetch_media_from_api(session, task['caption'], task['media_type']) for task in prepared_tasks]
            logger.info(f"Executing {len(coroutines)} Pixabay API calls concurrently...")
            pixabay_results = await asyncio.gather(*coroutines, return_exceptions=True)

        tasks_for_fallback = []
        for task, result in zip(prepared_tasks, pixabay_results):
            if isinstance(result, Exception) or result is None:
                tasks_for_fallback.append(task)
            else:
                final_mapping[task['dummy_url']] = result
        
        if tasks_for_fallback:
            if self.fallback_service:
                # IMPORTANT: We only want to fallback for IMAGE tasks, as ChromaDB has images.
                # You can adjust this logic if your fallback service supports video.
                image_fallback_tasks = [t for t in tasks_for_fallback if t['media_type'] == 'image']
                video_fallback_tasks = [t for t in tasks_for_fallback if t['media_type'] == 'video']

                if image_fallback_tasks:
                    fallback_input_dict = {task['caption']: task['dummy_url'] for task in image_fallback_tasks}
                    try:
                        fallback_results_dict = await asyncio.to_thread(self.fallback_service.find_best_images, fallback_input_dict)
                        for task in image_fallback_tasks:
                            caption, dummy_url = task['caption'], task['dummy_url']
                            retrieved_url = fallback_results_dict.get(caption)
                            if retrieved_url and retrieved_url != dummy_url:
                                logger.info(f" 👍 -> ChromaDB Success for '{caption[:40]}...'")
                                final_mapping[dummy_url] = retrieved_url
                            else:
                                logger.warning(f"  -> ChromaDB Fallback also failed for '{caption[:40]}...'.")
                                final_mapping[dummy_url] = "ERROR_FALLBACK_FAILED"
                    except Exception as e:
                        logger.error(f"❌ CRITICAL ERROR during ChromaDB fallback batch: {e}")
                        for task in image_fallback_tasks:
                            final_mapping[task['dummy_url']] = "ERROR_FALLBACK_SERVICE_EXCEPTION"
                
                # For tasks that were videos or for which there's no applicable fallback
                for task in video_fallback_tasks:
                     final_mapping[task['dummy_url']] = "ERROR_API_CALL_FAILED_NO_FALLBACK"

            else:
                logger.warning("\n  -> Fallback service not configured. Marking all failed tasks.")
                for task in tasks_for_fallback:
                    final_mapping[task['dummy_url']] = "ERROR_API_CALL_FAILED_NO_FALLBACK"
        
        return final_mapping
    
    # --- NEW CORRECTED METHOD ---
    def reconstruct_output(self, original_input_data: list, url_mapping: dict) -> list:
        reconstructed_data = []
        for page_group in original_input_data:
            new_page_group = {}
            for page_name, items_dict in page_group.items():
                # Create a new dict {dummy_url: new_url}
                new_items_dict = {
                    dummy_url: url_mapping.get(dummy_url, "ERROR_MAPPING_NOT_FOUND")
                    for dummy_url in items_dict.values()
                }
                new_page_group[page_name] = new_items_dict
            reconstructed_data.append(new_page_group)
        return reconstructed_data


# --- NEW: Core Reusable Function ---
async def process_media_requests(input_data_raw: list) -> list:
    """
    This is the core logic, designed to be imported and called from other scripts.
    It takes a data structure, processes it, and returns the result.
    """
    logger.info("🔄 Starting media processing...")
    # --- Parse Input Data ---
    input_data = []
    for data in input_data_raw:
        parsed_data = json.loads(data)
        if parsed_data:
            input_data.append(parsed_data)

    # print total images that needs to get processed
    logger.info(f"Total images to process: {sum(len(section) for page_group in input_data for section in page_group.values())}")

    pixabay_config = settings.pixabay_config
    PIXABAY_API_KEY = pixabay_config.PIXABAY_API_KEY

    if not PIXABAY_API_KEY:
        logger.error("FATAL: Pixabay API key not set in environment.")
        raise ValueError("Pixabay API key not configured.")

    try:
        chroma_fallback_service = ImageSearchService()
        # --- Optional: Ingest data into ChromaDB if needed ---
        # we will first check if our collection is empty and if yes then we will call ingest_from_huggingface
        if chroma_fallback_service.collection.count() == 0:
            logger.info("ChromaDB collection is empty. Ingesting initial dataset...")
            # Ingest the dataset from Hugging Face
            chroma_fallback_service.ingest_from_huggingface(
                hf_dataset_name="visheratin/unsplash-caption-questions-init",
                url_column='url',
                caption_column='caption'
            )
        else:
            logger.info(f"ChromaDB collection already has data. Skipping ingestion step. Database size: {chroma_fallback_service.collection.count()} items.")
            
    except Exception as e:
        logger.warning(f"⚠️ WARNING: Could not initialize ChromaDB fallback service: {e}")
        logger.warning("   The script will run without a fallback mechanism.")
    
    # --- Instantiate the main replacer, injecting the fallback service ---
    media_replacer = AsyncPixabayMediaReplacer(
        api_key=PIXABAY_API_KEY,
        fallback_service=chroma_fallback_service
    )
    
    # --- Run the 3-step process ---
    logger.info("Step 1: Preparing task list...")
    prepared_tasks = media_replacer.prepare_task_list(input_data)
    
    logger.info("Step 2: Fetching all media (with fallback)...")
    flat_url_mapping = await media_replacer.fetch_all_media(prepared_tasks)
    
    logger.info("Step 3: Reconstructing final output...")
    final_output = media_replacer.reconstruct_output(input_data, flat_url_mapping)
    
    return final_output

# --- MODIFIED: Main Execution Block for Standalone Running ---
# async def main():
#     """
#     The main entry point for running this script directly.
#     It defines sample data and demonstrates how to use `process_media_requests`.
#     """
#     start_time = time.time()
    
#     # --- Define Input Data for demonstration ---
#     # This is the same data you had before
#     sample_input_data = [
#        {
#                 "homepage": {
#                 "Kerala backwaters houseboat with tropical scenery": "https://homepage/image1.jpeg",
#                 "Rajasthan heritage forts and culture": "https://homepage/image2.jpeg",
#                 "Himalayan mountains with snow and trekking path": "https://homepage/image3.jpeg",
#                 "India travel hero section with scenic view": "https://homepage/image4.jpeg",
#                 "Colorful abstract background for travel offers": "https://homepage/image5.jpeg"
#                 },
#                 "dashboard": {
#                 "Kerala backwaters houseboat with tropical scenery": "https://dashboard/image1.jpeg",
#                 "Rajasthan heritage forts and culture": "https://dashboard/image2.jpeg",
#                 "Himalayan mountains with snow and trekking path": "https://dashboard/image3.jpeg",
#                 "India travel hero section with scenic view": "https://dashboard/image4.jpeg",
#                 "Colorful abstract background for travel offers": "https://dashboard/image5.jpeg"
#                 },
#                 "MainVideo": {
#                 "Kerala backwaters houseboat with tropical scenery": "https://MainVideo/video1.mp4",
#                 "Rajasthan heritage forts and culture": "https://MainVideo/video2.mp4",
#                 "Himalayan mountains with snow and trekking path": "https://MainVideo/video3.mp4",
#                 "India travel hero section with scenic view": "https://MainVideo/video4.mp4",
#                 "Colorful abstract background for travel offers": "https://MainVideo/video5.mp4"
#                 }
#             }
#         ]

#     # --- Ingest data into ChromaDB if needed (run once) ---
#     # try:
#     #     logger.info("Checking if ingestion is needed...")
#     #     chroma_service = ImageSearchService()
#     #     chroma_service.ingest_from_huggingface()
#     # except Exception as e:
#     #     logger.error(f"Could not perform ingestion: {e}")

#     # --- Call the core, reusable function ---
#     final_output = await process_media_requests(sample_input_data)
#     end_time = time.time()

#     # --- Handle the output (side effects like printing and saving) ---
#     output_json = json.dumps(final_output, indent=2)
#     logger.info(f"Total execution time: {end_time - start_time:.2f} seconds")
#     logger.info("Final JSON Output:")
#     logger.info(output_json)
    
#     output_filename = "media_links_output_final.json"
#     with open(output_filename, "w") as f:
#         f.write(output_json)
#     logger.info(f"\nOutput has been saved to '{output_filename}'")

# if __name__ == "__main__":
#     asyncio.run(main())