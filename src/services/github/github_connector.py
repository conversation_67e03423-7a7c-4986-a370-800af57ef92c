import asyncio
import base64
import json
from typing import Any, Dict, List

from fastapi import HTT<PERSON>Exception
from src.settings.settings import GithubConfig
from src.utils.api_helper.api_client import APIClient
from src.utils.logger import AppLogger


class GithubRepoConnector:
    def __init__(self):
        self._github_config = GithubConfig()
        self._base_url = "https://api.github.com"
        self.token = self._github_config.GH_PAT
        self._api_client = APIClient(
            base_url=self._base_url,
            headers={
                "Authorization": f"token {self.token}",
                "Accept": "application/vnd.github.v3+json",
                "X-GitHub-Api-Version": "2022-11-28",
            },
        )
        self.logger = AppLogger("- Github connector -").get_logger()

    async def create_repo_from_template(
        self,
        template_owner: str,
        template_repo_name: str,
        new_repo_name: str,
        new_repo_owner: str = None,
        description: str = "",
        private: bool = False,
        include_all_branches: bool = False,
    ) -> dict:
        if not all([template_owner, template_repo_name, new_repo_name]):
            raise ValueError(
                "template_owner, template_repo_name, and new_repo_name are required."
            )
        endpoint = f"repos/{template_owner}/{template_repo_name}/generate"
        payload = {
            "name": new_repo_name,
            "description": description,
            "private": private,
            "include_all_branches": include_all_branches,
        }
        if new_repo_owner:
            payload["owner"] = new_repo_owner

        try:
            print(
                f"GitHub: Attempting to create '{new_repo_name}' from template '{template_owner}/{template_repo_name}'..."
            )
            response = await self._api_client.post(endpoint, payload)
            created_repo_data = response
            print(
                f"GitHub: Successfully created repository: {created_repo_data.get('html_url')}"
            )
            return created_repo_data
        except Exception as e:
            print(f"GitHub: Request error occurred: {e}")
            raise

    async def _get_file_sha_async(
        self, repo_owner: str, repo_name: str, file_path: str, branch: str = None
    ) -> str | None:
        safe_file_path = file_path.lstrip("/")
        endpoint = f"repos/{repo_owner}/{repo_name}/contents/{safe_file_path}"
        params = {}
        if branch:
            params["ref"] = branch

        try:
            self.logger.info(
                f"Getting SHA for file '{safe_file_path}' in '{repo_owner}/{repo_name}' on branch '{branch or 'default'}'"
            )
            response_data = await self._api_client.get(endpoint, params=params)
            if isinstance(response_data, dict) and response_data.get("type") == "file":
                return response_data.get("sha")
            else:
                self.logger.info(
                    f"Path '{safe_file_path}' exists but is not a file or unexpected response type."
                )
                return None
        except HTTPException as e:
            if e.status_code == 404:  # File not found, this is okay for this helper.
                self.logger.info(
                    f"File '{safe_file_path}' not found (404), proceeding as new file."
                )
                return None
            # For other errors, re-raise or handle as per your app's error strategy
            self.logger.error(
                f"HTTPException getting file SHA for '{safe_file_path}': {e.detail}"
            )
            raise  # Or return None if you want to attempt commit anyway
        except (
            Exception
        ) as e:  # Catch broader exceptions from _handle_response if not HTTPException
            self.logger.error(f"Unexpected error getting file SHA: {e}")
            raise  # Or return None

    async def _commit_single_file_async(
        self,
        repo_owner: str,
        repo_name: str,
        file_name: str,
        file_content_str: str,
        commit_message: str,
        branch: str = None,
    ) -> Dict[str, Any] | None:
        """
        Commits a single file using the APIClient.
        Internal helper for commit_multiple_files_async.
        """
        safe_file_path = file_name.lstrip("/")
        try:
            content_bytes = file_content_str.encode("utf-8")
            base64_content = base64.b64encode(content_bytes).decode("utf-8")
        except Exception as e:
            self.logger.error(
                f"Error base64 encoding content for '{safe_file_path}': {e}"
            )
            # Raise a specific error or return a failure indicator
            raise ValueError(f"Base64 encoding failed for {safe_file_path}") from e

        endpoint = f"repos/{repo_owner}/{repo_name}/contents/{safe_file_path}"
        payload: Dict[str, Any] = {"message": commit_message, "content": base64_content}
        if branch:
            payload["branch"] = branch

        log_action = "create"
        try:
            current_file_sha = await self._get_file_sha_async(
                repo_owner, repo_name, safe_file_path, branch
            )
            if current_file_sha:
                payload["sha"] = current_file_sha
                log_action = "update"
        except Exception as e:  # Catch errors from _get_file_sha_async if it re-raises
            self.logger.error(
                f"Could not determine SHA for '{safe_file_path}', attempting create: {e}"
            )
            # Continue to attempt creation, GitHub will error if it exists and needs SHA

        self.logger.info(
            f"Attempting to {log_action} file '{safe_file_path}' in '{repo_owner}/{repo_name}' "
            f"on branch '{branch or 'default'}'"
        )
        try:
            commit_data = await self._api_client.put(endpoint, data=payload)
            self.logger.info(
                f"Successfully {log_action}d file '{safe_file_path}'. New Commit SHA: {commit_data.get('commit', {}).get('sha')}"
            )
            return {
                "fileName": safe_file_path,
                "status": "success",
                "action": log_action,
                "response": commit_data,
            }
        except HTTPException as e:
            self.logger.error(
                f"HTTPException during {log_action} of file '{safe_file_path}': {e.status_code} - {e.detail}"
            )
            return {
                "fileName": safe_file_path,
                "status": "failed",
                "action": log_action,
                "error": e.detail,
                "status_code": e.status_code,
            }
        except Exception as e:  # Catch broader exceptions
            self.logger.error(
                f"Unexpected error during {log_action} of file '{safe_file_path}': {str(e)}"
            )
            return {
                "fileName": safe_file_path,
                "status": "failed",
                "action": log_action,
                "error": str(e),
            }

    # async def commit_multiple_files_async(self,
    #                                       repo_owner: str,
    #                                       repo_name: str,
    #                                       files_to_commit: List[Dict[str, str]], # [{"fileName": "...", "content": "..."}]
    #                                       commit_message_prefix: str, # A prefix for all commit messages
    #                                       branch: str = None) -> List[Dict[str, Any]]:
    #     """
    #     Commits a list of files to a GitHub repository concurrently.
    #     Each file is committed individually.

    #     Args:
    #         repo_owner (str): Owner of the repository.
    #         repo_name (str): Name of the repository.
    #         files_to_commit (List[Dict[str, str]]): A list of dictionaries, where each
    #             dictionary must have "fileName" and "content" keys.
    #         commit_message_prefix (str): A prefix for the commit message. The filename
    #                                      will be appended to this for each commit.
    #         branch (str, optional): Branch to commit to. Defaults to repo's default branch.

    #     Returns:
    #         List[Dict[str, Any]]: A list of results, one for each file operation.
    #                               Each result dict includes "fileName", "status" ('success' or 'failed'),
    #                               "action" ('create' or 'update'), and "response" or "error".
    #     """
    #     if not all([repo_owner, repo_name, files_to_commit, commit_message_prefix]):
    #         self.logger.error("Missing required arguments for commit_multiple_files_async.")
    #         # Or raise ValueError
    #         return [{"fileName": "N/A", "status": "failed", "error": "Missing required arguments"}]

    #     tasks = []
    #     for file_info in files_to_commit:
    #         file_name = file_info.get("fileName")
    #         content = file_info.get("content")

    #         if not file_name or content is None: # content can be empty string
    #             self.logger.error(f"Skipping invalid file_info: {file_info}. 'fileName' and 'content' are required.")
    #             # You might want to collect these errors too
    #             continue

    #         # Construct a more specific commit message for each file
    #         specific_commit_message = f"{commit_message_prefix}: {file_name.split('/')[-1]}"

    #         tasks.append(
    #             self._commit_single_file_async(
    #                 repo_owner=repo_owner,
    #                 repo_name=repo_name,
    #                 file_name=file_name,
    #                 file_content_str=content,
    #                 commit_message=specific_commit_message,
    #                 branch=branch
    #             )
    #         )

    #     if not tasks:
    #         return [{"fileName": "N/A", "status": "failed", "error": "No valid files to commit"}]

    #     self.logger.info(f"Starting to commit {len(tasks)} files concurrently.")
    #     results = await asyncio.gather(*tasks, return_exceptions=False) # Errors are handled within _commit_single_file_async
    #     self.logger.info(f"Finished committing {len(tasks)} files.")
    #     return results

    async def commit_multiple_files_async(
        self,
        repo_owner: str,
        repo_name: str,
        files_to_commit: List[
            Dict[str, str]
        ],  # [{"fileName": "...", "content": "..."}]
        commit_message_prefix: str,  # A prefix for all commit messages
        branch: str = None,
    ) -> List[Dict[str, Any]]:
        """
        Commits a list of files to a GitHub repository.
        Each file is committed individually in sequence to avoid branch conflicts.

        Args:
            repo_owner (str): Owner of the repository.
            repo_name (str): Name of the repository.
            files_to_commit (List[Dict[str, str]]): A list of dictionaries, where each
                dictionary must have "fileName" and "content" keys.
            commit_message_prefix (str): A prefix for the commit message. The filename
                                        will be appended to this for each commit.
            branch (str, optional): Branch to commit to. Defaults to repo's default branch.

        Returns:
            List[Dict[str, Any]]: A list of results, one for each file operation.
                                Each result dict includes "fileName", "status" ('success' or 'failed'),
                                "action" ('create' or 'update'), and "response" or "error".
        """
        if not all([repo_owner, repo_name, files_to_commit, commit_message_prefix]):
            self.logger.error(
                "Missing required arguments for commit_multiple_files_async."
            )
            return [
                {
                    "fileName": "N/A",
                    "status": "failed",
                    "error": "Missing required arguments",
                    "action": "unknown",
                }
            ]

        results = []
        valid_files_count = 0

        for file_info in files_to_commit:
            file_name = file_info.get("fileName")
            content = file_info.get("content")  # content can be an empty string

            if not file_name or content is None:
                self.logger.error(
                    f"Skipping invalid file_info: {file_info}. 'fileName' and 'content' are required."
                )
                results.append(
                    {
                        "fileName": file_name or "N/A",
                        "status": "failed",
                        "action": "unknown",
                        "error": "Invalid file_info: 'fileName' and 'content' are required.",
                    }
                )
                continue

            valid_files_count += 1
            specific_commit_message = (
                f"{commit_message_prefix}: {file_name.split('/')[-1]}"
            )

            self.logger.info(f"Preparing to commit file: {file_name}")
            # Await each commit sequentially
            # This ensures that the next commit operation is based on the repository state
            # *after* the previous one has completed, thus avoiding the 409 conflict.
            try:
                result = await self._commit_single_file_async(
                    repo_owner=repo_owner,
                    repo_name=repo_name,
                    file_name=file_name,
                    file_content_str=content,
                    commit_message=specific_commit_message,
                    branch=branch,
                )
                results.append(result)
                if result.get("status") == "success":
                    self.logger.info(
                        f"Successfully committed {file_name}. Commit SHA: {result.get('commit_sha')}"
                    )
                else:
                    self.logger.error(
                        f"Failed to commit {file_name}. Error: {result.get('error')}"
                    )
            except Exception as e:
                self.logger.exception(
                    f"An unexpected error occurred while committing {file_name}: {e}"
                )
                results.append(
                    {
                        "fileName": file_name,
                        "status": "failed",
                        "action": "unknown",  # Or determine from context if possible
                        "error": f"Unexpected error: {str(e)}",
                    }
                )

        if (
            valid_files_count == 0 and files_to_commit
        ):  # Some file_info objects were provided but all were invalid
            return [
                {
                    "fileName": "N/A",
                    "status": "failed",
                    "error": "No valid files to commit from the provided list",
                    "action": "unknown",
                }
            ]
        elif not files_to_commit:  # The initial list was empty
            return [
                {
                    "fileName": "N/A",
                    "status": "failed",
                    "error": "No files to commit",
                    "action": "unknown",
                }
            ]

        self.logger.info(
            f"Finished processing {len(files_to_commit)} files sequentially."
        )
        return results


# --- Main PaaS Orchestration (Example Usage) ---
# if __name__ == "__main__":
#     gh = GithubRepoConnector()
#     await gh.commit_multiple_files_async()
