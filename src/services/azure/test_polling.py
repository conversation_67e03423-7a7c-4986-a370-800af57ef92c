# src/services/azure/test_polling.py
import asyncio
from src.services.azure.ado_helper import AdoHelper
from src.settings.settings import AzureADOConfig


async def test_build_polling():
    """
    A test script to create a repository from a template, trigger a build,
    and then poll for its completion status.
    """
    print("--- Starting ADO Build Polling Test ---")

    # --- Configuration ---
    # This should be a unique name for the repository to be created for the test.
    # Using a timestamp can help ensure it's unique for each run.
    from datetime import datetime

    timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
    test_repo_name = f"polling-test-{timestamp}"

    # This should be the name of your existing template repository in Azure DevOps.
    template_repo_name = "React_Tailwindcss_Template"  # Or another valid template

    # --- Check for required configuration ---
    config = AzureADOConfig()
    if not all([config.ADO_PAT, config.ADO_ORG, config.ADO_PROJECT]):
        print(
            "Error: Azure DevOps environment variables (ADO_PAT, ADO_ORG, ADO_PROJECT) are not set."
        )
        print("Please set them before running the test.")
        return

    print(f"Test Repository Name: {test_repo_name}")
    print(f"Using Template: {template_repo_name}")
    print(f"Target ADO Project: {config.ADO_PROJECT}")
    print("-" * 20)

    # 1. Initialize the AdoHelper
    # The YAML filename should match the one in your template repository.
    ado_helper = AdoHelper(
        repo_name=test_repo_name, yaml_filename="azure-pipelines.yaml"
    )

    try:
        # 2. Create the repository from the template and trigger the initial build.
        print(
            f"\nStep 1: Creating repository '{test_repo_name}' from template and triggering build..."
        )
        commit_hash = await ado_helper.create_repository_and_deploy(
            repository_name=test_repo_name,
            from_seed=True,
            seed_repository_name=template_repo_name,
            swa_app_name="",
        )

        if not commit_hash or commit_hash == "-1":
            print(
                "\n[FAIL] Failed to create repository or get a valid commit hash. Aborting test."
            )
            return

        print(
            f"\n[SUCCESS] Repository created and build triggered for commit: {commit_hash}"
        )

        # 3. Poll for the build status using the commit hash.
        print(f"\nStep 2: Polling for build status of commit {commit_hash[:7]}...")
        build_result = await ado_helper.poll_build_status(
            commit_sha=commit_hash,
            repository_name=test_repo_name,
            timeout_minutes=20,  # Adjust timeout as needed for your build
            poll_interval_seconds=10,
        )

        print("\n--- Polling Complete ---")
        print(f"Final Status: {build_result.get('status')}")
        print(f"Final Result: {build_result.get('result')}")
        print(f"Build ID: {build_result.get('build_id')}")

        if build_result.get("result") == "succeeded" and build_result.get("live_url"):
            print(f"\n[SUCCESS] Deployment URL: {build_result.get('live_url')}")

        if build_result.get("result") == "failed":
            failed_step = build_result.get("failed_step")
            error_logs = build_result.get("logs")
            print(f"\n[BUILD FAILED] at step: '{failed_step}'")
            if error_logs:
                print("\nError Log Snippet (last 50 lines):")
                print("--------------------------")
                print("\n".join(error_logs.splitlines()[-50:]))
                print("--------------------------")

        # Print some of the full build details if they exist
        build_details = build_result.get("details")
        if build_details:
            print("\n--- Full Build Details ---")
            print(f"  Build Number: {build_details.get('build_number')}")
            print(
                f"  Requested For: {build_details.get('requested_for', {}).get('displayName')}"
            )
            print(f"  Finish Time: {build_details.get('finish_time')}")
            build_url = build_details.get("_links", {}).get("web", {}).get("href")
            if build_url:
                print(f"  Direct Build URL: {build_url}")
            print("--------------------------")

    except Exception as e:
        print(f"\nAn unexpected error occurred during the test: {e}")
        import traceback

        traceback.print_exc()
    finally:
        print("\n--- Test Finished ---")
        # Optional: Add cleanup logic here to delete the created repository if desired.
        print(
            f"To cleanup, manually delete the repository '{test_repo_name}' in Azure DevOps."
        )


if __name__ == "__main__":
    # To run this script:
    # 1. Make sure your .env file has the ADO_PAT, ADO_ORG, and ADO_PROJECT variables set.
    # 2. Run from your project's root directory: python -m src.services.azure.test_polling
    asyncio.run(test_build_polling())
