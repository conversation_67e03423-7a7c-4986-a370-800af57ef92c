import os

from azure.storage.blob import BlobServiceClient
from fastapi import HTTPException


class AzureBlobService:
    def __init__(self):
        self.sas_url = os.getenv("AZURE_STORAGE_SAS_URL")
        self.sas_token = os.getenv("AZURE_STORAGE_SAS_TOKEN")
        self.blob_service_client = BlobServiceClient(
            account_url=self.sas_url, credential=self.sas_token
        )
        self.container_client = self.blob_service_client.get_container_client(
            self.sas_url.split("/")[-1]
        )

    def upload_image(self, file, filename):
        try:
            blob_client = self.container_client.get_blob_client(filename)
            blob_client.upload_blob(file)
            return {"filename": filename, "message": "Image uploaded successfully"}
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))

    def download_image(self, filename):
        try:
            blob_client = self.container_client.get_blob_client(filename)
            stream = blob_client.download_blob()
            return stream.readall()
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
