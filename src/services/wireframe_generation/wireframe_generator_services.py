import datetime
import j<PERSON>
from typing import Dict

from fastapi import <PERSON>TT<PERSON><PERSON>x<PERSON>
from src.core.config import <PERSON><PERSON><PERSON>
from src.model.request_schemas import DARequest
from src.services.generation.avaplus_generation import AVAPlusGeneration
from src.settings.settings import DACreds
from src.utils.debug_file_logger import Debug<PERSON><PERSON><PERSON>ogger
from src.utils.logger import AppLogger


class WireframeService:
    """
    Service class for handling wireframe generation requests.
    """

    def __init__(self, provider: AVAPlusGeneration, credentials: DACreds):
        self.credentials = credentials
        self.provider = provider
        self.logger = AppLogger("Wireframe Generation Service").get_logger()

    async def generate_wireframe(self, request: Dict) -> Dict:
        """
        Generate wireframe designs using AVA Plus service.

        Args:
            request: Dictionary containing the wireframe request data including
                prompt (string with wireframe requirements), device type, and optional image

        Returns:
            Dictionary containing the wireframe generation response

        Raises:
            HTTPException: When the API request fails or returns invalid data
        """
        self.logger.info("---- Processing wireframe generation request via DA strategy")

        try:
            if request["device"] == "mobile":
                payload = DARequest(
                    mode=AgentCode.WIREFRAME_GENERATION_MOBILE,
                    promptOverride=False,
                    userSignature=self.credentials.DA_USER_SIGNATURE,
                    useCaseIdentifier=f"{AgentCode.WIREFRAME_GENERATION_MOBILE}{self.credentials.DA_USECASE_IDENTIFIER}",
                    image=request.get("image"),
                    prompt=request["prompt"],
                )
            else:
                payload = DARequest(
                    mode=AgentCode.WIREFRAME_GENERATION_WEB,
                    promptOverride=False,
                    userSignature=self.credentials.DA_USER_SIGNATURE,
                    useCaseIdentifier=f"{AgentCode.WIREFRAME_GENERATION_WEB}{self.credentials.DA_USECASE_IDENTIFIER}",
                    image=request.get("image"),
                    prompt=request["prompt"],
                )

            self.logger.info(
                "---- Sending wireframe generation request to AVA Plus API"
            )

            response = await self.provider.generate(
                payload=payload.model_dump(), extract_markdown=None
            )

            self.logger.info(
                "---- Successfully received wireframe response from AVA Plus API"
            )

            DebugFileLogger.log_to_file(
                f"wireframe_response_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                response,
            )

            return response

        except HTTPException as e:
            self.logger.error(
                f"---- Wireframe generation failed with HTTP error: {e.status_code} - {e.detail}",
                exc_info=True,
            )
            raise HTTPException(
                status_code=e.status_code,
                detail=f"Wireframe generation failed: {e.detail}",
            )
        except Exception as e:
            self.logger.error(
                f"---- Wireframe generation failed with unexpected error: {str(e)}",
                exc_info=True,
            )
            raise HTTPException(
                status_code=500,
                detail=f"An unexpected error occurred during wireframe generation: {str(e)}",
            )

    async def regenerate_wireframe(self, request: Dict) -> list[Dict[str, str]]:
        """
        Regenerate wireframe designs using existing code files and user request.

        Args:
            request: Dictionary containing the wireframe regeneration request data including
                code (list of dictionaries with fileName and content)
                user_request (string with regeneration requirements)

        Returns:
            String containing the wireframe regeneration response

        Raises:
            HTTPException: When the API request fails or returns invalid data
        """
        self.logger.info(
            "---- Processing wireframe regeneration request via DA strategy"
        )

        try:
            # Prepare the prompt with code files and user request
            prompt_data = {
                "code": request["code"],
                "user_request": request["user_request"],
            }

            payload = DARequest(
                mode=AgentCode.WIREFRAME_REGENERATION,
                promptOverride=False,
                userSignature=self.credentials.DA_USER_SIGNATURE,
                useCaseIdentifier=f"{AgentCode.WIREFRAME_REGENERATION}{self.credentials.DA_USECASE_IDENTIFIER}",
                image=None,
                prompt=json.dumps(prompt_data),
            )

            self.logger.info(
                "---- Sending wireframe regeneration request to AVA Plus API"
            )

            response = await self.provider.generate(
                payload=payload.model_dump(), extract_markdown="json"
            )

            self.logger.info(
                "---- Successfully received wireframe regeneration response from AVA Plus API"
            )

            DebugFileLogger.log_to_file(
                f"wireframe_regeneration_response_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                response,
            )

            return response

        except HTTPException as e:
            self.logger.error(
                f"---- Wireframe regeneration failed with HTTP error: {e.status_code} - {e.detail}",
                exc_info=True,
            )
            raise HTTPException(
                status_code=e.status_code,
                detail=f"Wireframe regeneration failed: {e.detail}",
            )
        except Exception as e:
            self.logger.error(
                f"---- Wireframe regeneration failed with unexpected error: {str(e)}",
                exc_info=True,
            )
            raise HTTPException(
                status_code=500,
                detail=f"An unexpected error occurred during wireframe regeneration: {str(e)}",
            )

    async def generate_project_brief(self, request: dict) -> dict:
        payload = DARequest(
            mode=AgentCode.WIREFRAME_PROJECT_BRIEF,
            promptOverride=False,
            userSignature=request["userSignature"],
            image=request["image"],
            prompt=request["prompt"],
            useCaseIdentifier=f"{AgentCode.APP_GEN_BRIEF}{self.credentials.DA_USECASE_IDENTIFIER}",
        )
        data = await self.provider.generate(
            payload=payload.model_dump(), extract_markdown="json"
        )
        return await self.provider.handle_json(data)

    async def intro_wireframe(self, request: Dict) -> str:
        """
        Intro wireframe designs using user request.

        Args:
            request: Dictionary containing
                user_request (string with user requirements)

        Returns:
            String containing the wireframe intro response
        """
        self.logger.info("---- Processing wireframe intro request via DA strategy")

        try:
            payload = DARequest(
                mode="EE_MLO_WIREFRAME_INTRO",
                promptOverride=False,
                userSignature=self.credentials.DA_USER_SIGNATURE,
                useCaseIdentifier=f"EE_MLO_WIREFRAME_INTRO{self.credentials.DA_USECASE_IDENTIFIER}",
                image=None,
                prompt=json.dumps(request),
            )

            self.logger.info("---- Sending wireframe intro request to AVA Plus API")

            response = await self.provider.generate(
                payload=payload.model_dump(), extract_markdown=None
            )

            self.logger.info(
                "---- Successfully received wireframe intro response from AVA Plus API"
            )

            DebugFileLogger.log_to_file(
                f"wireframe_intro_response_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                response,
            )

            return response

        except HTTPException as e:
            self.logger.error(
                f"---- Wireframe intro failed with HTTP error: {e.status_code} - {e.detail}",
                exc_info=True,
            )
            raise HTTPException(
                status_code=e.status_code, detail=f"Wireframe intro failed: {e.detail}"
            )
        except Exception as e:
            self.logger.error(
                f"---- Wireframe intro failed with unexpected error: {str(e)}",
                exc_info=True,
            )
            raise HTTPException(
                status_code=500,
                detail=f"An unexpected error occurred during wireframe intro: {str(e)}",
            )
