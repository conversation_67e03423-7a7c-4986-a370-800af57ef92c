import json
from typing import Dict

from fastapi import <PERSON>TT<PERSON>Exception
from src.model.request_schemas import (
    WireframeGeneratorRequest,
    WireframeRegenerateRequest,
)
from src.services.wireframe_generation.wireframe_generator_services import (
    WireframeService,
)
from src.services.design_generation.design_generation_service import (
    DesignGenerationService,
)
from src.services.update_media import process_media_requests
from src.services.generation.generation_factory import GenerationFactory
from src.settings.settings import Settings
from src.utils.logger import AppLogger
from src.utils.code_regeneration.post_processor import extract_mlo_code_blocks
from datetime import datetime

settings = Settings()
provider = GenerationFactory.get_generation("avaplus")
da_strategy = WireframeService(provider, settings.da_creds)
logger = AppLogger("WireframeGenerator").get_logger()


async def get_wireframe_designs(request: WireframeGeneratorRequest):
    """
    Generate wireframe designs using DA service strategy.

    Args:
        request: WireframeGeneratorRequest containing the prompt    Returns:
        dict: Response from DA service containing wireframe designs
    """
    try:
        start_time = datetime.now()
        logger.info("START | Wireframe | Generation")
        logger.info(f"Wireframe | Prompt: {request.prompt[:100]}...")

        logger.info("Wireframe | Calling DA strategy")

        # Convert Pydantic model to dictionary for service compatibility
        request_dict = request.model_dump()
        response = await da_strategy.generate_wireframe(request_dict)

        logger.info("Wireframe | Post-processing response to extract mlo-write blocks")

        extracted_code_blocks = extract_mlo_code_blocks(response)
        images_response: list = DesignGenerationService.parse_image_block(response)
        final_image_response: dict = images_response[0]["description"]
        input_list = [final_image_response]

        async def get_processed_media_links(dynamic_input_list: list) -> list:
            """
            A dedicated calling function that takes a dynamic list of dictionaries,
            invokes the media processor, and returns the results.

            Args:
                dynamic_input_list (list): A list containing dictionaries that define
                                        the media to be processed. The structure must
                                        match the input format of the processor.

            Returns:
                list: The processed list with placeholder URLs replaced by real media links.
            """
            if not isinstance(dynamic_input_list, list) or not dynamic_input_list:
                logger.error("Input must be a non-empty list.")
                return []

            logger.info(
                f"Starting media processing job for {len(dynamic_input_list)} page(s)."
            )

            try:
                # --- This is the core of the calling function ---
                # It calls the imported async function and awaits its result.
                processed_results = await process_media_requests(dynamic_input_list)

                # 3. DO SOMETHING WITH THE RESULT
                if processed_results:
                    processed_results = json.dumps(processed_results, indent=2)
                    # logger.info(f"New Image Links Json has been generated: {processed_results}")
                # You can return the processed results or do something else with them.
                logger.info("Successfully processed the media requests.")
                return processed_results

            except Exception as e:
                logger.critical(
                    f"A critical error occurred in the media processing pipeline: {e}"
                )
                # Depending on your needs, you might want to return an empty list,
                # the original data, or raise the exception further.
                return []

        # Call the function with the valid JSON image data
        valid_links = await get_processed_media_links(input_list)

        # Replace placeholder URLs in the page response code with actual media links
        extracted_code_blocks = DesignGenerationService.replace_image_links(
            valid_links, extracted_code_blocks
        )

        logger.info(
            f"Wireframe | Extracted {len(extracted_code_blocks)} code blocks from response"
        )

        end_time = datetime.now() - start_time
        logger.info(f"Wireframe | Generation complete ✓ in {end_time}")

        return extracted_code_blocks

    except Exception as e:
        logger.error(f"Wireframe | Generation failed: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to generate wireframe: {str(e)}"
        )


async def regenerate_wireframe_designs(request: WireframeRegenerateRequest):
    """
    Regenerate wireframe designs using existing code files and user request.

    Args:
        request: WireframeRegenerateRequest containing the code files and user request

    Returns:
        list: List of dictionaries with fileName and content for regenerated code
    """
    try:
        start_time = datetime.now()
        logger.info("START | Wireframe | Regeneration")
        logger.info(
            f"Wireframe Regenerate | User request: {request.user_request[:100]}..."
        )
        logger.info(f"Wireframe Regenerate | Code files count: {len(request.code)}")

        # Convert code files to the format expected by the service
        code_files = [
            {"fileName": file.fileName, "content": file.content}
            for file in request.code
        ]

        request_data = {"code": code_files, "user_request": request.user_request}

        logger.info("Wireframe Regenerate | Calling DA strategy")

        response: list[Dict[str, str]] = await da_strategy.regenerate_wireframe(
            request_data
        )

        logger.info("Wireframe Regenerate | Response Recieved form DA")

        end_time = datetime.now() - start_time
        logger.info(
            f"Wireframe Regenerate | Generation complete ✓ in {end_time} for {len(response)}"
        )

        return response

    except Exception as e:
        logger.error(f"Wireframe Regenerate | Generation failed: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to regenerate wireframe: {str(e)}"
        )


async def get_wireframe_intro(request: WireframeRegenerateRequest) -> str:
    """
    Provide information for wireframe designs based on user request.

    Args:
        request: WireframeRegenerateRequest containing the optional code files and user request

    Returns:
        String: a intro message for user about the project or request
    """
    try:
        start_time = datetime.now()
        logger.info("START | Wireframe | Intro")
        logger.info(f"Wireframe Intro | User request: {request.user_request[:100]}...")

        # Prepare request data - include code files if provided
        request_data = {"user_request": request.user_request}

        # Add code files to request if they exist
        if request.code:
            code_files = [
                {"fileName": file.fileName, "content": file.content}
                for file in request.code
            ]
            request_data["code"] = code_files
            logger.info(f"Wireframe Intro | Including {len(code_files)} code files")

        logger.info("Wireframe Intro | Calling DA strategy")

        response: str = await da_strategy.intro_wireframe(request_data)

        logger.info("Wireframe Intro | Response Recieved form DA")

        end_time = datetime.now() - start_time
        logger.info(f"Wireframe Intro | Generation complete ✓ in {end_time}")

        return response

    except Exception as e:
        logger.error(f"Wireframe Intro | Generation failed: {e}", exc_info=True)
        raise HTTPException(
            status_code=500, detail=f"Failed to intro wireframe: {str(e)}"
        )


async def get_project_brief(request: dict) -> dict:
    return await da_strategy.generate_project_brief(request)
