import asyncio

from src.services.azure.ado_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from src.settings.settings import AzureADOConfig


async def main():
    """
    Example deployment script using Azure DevOps.
    Creates a repository from template and triggers the pipeline for deployment.
    The Azure pipeline will automatically create the Static Web App with a random URL.
    """
    azure_config = AzureADOConfig()

    # Configuration
    AZURE_REPO = "ee-ado-wfk"
    DEFAULT_BRANCH = "main"

    # Create Azure DevOps repository from template
    ado_helper = AdoHelper(AZURE_REPO, "azure-pipelines.yaml")
    commit_sha = await ado_helper.create_repository_and_deploy(
        repository_name=AZURE_REPO, 
        from_seed=True, 
        seed_repository_name="React_Tailwindcss_Template"
    )

    if commit_sha and commit_sha != "-1":
        print(f"\n--- Azure DevOps Repository Created Successfully ---")
        print(f"Repository: {AZURE_REPO}")
        print(f"Commit SHA: {commit_sha}")
        print(f"Pipeline will automatically deploy to Azure Static Web Apps")
        print(f"URL will be available after pipeline completes (check Azure portal)")
    else:
        print("\nFailed to create Azure DevOps repository.")


if __name__ == "__main__":
    asyncio.run(main())
