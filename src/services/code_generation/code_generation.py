from typing import Callable, Dict, List, Optional

from src.services.code_generation.strategies.base_strategy import BaseStrategy
from src.settings.settings import Settings


class CodeGeneration:
    def __init__(self, strategy: BaseStrategy) -> None:
        self.credentials = Settings()
        self._strategy = strategy

    async def generate_project_brief(self, request: dict) -> dict:
        return await self._strategy.generate_project_brief(request)

    async def generate_prd(self, request: dict) -> dict:
        return await self._strategy.generate_prd(request)

    async def identify_files(self, request: dict, detailed_prd: dict) -> dict:
        return await self._strategy.identify_files(request, detailed_prd)

    async def generate_design_system(
        self, detailed_prd: dict, request: dict, design_system_files: any
    ) -> str:
        return await self._strategy.generate_design_system(
            detailed_prd, request, design_system_files
        )

    async def call_llm_for_code_batch(
        self, request: dict, prompt: str, image_uri: list[str], target_files: List[str]
    ) -> Dict[str, str]:
        return await self._strategy.call_llm_for_code_batch(
            request=request,
            prompt=prompt,
            image_uri=image_uri,
            target_files=target_files,
        )

    async def IMAGE_VALIDATOR(self, images_path: List[str]) -> List[str]:
        return await self._strategy.IMAGE_VALIDATOR(images_path=images_path)

    async def GENERATE_HTML_FOR_EACH_IMAGE(self, images_path: List[str]) -> List[str]:
        return await self._strategy.GENERATE_HTML_FOR_EACH_IMAGE(
            images_path=images_path
        )

    async def generate_code(
        self,
        detailed_prd: dict,
        design_system_code: dict,
        identified_files: dict,
        request: dict,
        callback: Optional[Callable[[Dict[str, str]], None]],
    ) -> dict:
        return await self._strategy.generate_code(
            detailed_prd, design_system_code, identified_files, request, callback
        )

    async def generate(
        self, mode: str, prompt: str, extraction_markdown: str, image_uri: list[str]
    ) -> dict[str, str]:
        pass

    async def process_conversation(self, request: dict) -> dict:
        return await self._strategy.process_conversation(request)

    async def regenerate_code(self, request: dict) -> dict:
        return await self._strategy.regenerate_code(request)
