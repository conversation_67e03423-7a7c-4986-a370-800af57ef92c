import datetime
import json
from typing import Any, Dict, List, Union

from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>


from src.core.config import <PERSON><PERSON><PERSON>
from src.model.request_schemas import (
    AppProgressEnum,
    DAConversationRequest,
    DARequest,
    ProjectStatusEnum,
)
from src.services.React_prompt_helper import <PERSON><PERSON><PERSON>romptHel<PERSON>
from src.services.code_generation.strategies.base_strategy import BaseStrategy
from src.services.generation.avaplus_generation import AVAPlusGeneration
from src.settings.settings import DACreds
from src.utils.debug_file_logger import DebugFileLogger
from src.utils.decorators.avaplus_decorator import handle_exceptions_and_log
from src.utils.logger import AppLogger

logger = AppLogger(__name__).get_logger()


class DAServices(BaseStrategy):
    """
    Service class for handling Digital Assistant (DA) API requests.
    """

    def __init__(self, provider: AVAPlusGeneration, credentials: DACreds):
        super().__init__(provider)
        self.credentials = credentials
        self.provider = provider

    @handle_exceptions_and_log(logger)
    async def generate_project_brief(self, request: dict) -> dict:
        payload = DARequest(
            mode=AgentCode.APP_GEN_BRIEF,
            promptOverride=False,
            userSignature=request["userSignature"],
            image=request["image"],
            prompt=request["project_description"],
            useCaseIdentifier=f"{AgentCode.APP_GEN_BRIEF}{self.credentials.DA_USECASE_IDENTIFIER}",
        )
        data = await self.provider.generate(
            payload=payload.model_dump(), extract_markdown="json"
        )
        return await self.provider.handle_json(data)

    @handle_exceptions_and_log(logger)
    async def generate_prd(self, request: dict) -> dict:
        """Generate an PRD from the DA service for a given image and user info.

        Args:
            image_uri: URI string of the image to analyze

        Returns:
            Dictionary containing the identified files data

        Raises:
            HTTPException: When the API request fails or returns invalid data
        """
        logger.info(" ---- Generate PRD ----")

        # try:
        # Validate required fields in the request
        required_fields = ["user_input", "userSignature", "image"]
        for field in required_fields:
            if field not in request or not request[field]:
                logger.error(f" ---- Missing required field: {field}")
                raise ValueError(f"Missing required field: {field}")

        payload_prompt = {
            "user_input": request["user_input"],
        }

        # "platform": request["platform"],
        # "framework": request["framework"],
        # "design_library": request["design_library"],

        payload = DARequest(
            mode=AgentCode.APP_GEN_PRD,
            promptOverride=False,
            userSignature=request["userSignature"],
            image=request["image"],
            prompt=json.dumps(payload_prompt),
            useCaseIdentifier=f"{AgentCode.APP_GEN_PRD}{self.credentials.DA_USECASE_IDENTIFIER}",
        )

        logger.info(" ---- Sending request to AVA Plus API ----")
        json_text = await self.provider.generate(
            payload=payload.model_dump(), extract_markdown=None
        )
        logger.info(" ---- Successfully received response from AVA Plus API")
        return await self.provider.handle_json(json_text)

    @handle_exceptions_and_log(logger)
    async def identify_files(self, request: dict, detailed_prd: dict) -> dict:
        """Request file identification from the DA service for a given image.

        Args:
            image_uri: URI string of the image to analyze

        Returns:
            Dictionary containing the identified files data

        Raises:
            HTTPException: When the API request fails or returns invalid data
        """
        logger.info(" ---- Identifying files, designTokens and analyzing user prompt")

        filtered_prd = {
            "projectInfo": detailed_prd["projectInfo"],
            "techStack": detailed_prd["techStack"],
            "componentHierarchy": detailed_prd["componentBreakdown"]["organisms"],
            "rootComponent": "src/pages/index.tsx",
            "styleComponents": ["tailwind.config.js, src/index.css"],
        }

        payload_prompt = {
            "project_requirements_document": filtered_prd,
        }

        # Prepare request payload
        payload = DARequest(
            mode=AgentCode.APP_FILE_ID,
            promptOverride=False,
            userSignature=request["userSignature"],
            image=request["image"],
            prompt=json.dumps(payload_prompt),
            useCaseIdentifier=f"{AgentCode.APP_FILE_ID}{self.credentials.DA_USECASE_IDENTIFIER}",
        )

        json_text = await self.provider.generate(
            payload=payload.model_dump(), extract_markdown="json"
        )
        return await self.provider.handle_json(json_text)

    async def generate_design_system(
        self, detailed_prd: dict, request: dict, design_system_files: any
    ) -> str:
        """Generate The design system based on the users prompt and the identified files.
        Args:
            request: dict containing the request data
        Returns:
            str: Generated code as a json string
        """
        logger.info(
            " ---- Generating code for the design system for {},{}".format(
                "React", "Tailwindcss"
            )
        )

        payload_prompt = {
            "project_requirements_document": detailed_prd,
            "filesToUpdate": design_system_files,
        }

        payload = DARequest(
            mode=AgentCode.APP_DESIGN_SYSTEM,
            promptOverride=False,
            userSignature=self.credentials.DA_USER_SIGNATURE,
            image=request["image"],
            prompt=json.dumps(payload_prompt),
            useCaseIdentifier=f"{AgentCode.APP_DESIGN_SYSTEM}{self.credentials.DA_USECASE_IDENTIFIER}",
        )

        json_text = await self.provider.generate(
            payload=payload.model_dump(), extract_markdown="json"
        )

        # print("typ[e::: ]",type(json_text))
        return await self.provider.handle_json(json_text)

    async def call_llm_for_code_batch(
        self, prompt: str, image_uri: list[str], target_files: List[str]
    ) -> Dict[str, str]:
        """Makes the API call for a specific batch of files."""
        logger.info(f" ---- Calling LLM for batch: {target_files}")
        try:
            payload = DARequest(
                mode="EE_MLO_RCG_RCGAF",  # Use a relevant mode identifier
                promptOverride=False,
                userSignature=self.credentials.DA_USER_SIGNATURE,
                image=image_uri,  # Pass the image URI/data
                prompt=prompt,
                useCaseIdentifier=f"EE_MLO_RCG_RCGAF{self.credentials.DA_USECASE_IDENTIFIER}",
            )

            DebugFileLogger.log_to_file(
                f"prompt_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                prompt,
            )

            json_text = await self.provider.generate(
                payload=payload.model_dump(), extract_markdown="json"
            )

            DebugFileLogger.log_to_file(
                f"response_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                json_text,
            )
            generated_code_batch = await self.provider.handle_json(json_text)

            # Ensure the response is in the expected format (dictionary with file paths as keys)
            # This handles both formats: {"src/App.tsx": "<code>"} and [{"fileName": "src/App.tsx", "content": "<code>"}]
            if isinstance(generated_code_batch, list):
                # Convert from list of dicts to dict format
                logger.info(
                    " ---- Converting response from list format to dictionary format"
                )
                generated_code_batch = {
                    item["fileName"]: item["content"] for item in generated_code_batch
                }

            # Basic validation: Check if keys match target files
            if set(generated_code_batch.keys()) != set(target_files):
                logger.warning(
                    f" ---- Mismatch between target files and generated keys for batch {target_files}."
                )
                # Attempt to return what was received, but log warning
                return {
                    file_path: generated_code_batch.get(
                        file_path, "// ERROR: Content missing in LLM response."
                    )
                    for file_path in target_files
                }

            logger.info(f" ---- Successfully generated code for batch: {target_files}")
            return generated_code_batch

        except Exception as e:
            logger.error(
                f" ---- LLM call failed for batch {target_files}: {e}", exc_info=True
            )
            return {
                file_path: f"// ERROR: Exception during LLM call: {e}"
                for file_path in target_files
            }

    async def generate_code(
        self,
        detailed_prd: Dict[str, Any],
        design_system_code: Dict[str, str],
        identified_files: Dict[str, Any],
        request: Any,
        callback,
    ) -> List[Dict[str, str]]:
        """
        Generate code for the files identified, using a batched approach.

        Args:
            initial_test1_response: Response from the initial file identification (contains filesToGenerate, detailedAnalysisSummary).
            design_system_code: Code for the design system foundation files (e.g., styles.css, tailwind.config.js).
            request: Request object containing image URI, framework, design_library etc.

        Returns:
            List[Dict[str, str]]: List of {"fileName": path, "content": code} dictionaries.
        """
        logger.info(" ---- Starting batched code generation process")

        files_to_generate = identified_files["filesToGenerate"]
        image_uri = request["image"]  # Assuming image URI/data is in request.image

        if not all(
            [
                design_system_code,
                detailed_prd,
                files_to_generate,
                image_uri,
            ]
        ):
            raise ValueError(
                "Missing necessary data for code generation (files, summary, framework, library, image)."
            )

        # Combine generated design system code with standard config file list if needed
        # This ensures design system files are not regenerated but are available as context
        all_context_files = design_system_code.copy()

        categorized_files = ReactPromptHelper._categorize_files(files_to_generate)
        all_generated_files_dict = {}  # Use dict first for easier updates/overwrites
        all_generated_files_list = []  # Final list output

        # --- Batch 3: Components  ---
        failed_components = []
        component_results_list = []

        # --- Batch 3.1: Feature Components First (Process all at once) ---
        feature_files = categorized_files.get("components", {}).get("features", [])
        if feature_files:
            logger.info(
                f"Batch 3.1 | Feature Components | {len(feature_files)} files - Processing all at once in a single batch"
            )
            feature_results = {}

            try:
                # Create a prompt for all feature files at once
                prompt_feature = ReactPromptHelper._create_prompt_for_batch(
                    batch_type="component",
                    files_in_batch=feature_files,  # Pass all feature files at once
                    context_files=all_context_files,
                    detailed_prd=detailed_prd,
                    component_name="features",
                )

                await callback(
                    {
                        "app_progress": AppProgressEnum.COMPONENTS_CREATED,
                        "progress_description": f" Identified {len(feature_files)} files to generate. ✓\n <mlo_files>{', '.join(feature_files)}</mlo_files>",
                        "log": f"Agent : Code Agent | Features | {len(feature_files)} files generated \n Action: Generating code for components  ",
                        "project_status": ProjectStatusEnum.IN_PROGRESS,
                        "metadata": [],
                    }
                )

                # Call LLM for all feature files
                feature_result = await self.call_llm_for_code_batch(
                    prompt_feature, image_uri, feature_files
                )

                # Ensure result is in the expected dictionary format
                if not isinstance(feature_result, dict):
                    logger.warning(
                        "COMPONENT | Features | Unexpected result format, converting"
                    )
                    if isinstance(feature_result, list):
                        feature_result = {
                            item["fileName"]: item["content"] for item in feature_result
                        }

                # Update the feature results and context files immediately
                feature_results.update(feature_result)
                all_context_files.update(feature_result)
                all_generated_files_dict.update(feature_result)
                logger.info(
                    f"COMPONENT | Features | {len(feature_files)} files generated successfully ✓"
                )

                await callback(
                    {
                        "app_progress": AppProgressEnum.COMPONENTS_CREATED,
                        "progress_description": f" {len(feature_files)} files generated ✓\n <mlo_files>{', '.join(feature_files)}</mlo_files>",
                        "log": f"Code Agent | Features | {len(feature_files)} files generated",
                        "project_status": ProjectStatusEnum.COMPLETED,
                        "metadata": [{"type": "files", "data": feature_result}],
                    }
                )

            except Exception as e:
                logger.error(f"COMPONENT | Features | Generation failed X: {str(e)}")
                failed_components.append(("Feature", feature_files))

                # Create placeholder content for each file in the failed batch
                placeholder_content = {}
                for feature_file in feature_files:
                    placeholder_content[feature_file] = (
                        f"// ERROR: Failed to generate code for {feature_file}\n// Error: {str(e)}\n// TODO: Implement feature component"
                    )

                feature_results.update(placeholder_content)
                all_generated_files_dict.update(placeholder_content)

            # Add the combined results to the component_results_list
            if feature_results:
                component_results_list.append(feature_results)
                logger.info(
                    f"COMPONENT | Features | {len(feature_results)} files processed ✓"
                )

        # --- Batch 3.2: Layout Components Second ---
        layout_files = categorized_files.get("components", {}).get("layout", [])
        if layout_files:
            await callback(
                {
                    "app_progress": AppProgressEnum.LAYOUT_ANALYZED,
                    "progress_description": f" {len(layout_files)} files identified.\n <mlo_files>{', '.join(layout_files)}</mlo_files>\n Lets generate the code.",
                    "log": f"Code Agent | Features | {len(layout_files)} files generated",
                    "project_status": ProjectStatusEnum.IN_PROGRESS,
                    "metadata": [],
                }
            )
            logger.info(f"Batch 3.2 | Layout Components | {len(layout_files)} files")
            try:
                # Process all layout files in a single batch
                prompt_layout = ReactPromptHelper._create_prompt_for_batch(
                    batch_type="component",
                    files_in_batch=layout_files,
                    context_files=all_context_files,
                    detailed_prd=detailed_prd,
                    component_name="layout",
                )
                # DebugFileLogger.log_to_file("prompt_layout.txt", prompt_layout)

                result = await self.call_llm_for_code_batch(
                    prompt_layout, image_uri, layout_files
                )
                # Ensure result is in the expected dictionary format
                if not isinstance(result, dict):
                    logger.warning(
                        "COMPONENT | Layout | Unexpected result format, converting"
                    )
                    if isinstance(result, list):
                        result = {item["fileName"]: item["content"] for item in result}

                component_results_list.append(result)
                logger.info(
                    f"COMPONENT | Layout | {len(layout_files)} files generated ✓"
                )
                # DebugFileLogger.log_to_file("response_layout.json", result)
                # Update context files immediately after layout
                all_context_files.update(result)
                all_generated_files_dict.update(result)
                await callback(
                    {
                        "app_progress": AppProgressEnum.LAYOUT_ANALYZED,
                        "progress_description": f" {len(layout_files)} files generated ✓\n <mlo_files>{', '.join(layout_files)}</mlo_files>",
                        "log": f"Agent : Code Agent | {request['framework']} | {request['design_library']}\n Action: Done identifying the layout",
                        "project_status": ProjectStatusEnum.COMPLETED,
                        "metadata": [
                            {
                                "type": "artifact",
                                "data": {
                                    "type": "text",
                                    "data": f"{detailed_prd['layoutStructure']['layoutType']}",
                                },
                            }
                        ],
                    }
                )

            except Exception as e:
                logger.error("COMPONENT | Layout | Generation failed X")
                failed_components.append(("Layout", layout_files))
                placeholder_content = {}
                for file_path in layout_files:
                    placeholder_content[file_path] = (
                        f"// ERROR: Failed to generate code for {file_path}\n// Error: {str(e)}\n// TODO: Implement layout component"
                    )
                component_results_list.append(placeholder_content)
                all_generated_files_dict.update(placeholder_content)

        # --- Batch 3.4: Main Content Component (Last) ---
        # main_content_files = categorized_files.get("main_content", [])
        # if main_content_files:
        #     logger.info(f"Batch 3.4 | Main Content Component | {len(main_content_files)} files")
        #     prompt_main = ReactPromptHelper._create_prompt_for_batch(
        #         batch_type="component",
        #         files_in_batch=main_content_files,
        #         context_files=all_context_files,
        #         detailed_prd=detailed_prd,
        #         component_name="main_content"
        #     )
        #     # DebugFileLogger.log_to_file("prompt_MainContent.txt", prompt_main)
        #     try:
        #         main_results = await self.call_llm_for_code_batch(prompt_main, image_uri, main_content_files)
        #         # Ensure result is in the expected dictionary format
        #         if not isinstance(main_results, dict):
        #             logger.warning(f"COMPONENT | MainContent | Unexpected result format, converting")
        #             if isinstance(main_results, list):
        #                 main_results = {item["fileName"]: item["content"] for item in main_results}

        #         component_results_list.append(main_results)
        #         logger.info(f"COMPONENT | MainContent | {len(main_content_files)} files generated ✓")
        #         # DebugFileLogger.log_to_file("response_MainContent.json", main_results)
        #         # Update context files
        #         all_context_files.update(main_results)
        #         all_generated_files_dict.update(main_results)

        #     except Exception as e:
        #         logger.error(f"COMPONENT | MainContent | Generation failed X")
        #         failed_components.append(("MainContent", main_content_files))
        #         placeholder_content = {}
        #         for file_path in main_content_files:
        #             placeholder_content[file_path] = f"// ERROR: Failed to generate code for {file_path}\n// Error: {str(e)}\n// TODO: Implement MainContent component"
        #         component_results_list.append(placeholder_content)
        #         all_generated_files_dict.update(placeholder_content)

        # --- Batch 4: Pages ---
        batch4_files = categorized_files.get("pages", [])
        if batch4_files:
            logger.info("--- Processing Batch 4: Pages ---")
            prompt4 = ReactPromptHelper._create_prompt_for_batch(
                "pages",
                batch4_files,
                all_context_files,
                detailed_prd,
            )

            await callback(
                {
                    "app_progress": AppProgressEnum.PAGES_GENERATED,
                    "progress_description": f"Lets now generated the pages for your app. Identified {len(batch4_files)} to generate. ✓\n <mlo_files>{', '.join(batch4_files)}</mlo_files>",
                    "log": f"Agent : Code Agent | Pages | \n Action : Generate {len(batch4_files)} files \n ",
                    "project_status": ProjectStatusEnum.IN_PROGRESS,
                    "metadata": [],
                }
            )
            # write the prompt into a file
            # with open("prompt4.txt", "w") as f:
            #     f.write(prompt4)
            batch4_results = await self.call_llm_for_code_batch(
                prompt4, image_uri, batch4_files
            )
            # Ensure result is in the expected dictionary format
            if not isinstance(batch4_results, dict):
                logger.warning("BATCH 4 | Pages | Unexpected result format, converting")
                if isinstance(batch4_results, list):
                    batch4_results = {
                        item["fileName"]: item["content"] for item in batch4_results
                    }

            # write the results into a file
            # with open("batch4_results.json", "w") as f:
            #     json.dump(batch4_results, f)
            all_context_files.update(batch4_results)
            all_generated_files_dict.update(batch4_results)
            await callback(
                {
                    "app_progress": AppProgressEnum.PAGES_GENERATED,
                    "progress_description": "Great! I have generated the page components.",
                    "log": f"Code Agent | Pages | {len(batch4_results)} files generated ✓\n ",
                    "project_status": ProjectStatusEnum.COMPLETED,
                    "metadata": [{"type": "files", "data": batch4_results}],
                }
            )

        # Batch 5 : Root Files ---
        batch5_files = categorized_files.get("root", [])
        if batch5_files:
            logger.info("--- Processing Batch 5: Root Files ---")
            prompt5 = ReactPromptHelper._create_prompt_for_batch(
                "root",
                batch5_files,
                all_context_files,
                detailed_prd,
            )
            # write the prompt into a file
            # with open("prompt5.txt", "w") as f:
            #     f.write(prompt5)
            batch5_results = await self.call_llm_for_code_batch(
                prompt5, image_uri, batch5_files
            )
            # Ensure result is in the expected dictionary format
            if not isinstance(batch5_results, dict):
                logger.warning(
                    "BATCH 5 | Root Files | Unexpected result format, converting"
                )
                if isinstance(batch5_results, list):
                    batch5_results = {
                        item["fileName"]: item["content"] for item in batch5_results
                    }

            # write the results into a file
            # with open("batch5_results.json", "w") as f:
            #     json.dump(batch5_results, f)
            all_generated_files_dict.update(batch5_results)

        DebugFileLogger.log_to_file(
            "all_generated_files_dict.json", all_generated_files_dict
        )

        # --- Handle Style and Config Files ---
        style_files = {
            "angular": ["src/styles.css", "src/styles.scss"],
            "react": ["src/styles.css", "src/index.css"],
            "vue": ["src/styles.css", "src/assets/main.css"],
        }

        # Add design system files
        design_system_files = [
            "tailwind.config.js",
            "tailwind.config.ts",
            "material.module.ts",
            "bootstrap.config.js",
            "material-theme.scss",
            "material-custom.scss",
            "bootstrap.scss",
            "bootstrap-custom.scss",
            "_variables.scss",
        ]

        # Process style files specific to the framework
        for style_file in style_files.get("react", []):
            if style_file in files_to_generate and style_file in design_system_code:
                all_generated_files_dict[style_file] = design_system_code[style_file]

        # Process design system config files if present
        for config_file in design_system_files:
            if config_file in files_to_generate and config_file in design_system_code:
                all_generated_files_dict[config_file] = design_system_code[config_file]

        # --- Final Assembly ---
        for file_path in files_to_generate:
            if file_path in design_system_code:
                content = design_system_code[file_path]
            elif file_path in all_generated_files_dict:
                content = all_generated_files_dict[file_path]
            else:
                logger.warning(
                    f"File '{file_path}' was in initial list but not generated or categorized."
                )
                content = f"// ERROR: Content generation failed or file was missed for {file_path}"

            final_file_path = file_path
            all_generated_files_list.append(
                {"fileName": final_file_path, "content": content}
            )

        # Log summary of failures if any
        if failed_components:
            logger.warning("--- Failed Components Summary ---")
            for component_name, _ in failed_components:
                logger.warning(f"Failed to generate: {component_name}")

        logger.info(" ---- Batched code generation process completed.")
        return all_generated_files_list

    async def generate(self, mode, prompt, extraction_markdown, image=None):
        payload = DARequest(
            mode=mode,  # Use a relevant mode identifier
            promptOverride=False,
            userSignature=self.credentials.DA_USER_SIGNATURE,
            image=image,  # Pass the image URI/data
            prompt=prompt,
            useCaseIdentifier=f"{mode}{self.credentials.DA_USECASE_IDENTIFIER}",
        )
        return await self.provider.generate(
            payload=payload.model_dump(), extract_markdown=extraction_markdown
        )

    async def regenerate_code(self, request: dict) -> Union[Dict[str, Any], List, str]:
        """
        Process a code regeneration request.

        Args:
            request: Dictionary containing the request data including
                prompt (stringified JSON with code, user_request, and image),
                userSignature, and optionally image

        Returns:
            Union[Dict[str, Any], List, str]: The processed response from the provider.
                This can be a dictionary, a list, or a raw string depending on the provider's output.

        """
        logger.info("---- Processing code regeneration request via DA strategy")

        try:
            # Create the request payload
            payload = DARequest(
                mode=AgentCode.APP_REGENERATION,
                promptOverride=False,  # Assuming this is the intended behavior
                userSignature=request["userSignature"],
                image=request.get("image"),  # Optional field
                prompt=request["prompt"],
                useCaseIdentifier=f"{AgentCode.APP_REGENERATION}{self.credentials.DA_USECASE_IDENTIFIER}",  # Assuming this format
            )

            # Make the API call
            raw_api_text_response = await self.provider.generate(
                payload=payload.model_dump(), extract_markdown=None
            )

            logger.info("---- Successfully received response from AVA Plus API")
            logger.debug(f"Raw API response type: {type(raw_api_text_response)}")

            # Log the raw response for debugging (using DebugFileLogger)
            try:
                DebugFileLogger.log_to_file(
                    f"code_regeneration_raw_api_response_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
                    raw_api_text_response,
                )
            except Exception as e:
                logger.warning(f"Failed to log raw API response: {str(e)}")

            # For code regeneration, check if the response contains MLO tags first
            # If it does, return the raw response directly to avoid JSON processing issues
            if (
                isinstance(raw_api_text_response, str)
                and "<mlo-write" in raw_api_text_response
            ):
                return raw_api_text_response

        except HTTPException as e:
            raise e
        except Exception as e:
            logger.error(f"✗ Code regeneration failed: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"An unexpected error occurred during code regeneration request: {str(e)}",
            )

    async def process_conversation(self, request: dict) -> dict:
        """Process a conversation request with the provided conversations.

        Args:
            request: Dictionary containing the conversation request data including
                mode, useCaseIdentifier, userSignature, and conversations list

        Returns:
            Dictionary containing the processed conversation response

        Raises:
            HTTPException: When the API request fails or returns invalid data
        """
        logger.info(" ---- Processing conversation request")

        try:
            # Create the conversation request payload
            payload = DAConversationRequest(
                mode=request["mode"],
                useCaseIdentifier=request["useCaseIdentifier"],
                userSignature=request["userSignature"],
                conversations=request["conversations"],
            )

            # Log the payload for debugging
            DebugFileLogger.log_to_file(
                f"conversation_request_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                payload.model_dump(),
            )

            logger.info(" ---- Sending conversation request to AVA Plus API")

            # Make the API call using the provider's API client
            response = await self.provider.api_client.post(
                "v1/api/instructions/ava/force/gpt/model/chat",
                data=payload.model_dump(),
            )

            logger.info(" ---- Successfully received response from AVA Plus API")

            # Log the response for debugging
            DebugFileLogger.log_to_file(
                f"conversation_response_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                response,
            )

            return response

        except HTTPException as e:
            # Capture specific HTTP exceptions with their status codes
            logger.error(
                f" ---- Conversation processing failed with HTTP error: {e.status_code} - {e.detail}",
                exc_info=True,
            )
            raise HTTPException(
                status_code=e.status_code,
                detail=f"Conversation processing failed: {e.detail}",
            )
        except ValueError as e:
            # Handle validation errors
            logger.error(
                f" ---- Conversation processing failed due to validation error: {str(e)}",
                exc_info=True,
            )
            raise HTTPException(
                status_code=400,
                detail=f"Validation error during conversation processing: {str(e)}",
            )
        except Exception as e:
            # Handle all other exceptions
            logger.error(
                f" ---- Conversation processing failed with unexpected error: {str(e)}",
                exc_info=True,
            )
            raise HTTPException(
                status_code=500,
                detail=f"An unexpected error occurred during conversation processing: {str(e)}",
            )
