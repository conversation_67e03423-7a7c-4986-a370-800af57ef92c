# src/services/code_generation/strategies/base_strategy.py
from abc import ABC, abstractmethod
from typing import Callable, Dict, Optional


class BaseStrategy(ABC):
    def __init__(self, provider):
        self.provider = provider

    @abstractmethod
    async def generate_prd(self, request: dict) -> dict:
        pass

    @abstractmethod
    async def generate_project_brief(self, request: dict) -> dict:
        pass

    @abstractmethod
    async def identify_files(self, request: dict, detailed_prd: dict) -> dict:
        pass

    @abstractmethod
    async def generate_design_system(
        self, detailed_prd: dict, request: dict, design_system_files: list
    ) -> dict:
        pass

    @abstractmethod
    async def generate_code(
        self,
        detailed_prd: dict,
        design_system_code: dict,
        identified_files: dict,
        request: dict,
        callback: Optional[Callable[[Dict[str, str]], None]],
    ) -> dict:
        pass

    @abstractmethod
    async def call_llm_for_code_batch(
        self, request: dict, prompt: str, image_uri: list[str]
    ) -> dict[str, str]:
        pass

    @abstractmethod
    async def process_conversation(self, request: dict) -> dict:
        pass

    @abstractmethod
    async def regenerate_code(self, request: dict) -> dict:
        pass
