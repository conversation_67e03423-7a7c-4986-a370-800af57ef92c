import requests
import json
import base64
from pathlib import Path


def add_ssh_public_key(
    organization_url,
    personal_access_token,
    ssh_public_key_content,
    key_description="My SSH Key",
):
    """
    Add an SSH public key to Azure DevOps using REST API

    Args:
        organization_url (str): Your Azure DevOps organization URL (e.g., 'https://dev.azure.com/yourorg')
        personal_access_token (str): Your PAT with appropriate permissions
        ssh_public_key_content (str): The SSH public key content (from .pub file)
        key_description (str): Description for the SSH key

    Returns:
        dict: Information about the created SSH key
    """

    # Prepare the API endpoint
    api_url = f"{organization_url.rstrip('/')}/_apis/git/sshkeys?api-version=7.1"

    # Prepare headers
    headers = {
        "Content-Type": "application/json",
        "Authorization": f'Basic {base64.b64encode(f":{personal_access_token}".encode()).decode()}',
    }

    # Prepare the SSH key data
    ssh_key_data = {
        "displayName": key_description,
        "keyData": ssh_public_key_content.strip(),
    }

    try:
        # Make the API request
        response = requests.post(api_url, headers=headers, json=ssh_key_data)

        if response.status_code == 201:
            result = response.json()
            print(f"SSH key added successfully!")
            print(f"Key ID: {result.get('sshKeyId')}")
            print(f"Display Name: {result.get('displayName')}")
            print(f"Fingerprint: {result.get('fingerprint')}")

            return result
        else:
            print(f"Error adding SSH key. Status code: {response.status_code}")
            print(f"Response: {response.text}")
            response.raise_for_status()

    except requests.exceptions.RequestException as e:
        print(f"Error making API request: {str(e)}")
        raise
    except Exception as e:
        print(f"Error adding SSH key: {str(e)}")
        raise


def list_ssh_keys(organization_url, personal_access_token):
    """
    List existing SSH keys in Azure DevOps using REST API

    Args:
        organization_url (str): Your Azure DevOps organization URL
        personal_access_token (str): Your PAT with appropriate permissions

    Returns:
        list: List of SSH keys
    """

    # Prepare the API endpoint
    api_url = f"{organization_url.rstrip('/')}/_apis/git/sshkeys?api-version=7.1"

    # Prepare headers
    headers = {
        "Authorization": f'Basic {base64.b64encode(f":{personal_access_token}".encode()).decode()}'
    }

    try:
        response = requests.get(api_url, headers=headers)

        if response.status_code == 200:
            result = response.json()
            return result.get("value", [])
        else:
            print(f"Error listing SSH keys. Status code: {response.status_code}")
            print(f"Response: {response.text}")
            response.raise_for_status()

    except requests.exceptions.RequestException as e:
        print(f"Error making API request: {str(e)}")
        raise
    except Exception as e:
        print(f"Error listing SSH keys: {str(e)}")
        raise


def delete_ssh_key(organization_url, personal_access_token, ssh_key_id):
    """
    Delete an SSH key from Azure DevOps using REST API

    Args:
        organization_url (str): Your Azure DevOps organization URL
        personal_access_token (str): Your PAT with appropriate permissions
        ssh_key_id (str): The ID of the SSH key to delete

    Returns:
        bool: True if successful
    """

    # Prepare the API endpoint
    api_url = (
        f"{organization_url.rstrip('/')}/_apis/git/sshkeys/{ssh_key_id}?api-version=7.1"
    )

    # Prepare headers
    headers = {
        "Authorization": f'Basic {base64.b64encode(f":{personal_access_token}".encode()).decode()}'
    }

    try:
        response = requests.delete(api_url, headers=headers)

        if response.status_code == 204:
            print(f"SSH key {ssh_key_id} deleted successfully!")
            return True
        else:
            print(f"Error deleting SSH key. Status code: {response.status_code}")
            print(f"Response: {response.text}")
            response.raise_for_status()

    except requests.exceptions.RequestException as e:
        print(f"Error making API request: {str(e)}")
        raise
    except Exception as e:
        print(f"Error deleting SSH key: {str(e)}")
        raise


def read_ssh_public_key_from_file(file_path):
    """
    Read SSH public key from a file

    Args:
        file_path (str): Path to the SSH public key file (usually ~/.ssh/id_rsa.pub)

    Returns:
        str: SSH public key content
    """
    try:
        file_path = Path(file_path).expanduser()
        with open(file_path, "r") as file:
            content = file.read().strip()

        # Validate that it looks like an SSH public key
        if not any(
            content.startswith(prefix)
            for prefix in ["ssh-rsa", "ssh-dss", "ssh-ed25519", "ecdsa-sha2-"]
        ):
            raise ValueError("File doesn't appear to contain a valid SSH public key")

        return content
    except FileNotFoundError:
        raise FileNotFoundError(f"SSH public key file not found: {file_path}")
    except Exception as e:
        raise Exception(f"Error reading SSH public key file: {str(e)}")


# Example usage with Azure DevOps Python SDK for other operations
def get_user_profile_with_sdk(organization_url, personal_access_token):
    """
    Example of using the Azure DevOps Python SDK for operations that are supported
    """
    try:
        from azure.devops.connection import Connection
        from msrest.authentication import BasicAuthentication

        credentials = BasicAuthentication("", personal_access_token)
        connection = Connection(base_url=organization_url, creds=credentials)

        # Get core client for user profile
        core_client = connection.clients.get_core_client()
        user_profile = core_client.get_profile()

        return {
            "id": user_profile.id,
            "display_name": user_profile.display_name,
            "email_address": user_profile.email_address,
        }
    except ImportError:
        print("Azure DevOps SDK not installed. Install with: pip install azure-devops")
        return None
    except Exception as e:
        print(f"Error getting user profile: {str(e)}")
        return None


# Example usage
if __name__ == "__main__":
    # Configuration
    ORGANIZATION_URL = "https://dev.azure.com/ascendionava"
    PERSONAL_ACCESS_TOKEN = "AqGM5NMeUFejzFTuveNuyp28lJt0NPcWEXDbUToUZnYZkLOm6Mw5JQQJ99BEACAAAAASf5FgAAASAZDO2mzh"
    SSH_KEY_FILE_PATH = (
        "~/.ssh/netlify_global_ado_key.pub"  # Path to your SSH public key file
    )
    KEY_DESCRIPTION = "My Development Machine Key"

    try:
        # Read SSH public key from file
        ssh_public_key = read_ssh_public_key_from_file(SSH_KEY_FILE_PATH)
        print(f"SSH key read successfully from {SSH_KEY_FILE_PATH}")

        # Add the SSH key to Azure DevOps
        result = add_ssh_public_key(
            organization_url=ORGANIZATION_URL,
            personal_access_token=PERSONAL_ACCESS_TOKEN,
            ssh_public_key_content=ssh_public_key,
            key_description=KEY_DESCRIPTION,
        )

        print("\nSSH key added successfully:")
        print(f"  ID: {result.get('sshKeyId')}")
        print(f"  Name: {result.get('displayName')}")
        print(f"  Fingerprint: {result.get('fingerprint')}")

        # List all SSH keys to verify
        print("\nAll SSH keys:")
        keys = list_ssh_keys(ORGANIZATION_URL, PERSONAL_ACCESS_TOKEN)
        for key in keys:
            print(f"  - {key.get('displayName')} (ID: {key.get('sshKeyId')})")
            print(f"    Fingerprint: {key.get('fingerprint')}")

        # Optional: Get user profile using SDK (example of SDK usage)
        profile = get_user_profile_with_sdk(ORGANIZATION_URL, PERSONAL_ACCESS_TOKEN)
        if profile:
            print(
                f"\nUser Profile: {profile['display_name']} ({profile['email_address']})"
            )

    except Exception as e:
        print(f"Failed to add SSH key: {str(e)}")
