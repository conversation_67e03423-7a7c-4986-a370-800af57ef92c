import os
import json
from typing import Any, Dict, List, <PERSON>ple

from src.utils.logger import AppLogger

# Configure logging
logging = AppLogger("React-PH").get_logger()


class ReactPromptHelper:
    """
    Helper class for organizing and processing React project files.

    This class provides utilities for categorizing React files and determining
    the optimal order for file generation based on dependencies between different
    types of files in a React project.
    """

    @staticmethod
    def _normalize_path(file_path: str) -> str:
        """
        Normalizes file paths for consistent processing.

        Args:
            file_path: The file path to normalize

        Returns:
            Normalized path with forward slashes and no leading ./
        """
        if file_path.startswith("./"):
            file_path = file_path[2:]
        return file_path.replace("\\", "/")

    @staticmethod
    def _get_component_info(file_path: str) -> Tuple[str, str]:
        """
        Extracts component name and type (layout, ui, feature) based on path.

        Args:
            file_path: Path to the component file

        Returns:
            Tuple containing (component_name, component_type)
        """
        parts = file_path.split("/")
        filename = parts[-1]
        file_stem = os.path.splitext(filename)[0]

        # Default component name is the file stem (e.g., "Header" from "Header.tsx")
        component_name = file_stem
        # If it's an index file, use the parent directory name
        if file_stem.lower() == "index" and len(parts) > 1:
            component_name = parts[-2]

        # Determine type based on path
        if "components/layout" in file_path:
            return component_name, "layout"
        elif "components/ui" in file_path:
            return component_name, "ui"
        elif "components" in file_path:
            # Try to get a more descriptive name if nested deeper
            # e.g., components/hospital/MetricCard.tsx -> hospital/MetricCard
            try:
                components_index = parts.index("components")
                if (
                    len(parts) > components_index + 2
                ):  # Check if there's a subfolder + filename
                    component_name = (
                        "/".join(parts[components_index + 1 : -1]) + "/" + file_stem
                    )
                elif (
                    len(parts) > components_index + 1
                ):  # Check filename directly under components
                    component_name = (
                        file_stem  # Keep as is if directly under components
                    )
            except ValueError:
                pass  # Should not happen if "components" is in file_path
            return (
                component_name,
                "feature",
            )  # Treat other components as feature-specific

        # Default if somehow missed (e.g., component at root level)
        return component_name, "feature"

    @staticmethod
    def _categorize_files(files_to_generate: List[str]) -> Dict[str, Any]:
        """
        Categorizes files based on their typical role in a React project.

        This method organizes files into logical groups for better processing:
        - config: Configuration files like package.json, tsconfig.json
        - global_styles: Global CSS/SCSS files
        - utils_lib: Utility files, hooks, types
        - components: Component files grouped by type and name
        - pages: Page-level components
        - root: Root application files
        - unknown: Files that don't fit other categories

        Args:
            files_to_generate: List of file paths to categorize

        Returns:
            Dictionary with categorized files
        """
        # Refined categories for better structure and ordering
        categories = {
            "config": [],  # Project/build configs (package.json, vite.config, tailwind.config)
            "global_styles": [],  # Global CSS/SCSS (index.css, global.css)
            "utils_lib": [],  # Utilities, helpers, hooks, types (lib/, utils/, hooks/)
            "components": {  # Component files grouped by type (layout, features)
                "layout": [],  # Layout components (Header, Sidebar, etc.)
                "features": [],  # Feature components (UserCard, Dashboard, etc.)
            },
            "pages": [],  # Page-level components (pages/, views/)
            "root": [],  # Root application files (App.tsx, main.tsx, index.html)
            "unknown": [],  # Anything else
        }

        # Simplified patterns - focusing on common React structures
        config_files = [
            "package.json",
            "tsconfig.json",
            "vite.config.js",
            "vite.config.ts",
            "next.config.js",
            "next.config.mjs",
            "vue.config.js",
            "angular.json",
            "jsconfig.json",
            "postcss.config.js",
            ".editorconfig",
            "tsconfig.app.json",
            "tsconfig.spec.json",
            ".gitignore",
            # Design system configs often act as project config
            "tailwind.config.js",
            "tailwind.config.ts",
        ]
        root_app_files = [  # Base names of root files
            "App.jsx",
            "App.tsx",
            "index.js",
            "main.jsx",
            "main.tsx",
            "layout.jsx",
            "layout.tsx",  # Root layout in some frameworks like Next.js
            "index.html",  # Typically the entry HTML
        ]
        # React component extensions
        component_extensions = [".jsx", ".tsx"]
        style_extensions = [".css", ".scss", ".module.css", ".module.scss"]
        all_component_related_extensions = component_extensions + style_extensions

        normalized_files = [
            ReactPromptHelper._normalize_path(f) for f in files_to_generate
        ]
        processed_files = set()  # Keep track of files already categorized

        # --- Categorization Pass ---
        for file_path in normalized_files:
            if file_path in processed_files:
                continue

            base_name = os.path.basename(file_path)
            dir_name = os.path.dirname(file_path)

            # 1. Config Files (by base name)
            if base_name in config_files:
                categories["config"].append(file_path)
                processed_files.add(file_path)
                continue

            # 2. Root Application Files (by base name, typically in src/ or root)
            # Check if it's a root file AND resides in expected locations (root or src/)
            is_root_file = base_name in root_app_files
            is_in_root_or_src = not dir_name or dir_name == "src"
            # Adjust check for Next.js App Router layout/page convention at app/ level
            is_nextjs_app_root = (
                "app/" in file_path
                and base_name in ["layout.tsx", "layout.jsx", "page.tsx", "page.jsx"]
                and file_path.count("/") <= 1
            )

            if is_root_file and (is_in_root_or_src or is_nextjs_app_root):
                # Special case: Don't categorize Next.js app/page.tsx as root, it's a page
                if not ("app/page.tsx" in file_path or "app/page.jsx" in file_path):
                    categories["root"].append(file_path)
                    processed_files.add(file_path)
                    continue

            # 3. Global Styles (specific names/paths)
            global_style_names = [
                "index.css",
                "global.css",
                "styles.css",
                "index.scss",
                "global.scss",
                "styles.scss",
            ]
            global_style_dirs = ["src", "styles", "src/styles", ""]

            if base_name in global_style_names and dir_name in global_style_dirs:
                categories["global_styles"].append(file_path)
                processed_files.add(file_path)
                continue

            # 4. Utilities / Lib / Hooks / Types (by directory)
            util_directories = [
                "src/lib/",
                "src/utils/",
                "src/hooks/",
                "src/types/",
                "src/interfaces/",
                "src/shared/",
                "src/core/",
                "src/api/",
                "src/service",
            ]

            if any(part in file_path for part in util_directories):
                categories["utils_lib"].append(file_path)
                processed_files.add(file_path)
                continue

            # 5. Pages (by directory)
            page_directories = ["src/pages/", "src/views/", "app/"]
            is_page_file = any(part in file_path for part in page_directories) and any(
                file_path.endswith(ext) for ext in component_extensions
            )

            if is_page_file:
                # Handle Next.js app router convention where 'page.tsx' signifies a page
                if "app/" in file_path and base_name in ["page.tsx", "page.jsx"]:
                    # Get descriptive page name from folder structure
                    # e.g., 'dashboard/settings' from 'app/dashboard/settings/page.tsx'
                    page_name = "/".join(file_path.split("/")[1:-1])
                    page_name = page_name if page_name else "home"  # Root page
                    categories["pages"].append({"name": page_name, "path": file_path})
                    processed_files.add(file_path)
                    continue
                elif "src/pages/" in file_path or "src/views/" in file_path:
                    # For traditional page structures, use the filename as page name
                    categories["pages"].append(
                        {"name": os.path.splitext(base_name)[0], "path": file_path}
                    )
                    processed_files.add(file_path)
                    continue

            # 6. Components (tsx/jsx or related styles)
            is_component_file = "src/components" in file_path and any(
                file_path.endswith(ext) for ext in all_component_related_extensions
            )

            if is_component_file:
                # Get component type (layout, ui, feature) - we don't need the name anymore
                _, comp_type = ReactPromptHelper._get_component_info(file_path)

                # Map "feature" type to "features" for consistency
                if comp_type == "feature":
                    comp_type = "features"

                # Add the file directly to the appropriate component type list
                # Only handle "layout" and "features" types
                if comp_type in ["layout", "features"]:
                    categories["components"][comp_type].append(file_path)
                # Handle "ui" or other component types by adding them to "features"
                else:
                    categories["components"]["features"].append(file_path)

                processed_files.add(file_path)
                continue

            # 7. Unknown
            if file_path not in processed_files:
                categories["unknown"].append(file_path)
                processed_files.add(file_path)

        # Sort pages alphabetically by name for deterministic order
        categories["pages"].sort(key=lambda x: x["name"])
        # Extract just the path for the final list
        categories["pages"] = [p["path"] for p in categories["pages"]]

        logging.info(f"Categorized files: {json.dumps(categories, indent=2)}")
        return categories

    @staticmethod
    def get_generation_order(categorized_files: Dict[str, Any]) -> List[str]:
        """
        Returns a flattened list of files in a recommended generation order.

        The order follows a logical dependency chain for React projects:
        1. Config files (package.json, tsconfig.json, etc.)
        2. Global styles (CSS/SCSS files)
        3. Utilities, libraries, hooks, types
        4. UI components (base components)
        5. Feature components (domain-specific components)
        6. Layout components (components that use other components)
        7. Pages (page-level components)
        8. Root files (App.tsx, main.tsx, etc.)
        9. Unknown files

        Args:
            categorized_files: Dictionary with categorized files from _categorize_files

        Returns:
            List of file paths in recommended generation order
        """
        ordered_files = []

        # 1. Config files
        ordered_files.extend(categorized_files.get("config", []))

        # 2. Global Styles
        ordered_files.extend(categorized_files.get("global_styles", []))

        # 3. Utils / Lib
        ordered_files.extend(categorized_files.get("utils_lib", []))

        # 4. Components - Order: features first, then layout
        components = categorized_files.get("components", {})

        # Process components in dependency order: features first, then layout
        component_types = ["features", "layout"]
        for comp_type in component_types:
            # Get the list of files for this component type
            ordered_files.extend(components.get(comp_type, []))

        # 5. Pages (often depend on layout and feature components)
        ordered_files.extend(categorized_files.get("pages", []))

        # 6. Root files (App.tsx, main.tsx - depend on everything else)
        ordered_files.extend(categorized_files.get("root", []))

        # 7. Unknown files (add them at the end)
        ordered_files.extend(categorized_files.get("unknown", []))

        # Remove duplicates to ensure each file appears only once
        final_ordered_list = []
        seen = set()
        for file_path in ordered_files:
            if file_path not in seen:
                final_ordered_list.append(file_path)
                seen.add(file_path)

        logging.info("Final generation order determined.")
        return final_ordered_list

    @staticmethod
    def _create_prompt_for_batch(
        batch_type: str,
        files_in_batch: List[str],
        context_files: Dict[str, str],
        detailed_prd: dict,
        component_name: str = None,
    ) -> str:
        """
        Generates a highly specific and structured prompt for the LLM to generate code
        for a batch of files, addressing common pitfalls like imports, component usage,
        and data flow.

        Args:
            batch_type: The category of files ('config', 'component', 'pages', 'root').
            files_in_batch: List of absolute file paths to be generated in this batch.
            context_files: Dictionary of previously generated files {file_path: content}.
            detailed_prd: Dictionary containing project requirements and analysis.
            component_name: Full path/name of the component (used for 'component' type).

        Returns:
            A string containing the detailed prompt for the LLM.
        """

        # --- 1. Role Definition ---
        role = """
            # ROLE: Expert React Frontend Developer

            You are a senior React developer specializing in building production-grade frontends with TypeScript and Tailwind CSS.
            Your task is to write clean, efficient code that follows best practices and implements the exact requirements.
        """

        # --- 2. Core Task ---
        goal = f"""
            # GOAL: Generate Code for {batch_type.upper()} File{'s' if len(files_in_batch) > 1 else ''}

            Generate complete, production-ready code for:
            {chr(10).join([f'- `{f}`' for f in files_in_batch])}

            Return a JSON object with file paths as keys and code as values.
        """

        # --- 3. Project Context & Constraints ---
        # Simplify context presentation
        context_code_str = (
            json.dumps(context_files, indent=2) if context_files else "{}"
        )
        prd_project_info = detailed_prd.get("projectInfo", {})
        prd_tech_stack_overview = detailed_prd.get("uiOverview", {})
        prd_component_hierarchy = detailed_prd.get("componentBreakdown", {})
        prd_layout_info = detailed_prd.get("layoutStructure", {})

        inputs = f"""
            # INPUTS: Project Context & Requirements

            **1. Project Requirements:**
               - **Project Info:** {json.dumps(prd_project_info)}
               - **Tech Stack Overview:** {json.dumps(prd_tech_stack_overview, indent=2)}
               - **Component Hierarchy:** {json.dumps(prd_component_hierarchy, indent=2)}
               - **Layout Requirements:** {json.dumps(prd_layout_info, indent=2)}

            **2. Context Code:**
               Analyze this code to ensure consistency and understand existing patterns.
            ```json
            {context_code_str}
            ```

            **3. Technical Requirements (MANDATORY):**
               - **Stack:** React 18.3+ with TypeScript 5.5+, Tailwind CSS
               - **Components:** Use Shadcn UI components from `@/components/ui/` - CRITICAL: Import using EXACT path
               - **Icons:** Use `lucide-react` ONLY - `import {{ IconName }} from 'lucide-react'` - CRITICAL: NEVER use icons that don't exist in the lucide library (e.g., Google, App, etc.)
               - **Charts:** Use `recharts` with realistic, complex data - MANDATORY: Make charts visually appealing with varying data points (highs and lows) to look like real, complex charts
               - **State:** Use React hooks (useState, useEffect, useContext) - CRITICAL: Follow React best practices
               - **Data:** Define dummy data directly in components - CRITICAL: NEVER import data from external files

            **4. Critical Guidelines (STRICTLY ENFORCE):**
               - **Imports (MANDATORY):**
                 - Shadcn UI: `@/components/ui/component-name` - CRITICAL: Use EXACT import path
                 - Project components: Use relative paths - CRITICAL: Verify paths are correct
               - **TypeScript (CRITICAL):**
                 - MANDATORY: Use explicit interfaces/types for props and data
                 - CRITICAL: NEVER use `any` type - use specific types or `unknown` if necessary
                 - MANDATORY: Use proper type narrowing and guards
               - **Shadcn Components (CRITICAL):**
                 - MANDATORY: Only use documented variants (e.g., for Button: "default", "destructive", "outline", "secondary", "ghost", "link")
                 - CRITICAL: Never invent new variants that don't exist
                 - MANDATORY: Use className prop for styling, not variant for colors
               - **Styling (MANDATORY):** Use Tailwind utilities directly in JSX

            **5. Dependencies (CRITICAL - USE EXACT VERSIONS):**
               ```json
               {{
                 "dependencies": {{
                   "@hookform/resolvers": "^3.9.0",
                   "@radix-ui/react-accordion": "^1.2.0",
                   "@radix-ui/react-alert-dialog": "^1.1.1",
                   "@radix-ui/react-aspect-ratio": "^1.1.0",
                   "@radix-ui/react-avatar": "^1.1.0",
                   "@radix-ui/react-checkbox": "^1.1.1",
                   "@radix-ui/react-collapsible": "^1.1.0",
                   "@radix-ui/react-context-menu": "^2.2.1",
                   "@radix-ui/react-dialog": "^1.1.2",
                   "@radix-ui/react-dropdown-menu": "^2.1.1",
                   "@radix-ui/react-hover-card": "^1.1.1",
                   "@radix-ui/react-label": "^2.1.0",
                   "@radix-ui/react-menubar": "^1.1.1",
                   "@radix-ui/react-navigation-menu": "^1.2.0",
                   "@radix-ui/react-popover": "^1.1.1",
                   "@radix-ui/react-progress": "^1.1.0",
                   "@radix-ui/react-radio-group": "^1.2.0",
                   "@radix-ui/react-scroll-area": "^1.1.0",
                   "@radix-ui/react-select": "^2.1.1",
                   "@radix-ui/react-separator": "^1.1.0",
                   "@radix-ui/react-slider": "^1.2.0",
                   "@radix-ui/react-slot": "^1.1.0",
                   "@radix-ui/react-switch": "^1.1.0",
                   "@radix-ui/react-tabs": "^1.1.0",
                   "@radix-ui/react-toast": "^1.2.1",
                   "@radix-ui/react-toggle": "^1.1.0",
                   "@radix-ui/react-toggle-group": "^1.1.0",
                   "@radix-ui/react-tooltip": "^1.1.4",
                   "@tanstack/react-query": "^5.56.2",
                   "class-variance-authority": "^0.7.1",
                   "clsx": "^2.1.1",
                   "cmdk": "^1.0.0",
                   "date-fns": "^3.6.0",
                   "embla-carousel-react": "^8.3.0",
                   "input-otp": "^1.2.4",
                   "lucide-react": "^0.462.0",
                   "next-themes": "^0.3.0",
                   "react": "^18.3.1",
                   "react-day-picker": "^8.10.1",
                   "react-dom": "^18.3.1",
                   "react-hook-form": "^7.53.0",
                   "react-resizable-panels": "^2.1.3",
                   "react-router-dom": "^6.26.2",
                   "recharts": "^2.12.7",
                   "sonner": "^1.5.0",
                   "tailwind-merge": "^2.5.2",
                   "tailwindcss-animate": "^1.0.7",
                   "vaul": "^0.9.3",
                   "zod": "^3.23.8"
                 }},
                 "devDependencies": {{
                   "@eslint/js": "^9.9.0",
                   "@tailwindcss/typography": "^0.5.15",
                   "@types/node": "^22.5.5",
                   "@types/react": "^18.3.3",
                   "@types/react-dom": "^18.3.0",
                   "@vitejs/plugin-react-swc": "^3.5.0",
                   "autoprefixer": "^10.4.20",
                   "eslint": "^9.9.0",
                   "eslint-plugin-react-hooks": "^5.1.0-rc.0",
                   "eslint-plugin-react-refresh": "^0.4.9",
                   "globals": "^15.9.0",
                   "lovable-tagger": "^1.1.7",
                   "postcss": "^8.4.47",
                   "tailwindcss": "^3.4.11",
                   "typescript": "^5.5.3",
                   "typescript-eslint": "^8.0.1",
                   "vite": "^5.4.1"
                 }}
               }}
               ```
        """

        # --- 4. General Coding Standards ---
        general_standards = """
            # CODE QUALITY STANDARDS

            - Write complete, runnable code with proper syntax
            - Use functional components with proper TypeScript typing
            - Follow React best practices (hooks, keys for lists)
            - Use 2-space indentation and consistent formatting
            - Add error handling for edge cases
            - Focus on the specific requirements of the file being generated
        """

        # --- 5. Specific Instructions based on Batch Type ---
        instructions_header = (
            f"\n# SPECIFIC INSTRUCTIONS for Batch Type: '{batch_type.upper()}'\n"
        )
        specific_instructions = ""

        # --- Shadcn UI Component Information ---
        shadcn_info = """
            **Shadcn UI Components (CRITICAL REQUIREMENTS):**
            - **MANDATORY:** Import using EXACT path: `import { Component } from '@/components/ui/component'`
            - **Available components:** accordion, alert, avatar, badge, button, calendar, card, checkbox, dialog, dropdown-menu, form, input, label, select, tabs, toast, etc.
            - **CRITICAL:** Only use documented variants that actually exist:
              - Button: "default", "destructive", "outline", "secondary", "ghost", "link"
              - Badge: "default", "secondary", "destructive", "outline"
              - Card: No variants, use className for styling
              - Alert: "default", "destructive"
            - **MANDATORY:** Style with Tailwind via className prop, NOT by inventing variants:
              - CORRECT: `<Badge variant="outline" className="bg-red-100 text-red-600">Critical</Badge>`
              - INCORRECT: `<Badge variant="error">Critical</Badge>` (error variant doesn't exist)
            - **CRITICAL:** Never modify the core functionality of Shadcn components
            - **MANDATORY:** Use the cn utility for conditional classes: `className={cn("base", isActive && "active")}`
        """

        if batch_type == "config":
            specific_instructions = f"""
            - Generate standard, functional configuration files for a React/Vite/TypeScript/Tailwind project based ONLY on the target file paths requested (`{", ".join(files_in_batch)}`).
            - **`package.json`:** If generating `package.json`, use the EXACT dependencies and versions listed in the 'Dependencies' section under INPUTS. Do not add or change versions unless absolutely necessary and document why in a comment. Ensure `"type": "module"`.
            """

        elif batch_type == "component":
            # component_context will be defined inside the if/elif blocks
            component_context = ""  # Initialize
            component_type_for_prompt = "Component"  # Default display type
            component_name_base = (
                os.path.basename(component_name).split(".")[0]
                if component_name
                else "Unnamed"
            )

            # --- Simple Component Type Logic (based on original snippet) ---
            if (
                component_name and "feature" in component_name.lower()
            ):  # Use lower() for case-insensitivity
                feature_name = component_name.split("/")[-1].replace(
                    ".tsx", ""
                )  # Get name as per original snippet
                component_type_for_prompt = f"FEATURE: {feature_name}"
                component_context = f"""
                    ## Feature Component: {feature_name}

                    **CRITICAL: Focus EXCLUSIVELY on implementing this specific feature component - ignore other parts of the UI.**

                    **Implementation Requirements (MANDATORY):**
                    1. **Data (CRITICAL):** Define realistic, complex dummy data directly in this component file
                       ```typescript
                       // Example structure - customize based on the feature's needs
                       const {feature_name.lower()}Data = [
                         {{ id: 1, name: "Example", status: "active" as const, value: 42 }},
                         {{ id: 2, name: "Another", status: "inactive" as const, value: 27 }}
                       ];

                       // For charts, create complex data with varying values
                       const chartData = [
                         {{ name: "Jan", value: 400 }},
                         {{ name: "Feb", value: 300 }},
                         {{ name: "Mar", value: 600 }},
                         {{ name: "Apr", value: 200 }},
                         {{ name: "May", value: 700 }},
                         {{ name: "Jun", value: 100 }}
                       ] ;
                       ```

                    2. **Props Interface (MANDATORY):** Create a clear props interface with proper typing
                       ```typescript
                       interface {feature_name}Props {{
                         // CRITICAL: Only use string literals that actually exist in your component
                         variant?: 'default' | 'compact' | 'minimal';
                         className?: string;
                         // Add other props as needed
                       }}
                       ```

                    3. **Component Structure (CRITICAL):**
                       - **MANDATORY:** Use Shadcn UI components for UI elements (import from @/components/ui/...)
                       - **CRITICAL:** Implement proper state management with hooks (useState, useEffect)
                       - **MANDATORY:** Add appropriate interactivity (click handlers, form handling, etc.)
                       - **CRITICAL:** Use ONLY lucide-react icons that actually exist in the library
                       - **MANDATORY:** If creating a chart, use recharts with complex, realistic data that varies
                       - **CRITICAL:** Use proper TypeScript typing throughout with EXPLICIT types
                       - **CRITICAL:** ALWAYS use 'as const' for literal values
                       - **MANDATORY:** Never use 'any' type - use proper types or 'unknown'

                    4. **Styling (CRITICAL):**
                       - **MANDATORY:** Use Tailwind CSS utilities directly in JSX
                       - **CRITICAL:** Use the cn utility for conditional classes: cn("base-class", condition && "conditional-class")
                       - **MANDATORY:** Follow consistent spacing and layout patterns
                       - **CRITICAL:** Never invent custom variants for Shadcn components
                """

            elif (
                component_name and "layout" in component_name.lower()
            ):  # Use lower() for case-insensitivity
                layout_name = component_name.split("/")[-1].replace(
                    ".tsx", ""
                )  # Get name as per original snippet
                component_type_for_prompt = f"LAYOUT: {layout_name}"
                component_context = f"""
                    ## Layout Component: {layout_name}

                    **CRITICAL Purpose:** Structure and arrange feature components and UI elements

                    **Implementation Requirements (MANDATORY):**
                    1. **Props Interface (CRITICAL):**
                       ```typescript
                       interface {layout_name}Props {{
                         children: React.ReactNode; // MANDATORY: Always accept children
                         title?: string;
                         className?: string;
                         // Add other props needed to pass data to features
                       }}
                       ```

                    2. **Component Structure (CRITICAL):**
                       - **MANDATORY:** Import and render feature components from context files using RELATIVE paths
                       - **CRITICAL:** Use Shadcn UI components for structural elements (Card, Separator, etc.)
                       - **MANDATORY:** Implement responsive layout with Tailwind (Flexbox/Grid)
                       - **CRITICAL:** Accept children prop for page content
                       - **MANDATORY:** Ensure proper TypeScript typing throughout
                       - **CRITICAL:** Never use absolute paths starting with 'src/'

                    3. **Data Handling (CRITICAL):**
                       - **MANDATORY:** Receive data via props from parent page components
                       - **CRITICAL:** Pass appropriate data down to feature components
                       - **MANDATORY:** Only define internal data for layout-specific elements (e.g., navigation)
                       - **CRITICAL:** Example navigation data:
                         ```typescript
                         const navigationItems = [
                           {{ label: "Dashboard", href: "/", icon: "Home" as const }},
                           {{ label: "Analytics", href: "/analytics", icon: "BarChart" as const }},
                           {{ label: "Settings", href: "/settings", icon: "Settings" as const }}
                         ];
                         ```

                    4. **Interactivity (MANDATORY):**
                       - **CRITICAL:** Add layout-specific interactions (sidebar toggle, etc.)
                       - **MANDATORY:** Use ONLY lucide-react icons that actually exist in the library
                       - **CRITICAL:** Implement state management for layout features (collapsed sidebar, etc.)
                       - **MANDATORY:** Example:
                         ```typescript
                         const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
                         ```
                       - **CRITICAL:** Ensure all event handlers are properly typed
                """
            else:
                # If neither "feature" nor "layout" is found, provide a generic message
                component_type_for_prompt = f"Component: {component_name_base}"
                component_context = f"""
                    ## Generic Component: {component_name_base}

                    Analyze the component name and path to determine its purpose:

                    - If it's a self-contained feature: Define internal data and implement UI
                    - If it's a layout component: Accept children and compose other components
                    - If it's a UI primitive: Focus on reusability and accessibility

                    Follow the same TypeScript, styling, and import conventions as other components.
                """

            # --- Combine Specific Instructions ---
            specific_instructions = f"""
            # {component_type_for_prompt}

            {shadcn_info}

            {component_context}

            ## Implementation Template (CRITICAL - FOLLOW EXACTLY):
            ```typescript
            import React from 'react';
            import {{ cn }} from '@/lib/utils'; // MANDATORY: Import cn utility
            // CRITICAL: Import Shadcn UI components with EXACT paths
            import {{ Card, CardContent, CardHeader, CardTitle }} from '@/components/ui/card';
            import {{ Button }} from '@/components/ui/button';
            // CRITICAL: Only import lucide icons that actually exist
            import {{ BarChart, Settings, User }} from 'lucide-react';

            // MANDATORY: Define explicit interface with proper types
            interface {component_name_base}Props {{
              // CRITICAL: Only use string literals that will actually be used
              variant?: 'default' | 'compact';
              className?: string;
            }}

            // CRITICAL: Use React.FC with the proper interface
            const {component_name_base}: React.FC<{component_name_base}Props> = ({{
              variant = 'default', // MANDATORY: Default value for optional props
              className
            }}) => {{
              // CRITICAL: Proper state management with explicit typing
              const [isLoading, setIsLoading] = React.useState<boolean>(false);

              // MANDATORY: Define data when literals are used add 'as const'
              const data = [
                {{ id: 1, name: "Example", status: "active" as const, value: 42 }},
                {{ id: 2, name: "Sample", status: "inactive" as const, value: 27 }}
              ];

              // MANDATORY: Implement proper event handlers with useCallback
              const handleClick = React.useCallback(() => {{
                // Function implementation
              }}, []);

              // CRITICAL: Use proper JSX structure with Shadcn components
              return (
                <Card className={{cn("w-full", className)}}>
                  <CardHeader>
                    <CardTitle>Title</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {'{/* CRITICAL: Use proper mapping with key prop */}'}
                      {'{/* Example of data mapping */}'}

                      {'{/* CRITICAL: Use proper Button variant */}'}
                      <Button
                        variant="default"
                        onClick={{() => console.log("clicked")}}
                      >
                        Submit
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            }};

            export default {component_name_base};
            ```

            Focus exclusively on implementing this specific component according to the requirements.
            """

        elif batch_type == "pages":
            specific_instructions = f"""
            # PAGE COMPONENT

            {shadcn_info}

            ## Page Implementation Requirements:

            1. **Structure:**
               - Import and use appropriate Layout components
               - The Layout will handle rendering Feature components

            2. **Data:**
               - Define all dummy data directly in this page file
               - Create TypeScript interfaces for data structures
               - Pass data down to Layout components as props

            3. **Functionality:**
               - Handle page-level state (filters, selections)
               - Implement routing with react-router-dom if needed
               - Ensure data structures match Layout component prop types

            ## Layout Information:
            {detailed_prd.get("detailedLayoutAnalysis","")}
            """

        elif batch_type == "root":
            specific_instructions = f"""
            # ROOT APPLICATION FILES

            ## Implementation Requirements:

            1. **Entry Point (main.tsx):**
               - Use ReactDOM.createRoot
               - Set up global providers (BrowserRouter, ThemeProvider)
               - Import global styles

            2. **App Component (App.tsx):**
               - Implement routing with react-router-dom
               - Import and render Page components
               - Add global UI elements (Toaster, etc.)

            3. **Structure:**
               - Keep the implementation clean and focused
               - Follow best practices for React application structure
               - Ensure proper error handling
            """

        else:  # Fallback for unknown batch types
            specific_instructions = """
            # GENERIC FILE GENERATION

            - Analyze the file path to determine its purpose
            - Follow React/TypeScript/Tailwind best practices
            - Maintain consistency with existing code patterns
            - Use Shadcn UI components where appropriate
            """

        # --- 6. Output Format & Final Instructions ---
        output_req = f"""
            # OUTPUT FORMAT (CRITICAL - FOLLOW EXACTLY)

            1. **MANDATORY:** Return a valid JSON object where:
               - Keys MUST be the exact file paths from the target files list
               - Values MUST be complete code strings with proper syntax

            2. **CRITICAL FORMAT:**
                - Always retrun the json enclosed in ```json and ``` markers

            3. **CRITICAL REQUIREMENTS:**
               - **MANDATORY:** Do NOT include any text, explanations, or markdown outside the JSON object
               - **CRITICAL:** The entire response must be ONLY the JSON object
               - **MANDATORY:** Ensure the code is complete and properly formatted
               - **CRITICAL:** Double-check that all imports are correct (especially Shadcn UI paths)
               - **CRITICAL:** Verify that all TypeScript types are properly defined
               - **MANDATORY:** Make sure all lucide icons actually exist in the library
               - **CRITICAL:** ALWAYS use 'as const' for literal values
               - **MANDATORY:** Only use documented variants for Shadcn components
               - **CRITICAL:** Ensure all component props are properly typed
               - **MANDATORY:** Follow all the specific instructions for the component type
        """

        # --- Combine Prompt Sections ---
        prompt = f"{role}{goal}{inputs}{general_standards}{instructions_header}{specific_instructions}{output_req}"
        return prompt
