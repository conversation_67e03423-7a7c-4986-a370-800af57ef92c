import asyncio
import uuid
import json
import asyncpg
from typing import Dict, Any, Optional, List

from src.services.database.db_operations import get_connection
from src.utils.logger import AppLogger

logger = AppLogger(__name__).get_logger()


class ProjectDatabaseService:
    """Service class for handling project-related database operations."""

    @staticmethod
    async def create_project(
        project_name: str,
        project_description: str,
        # project_type: str,
        user_id: str,
        project_state: str = "ACTIVE",
    ) -> str:
        """
        Create a new project in the database.

        Args:
            project_name: Name of the project
            project_description: Description of the project
            user_id: ID of the user creating the project
            project_state: State of the project (default: ACTIVE)

        Returns:
            The UUID of the created project
        """
        try:
            con = await get_connection()
            project_id = str(uuid.uuid4())

            # First, check if we're dealing with the public schema or migration_schema
            # Check if the projects table exists in migration_schema
            migration_schema_exists = await con.fetchval(
                """
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = 'migration_schema'
                    AND table_name = 'projects'
                )
                """
            )

            if migration_schema_exists:
                # Using migration_schema
                # First, verify that the user exists in migration_schema.users
                user_exists = await con.fetchval(
                    """
                    SELECT COUNT(*) FROM migration_schema.users
                    WHERE user_id = $1
                    """,
                    user_id,
                )

                if not user_exists:
                    # User doesn't exist in migration_schema, try to find in public schema
                    user_data = await con.fetchrow(
                        """
                        SELECT user_id, email, username, created_at
                        FROM users
                        WHERE user_id = $1
                        """,
                        user_id,
                    )

                    if user_data:
                        # Copy user to migration_schema
                        try:
                            await con.execute(
                                """
                                INSERT INTO migration_schema.users
                                (user_id, email, full_name, created_at)
                                VALUES ($1, $2, $3, $4)
                                """,
                                user_data["user_id"],
                                user_data["email"],
                                user_data["username"],
                                user_data["created_at"],
                            )
                            logger.info(
                                f"Copied user {user_id} from public schema to migration_schema"
                            )
                        except asyncpg.UniqueViolationError:
                            # User already exists, which is fine
                            logger.info(
                                f"User {user_id} already exists in migration_schema"
                            )
                    else:
                        # User doesn't exist in either schema, create a new one
                        logger.warning(
                            f"User with ID {user_id} does not exist in any schema. Creating a default user."
                        )
                        # Create a default user with the provided ID
                        await con.execute(
                            """
                            INSERT INTO migration_schema.users
                            (user_id, email, full_name, created_at)
                            VALUES ($1, $2, $3, NOW())
                            """,
                            user_id,
                            f"user_{user_id}@example.com",  # Default email
                            f"User {user_id[:8]}",  # Default name
                        )

                # Now create the project in migration_schema
                await con.execute(
                    """
                    INSERT INTO migration_schema.projects
                    (project_id, project_name, project_description, created_by, project_state)
                    VALUES ($1, $2, $3, $4, $5)
                    """,
                    project_id,
                    project_name,
                    project_description,
                    user_id,
                    project_state,
                )
            else:
                # Using public schema
                # Check if user exists in public schema
                user_exists = await con.fetchval(
                    """
                    SELECT COUNT(*) FROM users
                    WHERE user_id = $1
                    """,
                    user_id,
                )

                if not user_exists:
                    # Create user in public schema
                    await con.execute(
                        """
                        INSERT INTO users
                        (user_id, username, email, created_at)
                        VALUES ($1, $2, $3, NOW())
                        """,
                        user_id,
                        f"User {user_id[:8]}",  # Default username
                        f"user_{user_id}@example.com",  # Default email
                    )

                # Create project in public schema
                await con.execute(
                    """
                    INSERT INTO projects
                    (project_id, project_name, project_description, created_by, project_state)
                    VALUES ($1, $2, $3, $4, $5)
                    """,
                    project_id,
                    project_name,
                    project_description,
                    user_id,
                    project_state,
                )

            await con.close()
            logger.info(f"Created new project with ID: {project_id}")
            return project_id

        except asyncpg.PostgresError as e:
            logger.error(f"Database error while creating project: {str(e)}")
            raise

    @staticmethod
    async def create_project_status(
        project_id: str,
        status_id: str,
        status: str,
        log_message: str,
        progress: str,
        progress_description: str,
        metadata: List[Dict[str, Any]] = None,
    ) -> None:
        """
        Create a new project status entry in the database.

        Args:
            project_id: ID of the project
            status_id: ID of the status (usually a request ID)
            status: Current status (PENDING, IN_PROGRESS, etc.)
            log_message: Log message for this status
            progress: Current progress stage
            progress_description: Description of the current progress
            metadata: Additional metadata as a list of dictionaries
        """
        try:
            con = await get_connection()
            metadata_json = json.dumps(metadata) if metadata else json.dumps([])

            # Validate progress value against allowed values in the constraint
            valid_progress_values = [
                "OVERVIEW",
                "FILE_QUEUE",
                "SEED_PROJECT_INITIALIZED",
                "LAYOUT_ANALYZED",
                "DESIGN_SYSTEM_MAPPED",
                "COMPONENTS_CREATED",
                "PAGES_GENERATED",
                "BUILD_STARTED",
                "BUILD_SUCCEEDED",
                "BUILD_FAILED",
                "COMPLETED",
                "FAILED",
                "CODE_GENERATION",
            ]

            if progress not in valid_progress_values:
                logger.warning(
                    f"Invalid progress value: {progress}. Using 'OVERVIEW' instead."
                )
                progress = "OVERVIEW"

            # Validate status value
            valid_status_values = ["PENDING", "IN_PROGRESS", "COMPLETED", "FAILED"]
            if status not in valid_status_values:
                logger.warning(
                    f"Invalid status value: {status}. Using 'PENDING' instead."
                )
                status = "PENDING"

            # Insert into project_status_v3
            await con.execute(
                """
                INSERT INTO migration_schema.project_status_v3
                (status_id, project_id, progress, status, log, progress_description, metadata)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
                """,
                status_id,
                project_id,
                progress,
                status,
                log_message,
                progress_description,
                metadata_json,
            )

            # Also add to history
            await con.execute(
                """
                INSERT INTO migration_schema.project_status_history
                (status_id, project_id, progress, status, log, progress_description, metadata)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
                """,
                status_id,
                project_id,
                progress,
                status,
                log_message,
                progress_description,
                metadata_json,
            )

            await con.close()
            logger.info(
                f"Created new project status for project: {project_id}, status: {status}"
            )

        except asyncpg.PostgresError as e:
            logger.error(f"Database error while creating project status: {str(e)}")
            raise

    @staticmethod
    async def update_project_status(
        project_id: str,
        status_id: str,
        status: str,
        log_message: str,
        progress: str,
        progress_description: str,
        metadata: List[Dict[str, Any]] = None,
    ) -> None:
        """
        Update an existing project status and add a new history entry.

        Args:
            project_id: ID of the project
            status_id: ID of the status (usually a request ID)
            status: Current status (PENDING, IN_PROGRESS, etc.)
            log_message: Log message for this status
            progress: Current progress stage
            progress_description: Description of the current progress
            metadata: Additional metadata as a list of dictionaries
        """
        try:
            con = await get_connection()
            metadata_json = json.dumps(metadata) if metadata else json.dumps([])

            # Validate progress value against allowed values in the constraint
            valid_progress_values = [
                "OVERVIEW",
                "FILE_QUEUE",
                "SEED_PROJECT_INITIALIZED",
                "LAYOUT_ANALYZED",
                "DESIGN_SYSTEM_MAPPED",
                "COMPONENTS_CREATED",
                "PAGES_GENERATED",
                "BUILD_STARTED",
                "BUILD_SUCCEEDED",
                "BUILD_FAILED",
                "COMPLETED",
                "FAILED",
                "CODE_GENERATION",
            ]

            if progress not in valid_progress_values:
                logger.warning(
                    f"Invalid progress value: {progress}. Using 'OVERVIEW' instead."
                )
                progress = "OVERVIEW"

            # Validate status value
            valid_status_values = ["PENDING", "IN_PROGRESS", "COMPLETED", "FAILED"]
            if status not in valid_status_values:
                logger.warning(
                    f"Invalid status value: {status}. Using 'PENDING' instead."
                )
                status = "PENDING"

            # Update project_status_v3
            await con.execute(
                """
                UPDATE migration_schema.project_status_v3
                SET status = $4, log = $5, progress = $3, progress_description = $6,
                    metadata = $7, updated_at = NOW()
                WHERE project_id = $2 AND status_id = $1
                """,
                status_id,
                project_id,
                progress,
                status,
                log_message,
                progress_description,
                metadata_json,
            )

            # Add new entry to history
            await con.execute(
                """
                INSERT INTO migration_schema.project_status_history
                (status_id, project_id, progress, status, log, progress_description, metadata)
                VALUES ($1, $2, $3, $4, $5, $6, $7)
                """,
                status_id,
                project_id,
                progress,
                status,
                log_message,
                progress_description,
                metadata_json,
            )

            await con.close()
            logger.info(
                f"Updated project status for project: {project_id}, new status: {status}"
            )

        except asyncpg.PostgresError as e:
            logger.error(f"Database error while updating project status: {str(e)}")
            raise

    @staticmethod
    async def get_project_status(project_id: str, status_id: str) -> Dict[str, Any]:
        """
        Get the current status of a project.

        Args:
            project_id: ID of the project
            status_id: ID of the status

        Returns:
            Dictionary containing the project status details
        """
        try:
            con = await get_connection()

            row = await con.fetchrow(
                """
                SELECT * FROM migration_schema.project_status_v3
                WHERE project_id = $1 AND status_id = $2
                """,
                project_id,
                status_id,
            )

            await con.close()

            if row:
                return dict(row)
            return None

        except asyncpg.PostgresError as e:
            logger.error(f"Database error while fetching project status: {str(e)}")
            raise

    @staticmethod
    async def get_project_status_history(
        project_id: str, status_id: str
    ) -> List[Dict[str, Any]]:
        """
        Get the complete history of a project status.

        Args:
            project_id: ID of the project
            status_id: ID of the status

        Returns:
            List of dictionaries containing the project status history
        """
        try:
            con = await get_connection()

            rows = await con.fetch(
                """
                SELECT * FROM migration_schema.project_status_history
                WHERE project_id = $1 AND status_id = $2
                ORDER BY created_at ASC
                """,
                project_id,
                status_id,
            )

            await con.close()

            return [dict(row) for row in rows]

        except asyncpg.PostgresError as e:
            logger.error(
                f"Database error while fetching project status history: {str(e)}"
            )
            raise

    @staticmethod
    async def update_project_name(
        project_id: str, project_name: str, project_description: str = None
    ) -> None:
        """
        Update the name and optionally the description of a project.

        Args:
            project_id: ID of the project
            project_name: New name for the project
            project_description: New description for the project (optional)
        """
        try:
            con = await get_connection()

            if project_description:
                await con.execute(
                    """
                    UPDATE migration_schema.projects
                    SET project_name = $2, project_description = $3, last_modified = NOW()
                    WHERE project_id = $1
                    """,
                    project_id,
                    project_name,
                    project_description,
                )
            else:
                await con.execute(
                    """
                    UPDATE migration_schema.projects
                    SET project_name = $2, last_modified = NOW()
                    WHERE project_id = $1
                    """,
                    project_id,
                    project_name,
                )

            await con.close()
            logger.info(f"Updated project name for project: {project_id}")

        except asyncpg.PostgresError as e:
            logger.error(f"Database error while updating project name: {str(e)}")
            raise

    @staticmethod
    async def create_or_update_project_settings(
        project_id: str,
        device: str,
        framework: str,
        design_system: str,
        generation_type: str,
    ) -> None:
        """
        Create or update project settings in the database.

        Args:
            project_id: ID of the project
            device: Target device platform (web, mobile, tablet)
            framework: JavaScript framework (react, angular, vue, etc.)
            design_system: Design system (tailwind, material_ui, etc.)
            generation_type: Type of generation (wireframe_generation, app_generation, etc.)
        """
        try:
            con = await get_connection()

            # First check if the project exists in migration_schema
            project_exists_migration = await con.fetchval(
                """
                SELECT COUNT(*) FROM migration_schema.projects
                WHERE project_id = $1
                """,
                project_id,
            )

            logger.info(
                f"Project {project_id} exists in migration_schema: {project_exists_migration}"
            )

            # Check if settings already exist for this project
            existing = await con.fetchval(
                """
                SELECT COUNT(*) FROM migration_schema.project_settings
                WHERE project_id = $1
                """,
                project_id,
            )

            if existing:
                # Update existing settings
                await con.execute(
                    """
                    UPDATE migration_schema.project_settings
                    SET device = $2, framework = $3, design_system = $4, generation_type = $5
                    WHERE project_id = $1
                    """,
                    project_id,
                    device,
                    framework,
                    design_system,
                    generation_type,
                )
                logger.info(f"Updated project settings for project: {project_id}")
            else:
                # Try to directly fix the foreign key constraint
                try:
                    # First, try to alter the constraint to point to the correct schema
                    await con.execute(
                        """
                        ALTER TABLE migration_schema.project_settings
                        DROP CONSTRAINT IF EXISTS fk_project;

                        ALTER TABLE migration_schema.project_settings
                        ADD CONSTRAINT fk_project
                        FOREIGN KEY (project_id)
                        REFERENCES migration_schema.projects(project_id)
                        ON DELETE CASCADE;
                        """
                    )
                    logger.info(
                        "Fixed foreign key constraint to point to migration_schema.projects"
                    )
                except Exception as e:
                    logger.error(f"Error fixing foreign key constraint: {str(e)}")

                # Insert new settings
                try:
                    await con.execute(
                        """
                        INSERT INTO migration_schema.project_settings
                        (project_id, device, framework, design_system, generation_type)
                        VALUES ($1, $2, $3, $4, $5)
                        """,
                        project_id,
                        device,
                        framework,
                        design_system,
                        generation_type,
                    )
                    logger.info(f"Created project settings for project: {project_id}")
                except asyncpg.ForeignKeyViolationError as fk_error:
                    logger.error(
                        f"Foreign key violation when creating project settings: {str(fk_error)}"
                    )

                    # As a last resort, try to use a direct query with no constraints
                    try:
                        await con.execute(
                            """
                            SET CONSTRAINTS ALL DEFERRED;

                            INSERT INTO migration_schema.project_settings
                            (project_id, device, framework, design_system, generation_type)
                            VALUES ($1, $2, $3, $4, $5);

                            SET CONSTRAINTS ALL IMMEDIATE;
                            """,
                            project_id,
                            device,
                            framework,
                            design_system,
                            generation_type,
                        )
                        logger.info(
                            f"Created project settings with constraints deferred for project: {project_id}"
                        )
                    except Exception as e:
                        logger.error(
                            f"Final attempt to insert project settings failed: {str(e)}"
                        )
                        raise fk_error

            await con.close()

        except asyncpg.PostgresError as e:
            logger.error(f"Database error while updating project settings: {str(e)}")
            raise

    @staticmethod
    async def get_project_details(project_id: str) -> Optional[Dict[str, Any]]:
        """
        Get project details by project ID.

        Args:
            project_id: UUID of the project

        Returns:
            Dictionary containing project details or None if not found
        """
        try:
            con = await get_connection()

            row = await con.fetchrow(
                """
                SELECT * FROM migration_schema.projects
                WHERE project_id = $1
                """,
                project_id,
            )

            await con.close()

            if row:
                return dict(row)
            return None

        except asyncpg.PostgresError as e:
            logger.error(f"Database error while fetching project details: {str(e)}")
            raise

    @staticmethod
    async def get_project_settings(project_id: str) -> Dict[str, Any]:
        """
        Get project settings from the database.

        Args:
            project_id: ID of the project

        Returns:
            Dictionary containing the project settings
        """
        try:
            con = await get_connection()

            row = await con.fetchrow(
                """
                SELECT * FROM migration_schema.project_settings
                WHERE project_id = $1
                """,
                project_id,
            )

            await con.close()

            if row:
                return dict(row)
            return None

        except asyncpg.PostgresError as e:
            logger.error(f"Database error while fetching project settings: {str(e)}")
            raise

    @staticmethod
    async def create_or_update_repository_details(
        project_id: str,
        vcs_provider: str,
        clone_url: str,
        deployment_provider: str,
        deployed_url: str = None,
    ) -> None:
        """
        Create or update repository details in the database.

        Args:
            project_id: ID of the project
            vcs_provider: Version control provider (github, azure_devops, gitlab)
            clone_url: URL to clone the repository
            deployment_provider: Deployment provider (netlify, vercel, azure_static_web_apps)
            deployed_url: URL where the project is deployed (optional)
        """
        try:
            con = await get_connection()

            # Check if repository details already exist for this project
            existing = await con.fetchval(
                """
                SELECT COUNT(*) FROM migration_schema.repository_details
                WHERE project_id = $1
                """,
                project_id,
            )

            if existing:
                # Update existing repository details
                if deployed_url:
                    await con.execute(
                        """
                        UPDATE migration_schema.repository_details
                        SET vcs_provider = $2, clone_url = $3, deployment_provider = $4, deployed_url = $5
                        WHERE project_id = $1
                        """,
                        project_id,
                        vcs_provider,
                        clone_url,
                        deployment_provider,
                        deployed_url,
                    )
                else:
                    await con.execute(
                        """
                        UPDATE migration_schema.repository_details
                        SET vcs_provider = $2, clone_url = $3, deployment_provider = $4
                        WHERE project_id = $1
                        """,
                        project_id,
                        vcs_provider,
                        clone_url,
                        deployment_provider,
                    )
                logger.info(f"Updated repository details for project: {project_id}")
            else:
                # Insert new repository details
                await con.execute(
                    """
                    INSERT INTO migration_schema.repository_details
                    (project_id, vcs_provider, clone_url, deployment_provider, deployed_url)
                    VALUES ($1, $2, $3, $4, $5)
                    """,
                    project_id,
                    vcs_provider,
                    clone_url,
                    deployment_provider,
                    deployed_url,
                )
                logger.info(f"Created repository details for project: {project_id}")

            await con.close()

        except asyncpg.PostgresError as e:
            logger.error(f"Database error while updating repository details: {str(e)}")
            raise

    @staticmethod
    async def get_repository_details(project_id: str) -> Dict[str, Any]:
        """
        Get repository details from the database.

        Args:
            project_id: ID of the project

        Returns:
            Dictionary containing the repository details
        """
        try:
            con = await get_connection()

            row = await con.fetchrow(
                """
                SELECT * FROM migration_schema.repository_details
                WHERE project_id = $1
                """,
                project_id,
            )

            await con.close()

            if row:
                return dict(row)
            return None

        except asyncpg.PostgresError as e:
            logger.error(f"Database error while fetching repository details: {str(e)}")
            raise

    @staticmethod
    async def update_repository_deployed_url(
        project_id: str, deployed_url: str
    ) -> None:
        """
        Update the deployed URL for a project's repository.

        Args:
            project_id: ID of the project
            deployed_url: The live URL where the application is deployed
        """
        try:
            con = await get_connection()

            # Check if repository details exist for this project
            existing = await con.fetchval(
                """
                SELECT COUNT(*) FROM migration_schema.repository_details
                WHERE project_id = $1
                """,
                project_id,
            )

            if existing:
                # Update the deployed URL
                await con.execute(
                    """
                    UPDATE migration_schema.repository_details
                    SET deployed_url = $2
                    WHERE project_id = $1
                    """,
                    project_id,
                    deployed_url,
                )
                logger.info(
                    f"Updated deployed URL to {deployed_url} for project: {project_id}"
                )
            else:
                logger.warning(
                    f"Cannot update deployed URL: No repository details found for project {project_id}"
                )

            await con.close()

        except asyncpg.PostgresError as e:
            logger.error(f"Database error while updating deployed URL: {str(e)}")
            raise

    @staticmethod
    async def get_conversation_by_id(conversation_id: str) -> Dict[str, Any]:
        """
        Get a specific project conversation by ID.

        Args:
            conversation_id: ID of the conversation

        Returns:
            Dictionary containing the project conversation
        """
        try:
            con = await get_connection()

            row = await con.fetchrow(
                """
                SELECT * FROM migration_schema.project_conversations
                WHERE conversation_id = $1
                """,
                conversation_id,
            )

            await con.close()

            if row:
                return dict(row)
            return None

        except asyncpg.PostgresError as e:
            logger.error(f"Database error while fetching conversation: {str(e)}")
            raise

    @staticmethod
    async def create_or_update_conversation(
        project_id: str, initial_ui_metadata: dict
    ) -> str:
        """
        Create a new conversation or update an existing one for a project with initial UI metadata.

        Args:
            project_id: UUID of the project
            initial_ui_metadata: Dictionary containing UI metadata for the stepper

        Returns:
            UUID of the created or updated conversation
        """
        try:
            con = await get_connection()

            # Check if a conversation already exists for this project
            existing = await con.fetchrow(
                """
                SELECT conversation_id
                FROM migration_schema.conversations
                WHERE project_id = $1
                """,
                project_id,
            )

            if existing:
                # Update existing conversation
                conversation_id = existing["conversation_id"]
                await con.execute(
                    """
                    UPDATE migration_schema.conversations
                    SET initial_ui_metadata = $1
                    WHERE conversation_id = $2
                    """,
                    json.dumps(initial_ui_metadata),
                    conversation_id,
                )
                logger.info(f"Updated conversation for project: {project_id}")
            else:
                # Create new conversation
                conversation_id = await con.fetchval(
                    """
                    INSERT INTO migration_schema.conversations
                    (project_id, initial_ui_metadata)
                    VALUES ($1, $2)
                    RETURNING conversation_id
                    """,
                    project_id,
                    json.dumps(initial_ui_metadata),
                )
                logger.info(f"Created new conversation for project: {project_id}")

            await con.close()
            return conversation_id

        except asyncpg.PostgresError as e:
            logger.error(
                f"Database error while creating/updating conversation: {str(e)}"
            )
            raise

    @staticmethod
    async def get_conversation_by_project_id(project_id: str) -> Optional[str]:
        """
        Get the conversation ID for a project.

        Args:
            project_id: UUID of the project

        Returns:
            UUID of the conversation or None if not found
        """
        try:
            con = await get_connection()

            conversation_id = await con.fetchval(
                """
                SELECT conversation_id
                FROM migration_schema.conversations
                WHERE project_id = $1
                """,
                project_id,
            )

            await con.close()
            return conversation_id

        except asyncpg.PostgresError as e:
            logger.error(f"Database error while fetching conversation: {str(e)}")
            raise

    @staticmethod
    async def update_conversation(
        conversation_id: str, initial_ui_metadata: dict
    ) -> None:
        """
        Update an existing conversation with new initial UI metadata.

        Args:
            conversation_id: UUID of the conversation
            initial_ui_metadata: Dictionary containing new UI metadata for the stepper
        """
        try:
            con = await get_connection()

            await con.execute(
                """
                UPDATE migration_schema.conversations
                SET initial_ui_metadata = $1
                WHERE conversation_id = $2
                """,
                json.dumps(initial_ui_metadata),
                conversation_id,
            )

            await con.close()
            logger.info(f"Updated conversation with ID: {conversation_id}")

        except asyncpg.PostgresError as e:
            logger.error(f"Database error while updating conversation: {str(e)}")
            raise

    @staticmethod
    async def delete_conversation(conversation_id: str) -> None:
        """
        Delete a conversation by its ID.

        Args:
            conversation_id: UUID of the conversation
        """
        try:
            con = await get_connection()

            await con.execute(
                """
                DELETE FROM migration_schema.conversations
                WHERE conversation_id = $1
                """,
                conversation_id,
            )

            await con.close()
            logger.info(f"Deleted conversation with ID: {conversation_id}")

        except asyncpg.PostgresError as e:
            logger.error(f"Database error while deleting conversation: {str(e)}")
            raise

    @staticmethod
    async def get_conversations_by_project_id(project_id: str) -> List[Dict[str, Any]]:
        """
        Get all conversations for a project by project ID.

        Args:
            project_id: UUID of the project

        Returns:
            List of dictionaries containing the conversations
        """
        try:
            con = await get_connection()

            rows = await con.fetch(
                """
                SELECT * FROM migration_schema.conversations
                WHERE project_id = $1
                """,
                project_id,
            )

            await con.close()

            return [dict(row) for row in rows]

        except asyncpg.PostgresError as e:
            logger.error(f"Database error while fetching conversations: {str(e)}")
            raise

    @staticmethod
    async def create_conversation_message(
        conversation_id: str, message_type: str, content: str, ui_metadata: dict = None
    ) -> str:
        """
        Create a new message in a conversation.

        Args:
            conversation_id: UUID of the conversation
            message_type: Type of message ('user', 'assistant', or 'capsule')
            content: The text content of the message
            ui_metadata: Optional UI state specific to this point in the conversation

        Returns:
            UUID of the created message
        """
        try:
            con = await get_connection()
            message_id = str(uuid.uuid4())

            # Convert content to string if it's not already
            if not isinstance(content, str):
                if isinstance(content, list) or isinstance(content, dict):
                    content = json.dumps(content)
                else:
                    content = str(content)

            # Convert ui_metadata to JSON string if provided
            ui_metadata_json = json.dumps(ui_metadata) if ui_metadata else None

            await con.execute(
                """
                INSERT INTO migration_schema.messages
                (message_id, conversation_id, message_type, content, ui_metadata)
                VALUES ($1, $2, $3, $4, $5)
                """,
                message_id,
                conversation_id,
                message_type,
                content,  # Now guaranteed to be a string
                ui_metadata_json,
            )

            await con.close()
            logger.info(f"Created new message in conversation: {conversation_id}")
            return message_id

        except asyncpg.PostgresError as e:
            logger.error(f"Database error while creating message: {str(e)}")
            raise

    @staticmethod
    async def get_complete_project_data(project_id: str) -> Dict[str, Any]:
        """
        Get complete project data including basic details, settings, repository details,
        and conversation history.

        Args:
            project_id: ID of the project

        Returns:
            Dictionary containing all project data
        """
        try:
            # Fetch all project data in parallel for efficiency
            (
                project_details,
                project_settings,
                repo_details,
                conversation_id,
            ) = await asyncio.gather(
                ProjectDatabaseService.get_project_details(project_id),
                ProjectDatabaseService.get_project_settings(project_id),
                ProjectDatabaseService.get_repository_details(project_id),
                ProjectDatabaseService.get_conversations_by_project_id(project_id),
            )

            # Initialize the result dictionary with basic project details
            result = {
                "project_details": project_details,
                # "metadata": project_details["initial_ui_metadata"] or [],
                "project_settings": project_settings or {},
                "repository_details": repo_details or {},
            }

            # logger.info(f"Fetched complete project data for conversation: {conversation_id}")

            # If conversation exists, fetch conversation data
            if conversation_id:
                conversations = await ProjectDatabaseService.get_project_conversations(
                    project_id
                )
                result["conversation"] = conversations or []
                result["metadata"] = conversation_id[0]["initial_ui_metadata"]
            else:
                result["conversation"] = []
                result["metadata"] = []

            logger.info(
                f"Successfully retrieved complete data for project: {project_id}"
            )
            logger.info(
                f"Successfully retrieved complete result data for project: {result}"
            )
            return result

        except Exception as e:
            logger.error(
                f"Error retrieving complete project data: {str(e)}", exc_info=True
            )
            raise

    @staticmethod
    async def get_project_conversations(project_id: str) -> List[Dict[str, Any]]:
        """
        Get all messages for a project's conversation in ascending order by time.

        Args:
            project_id: UUID of the project

        Returns:
            List of dictionaries containing the messages in chronological order
        """
        try:
            con = await get_connection()

            # First get the conversation ID
            conversation_id = await con.fetchval(
                """
                SELECT conversation_id
                FROM migration_schema.conversations
                WHERE project_id = $1
                """,
                project_id,
            )

            if not conversation_id:
                await con.close()
                return []

            # Then get all messages for this conversation ordered by creation time
            rows = await con.fetch(
                """
                SELECT * FROM migration_schema.messages
                WHERE conversation_id = $1
                ORDER BY created_at ASC
                """,
                conversation_id,
            )

            await con.close()

            return [dict(row) for row in rows]

        except asyncpg.PostgresError as e:
            logger.error(f"Database error while fetching project messages: {str(e)}")
            raise

    @staticmethod
    async def get_or_create_user(user_email: str) -> str:
        """
        Get an existing user by email or create a new one if not found.
        Uses the migration_schema.users table.

        Args:
            user_email: The email address of the user

        Returns:
            The user ID as a string

        Raises:
            asyncpg.PostgresError: If there's a database error
        """
        try:
            con = await get_connection()

            # Try to find the user in the migration_schema
            user = await con.fetchrow(
                """
                SELECT user_id FROM migration_schema.users
                WHERE email = $1
                """,
                user_email,
            )

            if user:
                user_id = str(user["user_id"])
                logger.info(f"Found existing user with ID: {user_id}")
                await con.close()
                return user_id

            # User not found, create a new one
            user_id = str(uuid.uuid4())
            full_name = user_email.split("@")[0]  # Simple username from email

            await con.execute(
                """
                INSERT INTO migration_schema.users (user_id, email, full_name, created_at)
                VALUES ($1, $2, $3, NOW())
                """,
                user_id,
                user_email,
                full_name,
            )

            logger.info(f"Created new user with ID: {user_id}")
            await con.close()
            return user_id

        except asyncpg.PostgresError as e:
            logger.error(f"Database error while getting/creating user: {str(e)}")
            raise

    @staticmethod
    async def get_project_list(
        user_id: str, num_projects: int = None
    ) -> List[Dict[str, Any]]:
        """
        Get a list of projects for a specific user with basic details and project type.

        Args:
            user_id: ID of the user
            num_projects: Optional limit on number of projects to return

        Returns:
            List of dictionaries containing project details (id, name, description, type)
        """
        try:
            con = await get_connection()

            # Query projects with a left join to project_settings to get project type
            query = """
                SELECT p.project_id, p.project_name, p.project_description,
                       ps.generation_type, p.last_modified
                FROM migration_schema.projects p
                LEFT JOIN migration_schema.project_settings ps ON p.project_id = ps.project_id
                WHERE p.created_by = $1
                ORDER BY p.last_modified DESC
            """

            # Add LIMIT clause if num_projects is specified
            if num_projects is not None:
                query += f" LIMIT {num_projects}"

            rows = await con.fetch(query, user_id)
            await con.close()

            # Convert rows to dictionaries with consistent keys
            projects = []
            for row in rows:
                projects.append(
                    {
                        "project_id": str(row["project_id"]),
                        "project_name": row["project_name"],
                        "project_description": row["project_description"],
                        "project_type": row["generation_type"],
                        "last_modified": row["last_modified"],
                    }
                )

            return projects

        except asyncpg.PostgresError as e:
            logger.error(f"Database error while fetching projects for user: {str(e)}")
            raise
