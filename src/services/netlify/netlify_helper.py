from src.services.netlify.netlify_connector import NetlifyConnector
from src.services.azure.ad_repo_connector import AzureRepoConnector
from src.settings.settings import Settings


class NetlifyHelper:
    def __init__(self):
        self.ado_connector = AzureRepoConnector()
        self.credentials = Settings().netlify_config
        self.netlify_connector = NetlifyConnector(self.credentials)
        self.ssh_public_key = Settings().ssh_public_key
        # Initialize any necessary attributes here
        pass

    async def authorize_netlify_with_azure_devops(
        self,
        azure_org_name,
        azure_project_name,
        azure_repo_name,
        branch_name,
        build_command,
        publish_dir,
        site_name,
    ):
        self.netlify_connector = NetlifyConnector(self.credentials)
        ado_mgr = self.ado_connector

        # 1. Azure DevOps: Get Project and Repo IDs
        ado_project_id = ado_mgr.get_project_id(azure_project_name)
        if not ado_project_id:
            print("Workflow: Failed to get Azure DevOps Project ID. Exiting.")
            return

        ado_repo_id = ado_mgr.get_repo_id(
            project_id=ado_project_id, repo_name_or_id=azure_repo_name
        )
        if not ado_repo_id:
            print("Workflow: Failed to get Azure DevOps Repository ID. Exiting.")
            return

        # 2. Netlify: Create Deploy Key instance
        netlify_deploy_key_id = await self.netlify_connector.create_deploy_key(
            self.ssh_public_key
        )
        if not netlify_deploy_key_id:
            print("Workflow: Failed to create Netlify Deploy Key. Exiting.")
            return

        # 3. Netlify: Create/Configure Site
        # Ensure unique site name for Netlify

        netlify_site_id = await self.netlify_connector.create_or_update_site_with_ssh(
            netlify_site_name=site_name,
            azure_org_name=azure_org_name,
            azure_project_name=azure_project_name,  # Used for SSH URL construction
            azure_repo_name=azure_repo_name,  # Used for SSH URL construction
            branch_name=branch_name,
            build_command=build_command,
            publish_dir=publish_dir,
            netlify_deploy_key_id=netlify_deploy_key_id,
        )

        return netlify_site_id

    async def create_netlify_webhooks(
        self, netlify_site_id, azure_repo_name, ado_project_id, ado_repo_id, branch_name
    ):
        # 4. Netlify: Create Build Hook
        hook_title = f"ADO Trigger for {azure_repo_name} on {branch_name}"
        netlify_hook_url = await self.netlify_connector.create_build_hook(
            netlify_site_id, branch_name, hook_title
        )
        if not netlify_hook_url:
            print("Workflow: Failed to create Netlify Build Hook. Exiting.")

            return

        # 5. Azure DevOps: Create Service Hook
        success = await self.ado_connector.create_service_hook(
            project_id=ado_project_id,
            repo_id=ado_repo_id,
            branch_name=branch_name,
            netlify_hook_url=netlify_hook_url,
        )

        if success:
            print(
                f"--- Workflow Succeeded: Setup for '{azure_repo_name}' on Netlify site (ID: {netlify_site_id}) ---"
            )
        else:
            print(
                f"--- Workflow Failed: Setup for '{azure_repo_name}' encountered issues. Review logs. ---"
            )
