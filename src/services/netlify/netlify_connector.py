import asyncio
import json
import time
from typing import List
from fastapi import <PERSON><PERSON>P<PERSON>x<PERSON>
from src.utils.logger import AppLogger


from src.settings.settings import NetlifyConfig
from src.utils.api_helper.api_client import APIClient

logger = AppLogger(__name__).get_logger()


class NetlifyConnector:
    def __init__(self, credentials: NetlifyConfig):
        self.auth_token = credentials.NETLIFY_AUTH_TOKEN
        self._base_url = credentials.NETLIFY_BASE_URL
        self.api_client = APIClient(
            self._base_url,
            headers={
                "Authorization": f"Bearer {self.auth_token}",
                "Content-Type": "application/json",
            },
        )

    async def create_netlify_site(self, desired_site_name=None):
        print("--- Creating Netlify Site ---")

        payload = {}
        if desired_site_name:
            payload["name"] = desired_site_name

        try:
            response = await self.api_client.post("api/v1/sites", payload)
            site_data = response
            print(
                f"Netlify site '{site_data.get('name', 'N/A')}' created. Site ID: {site_data.get('site_id', 'N/A')}, URL: {site_data.get('ssl_url') or site_data.get('url', 'N/A')}"
            )
            return site_data
        except HTTPException as e:  # More specific exception for httpx
            print(f"Error creating Netlify site: {e.status_code} - {e.detail}")
            if e.status_code == 422 and desired_site_name:
                print(
                    f"The Netlify site name/subdomain '{desired_site_name}' might be taken. Try without a specific name or a different one."
                )
        except Exception as e:
            print(f"An unexpected error occurred while creating Netlify site: {e}")
        return None

    async def link_azure_repo_to_site(
        self,
        site_id: str,
        azure_org_name: str,
        azure_project_name: str,
        azure_repo_name: str,
        branch_name: str,
        build_command: str,
        publish_dir: str,
        is_private: bool = True,
    ):
        """
        Links an Azure DevOps repository to an existing Netlify site.
        """
        print(f"--- Linking Azure Repo to Netlify Site ID: {site_id} ---")

        repo_identifier = (
            f"{azure_org_name}/{azure_project_name}/_git/{azure_repo_name}"
        )

        payload = {
            "repo": {
                "provider": "azure_devops",
                "id": repo_identifier,
                "repo_path": repo_identifier,  # For Azure, id and repo_path are often the same
                "private": is_private,
                "branch": branch_name,
                "cmd": build_command,
                "dir": publish_dir,
                # "installation_id" is typically not required for Azure DevOps if the
                # Netlify account has already been authorized with Azure DevOps via the UI.
                # Netlify infers it. If issues arise, this might need to be investigated.
                "installation_id": None,
            }
        }

        endpoint = f"api/v1/sites/{site_id}"

        try:
            response = await self.api_client.put(endpoint, payload)
            site_data = response
            print(
                f"Successfully linked Azure repo '{repo_identifier}' to site ID '{site_id}'."
            )
            print(
                f"Site Name: {site_data.get('name')}, Build command: {site_data.get('build_settings', {}).get('cmd')}"
            )
            return site_data
        except HTTPException as e:  # More specific exception for httpx
            print(f"Error linking Azure repo: {e.status_code} - {e.detail}")
            if e.status_code == 404:
                print(
                    f"Site with ID '{site_id}' not found, or Netlify cannot access the specified Azure repo."
                )
            elif e.status_code == 401:
                print("Unauthorized. Check your Netlify Personal Access Token.")
        except Exception as e:
            print(f"An unexpected error occurred while linking Azure repo: {e}")
        return None

    async def create_deploy_key(self, public_key_content: str) -> str | None:
        """Registers the public SSH key with Netlify and returns the deploy_key_id."""
        print("Netlify: Creating Deploy Key instance...")
        payload = {"public_key": public_key_content}
        try:
            response = await self.api_client.post("api/v1/deploy_keys", payload)
            deploy_key_id = response["id"]
            print(f"Netlify: Deploy Key ID created: {deploy_key_id}")
            return deploy_key_id
        except HTTPException as e:
            print(f"Netlify Error (create_deploy_key): {e.status_code} - {e.detail}")
        except Exception as e:
            print(f"Netlify Unexpected Error (create_deploy_key): {e}")
        return None

    async def get_netlify_site_id(self, site_identifier, identifier_type="name"):
        """
        Fetches the Netlify Site ID for a given site identifier.

        Args:
            site_identifier (str): The name of the site (e.g., "my-cool-site")
                                or a custom domain (e.g., "www.example.com").
            identifier_type (str): "name" or "custom_domain" or "default_domain".
                                Specifies what `site_identifier` refers to.

        Returns:
            str: The Site ID if found, otherwise None.
        """
        try:
            response = await self.api_client.get("api/v1/sites")
        except HTTPException as e:
            print(f"Error fetching sites from Netlify API: {e}")
            if response is not None:
                logger.info(f"Response status: {response.status_code}")
                logger.info(f"Response content: {response.text}")
            return None

        for site in response:
            if identifier_type == "name":
                if site.get("name") == site_identifier:
                    return site.get("id")  # This is the Site ID
            elif identifier_type == "custom_domain":
                custom_domains = site.get("custom_domains", [])
                for domain_info in custom_domains:
                    if domain_info.get("name") == site_identifier:
                        return site.get("id")
            elif identifier_type == "default_domain":  # e.g., "my-site.netlify.app"
                if site.get("default_domain") == site_identifier:
                    return site.get("id")
            else:
                logger.error(
                    f"Error: Invalid identifier_type '{identifier_type}'. Use 'name', 'custom_domain', or 'default_domain'."
                )
                return None

        logger.warning(f"Site with {identifier_type} '{site_identifier}' not found.")
        return None

    async def create_or_update_site_with_ssh(
        self,
        netlify_site_name: str,
        azure_org_name: str,
        azure_project_name: str,
        azure_repo_name: str,
        branch_name: str,
        build_command: str,
        publish_dir: str,
        netlify_deploy_key_id: str,
    ) -> str | None:
        """Creates a new Netlify site or updates an existing one to use Azure DevOps SSH."""

        azure_repo_ssh_url = f"*********************:v3/{azure_org_name}/{azure_project_name}/{azure_repo_name}"
        azure_repo_ssh_url = f"https://<EMAIL>/ascendionava/{azure_org_name}/{azure_project_name}/_git/{azure_repo_name}"
        print(f"Netlify: Configuring site to use SSH URL: {azure_repo_ssh_url}")

        # --- Generate a unique site name (same as before) ---
        repo_slug = azure_repo_name.lower().replace("_", "-").replace(" ", "-")
        timestamp_suffix = str(int(time.time()))[-6:]
        base_name = f"{netlify_site_name}-{repo_slug}"
        max_base_len = 60 - len(timestamp_suffix) - 1
        final_site_name = f"{base_name[:max_base_len]}-{timestamp_suffix}"
        # --- End unique site name generation ---

        # repo_config = {
        #     "provider": "manual",  # <--- CHANGE THIS
        #     "repo_url": azure_repo_ssh_url, # <--- USE repo_url for "manual" provider
        #     # "id": azure_repo_ssh_url, # 'id' might not be used or expected for 'manual'
        #     # "repo_path": azure_repo_ssh_url, # 'repo_path' might not be used for 'manual'
        #     "private": True,
        #     "branch": branch_name,
        #     "cmd": build_command,
        #     "dir": publish_dir,
        #     "deploy_key_id": netlify_deploy_key_id # Still the problematic part
        # }

        repo_config = {
            "provider": "manual",  # <--- CHANGE THIS BACK
            "id": azure_repo_ssh_url,  # <--- Use SSH URL for 'id'
            "repo_path": azure_repo_ssh_url,  # <--- Use SSH URL for 'repo_path'
            "repo_url": azure_repo_ssh_url,  # 'repo_url' is typically for "manual"
            "public_repo": False,
            "repo_branch": branch_name,
            "cmd": build_command,
            "dir": publish_dir,
            "deploy_key_id": netlify_deploy_key_id,
        }
        site_id = None
        try:
            # Attempt to create a new site
            print(f"Netlify: Creating new site: {netlify_site_name}...")
            create_payload = {
                "name": final_site_name,
                "repo": repo_config,
                "build_settings": {
                    # If cmd and dir are not in repo_config, they might go here:
                    # "cmd": build_command,
                    # "dir": publish_dir,
                    "env": {
                        "GIT_SSH_COMMAND": "ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null"
                        # You can add other build-time environment variables here too
                        # "NODE_VERSION": "18"
                    }
                },
                # Add "account_slug" if needed for specific Netlify team/account
            }
            print(
                f"Netlify: Attempting to create site with payload: {json.dumps(create_payload, indent=2)}"
            )
            response = await self.api_client.post("api/v1/sites", create_payload)

            site_data = response
            site_id = site_data["id"]
            print(
                f"Netlify: Site created/configured: ID {site_id}, Name: {site_data.get('name')}"
            )
            return site_id
        except HTTPException as e:
            print(
                f"Netlify Error (create_or_update_site_with_ssh): {e.status_code} - {e.detail}"
            )
        except Exception as e:
            print(f"Netlify Unexpected Error (create_or_update_site_with_ssh): {e}")
        return site_id

    async def create_build_hook(
        self, site_id: str, branch_name: str, title: str
    ) -> str | None:
        """Creates a Netlify build hook."""
        print(
            f"Netlify: Creating Build Hook for site ID {site_id} on branch {branch_name}..."
        )
        payload = {"title": title, "branch": branch_name}
        try:
            response = await self.api_client.post(
                f"api/v1/sites/{site_id}/build_hooks", payload
            )
            hook_data = response
            print(f"Netlify: Build Hook created: URL {hook_data['url']}")
            return hook_data["url"]
        except HTTPException as e:
            print(f"Netlify Error (create_build_hook): {e.status_code} - {e.detail}")
        except Exception as e:
            print(f"Netlify Unexpected Error (create_build_hook): {e}")
        return None

    async def get_deploy(self, deploy_id: str) -> dict | None:
        """Fetches a specific deploy by its ID."""
        deploy_url = f"api/v1/deploys/{deploy_id}"
        try:
            response = await self.api_client.get(deploy_url)
            return response
        except HTTPException as http_err:
            print(
                f"Netlify: HTTP error fetching deploy {deploy_id}: {http_err} - {http_err.detail}"
            )
            raise HTTPException(500, {"message": "Error fetching deployment details"})
        except Exception as e:
            print(f"Netlify: Request error fetching deploy {deploy_id}: {e}")
            raise HTTPException(500, {"message": "Error fetching deployment details"})
        return None

    async def get_latest_site_deploy(self, site_id: str) -> dict | None:
        """Fetches the most recent deploy for a given site."""
        deploys_url = f"api/v1/sites/{site_id}/deploys"
        try:
            response = await self.api_client.get(deploys_url)
            deploys = response
            if deploys:  # The first deploy in the list is usually the latest
                return deploys[0]
            else:
                print(f"Netlify: No deploys found for site {site_id}.")
        except HTTPException as http_err:
            logger.info(
                f"Netlify: HTTP error fetching deploys for site {site_id}: {http_err} - {http_err.detail}"
            )
            raise HTTPException(500, {"message": "Error fetching deployment details"})
        except Exception as e:
            print(f"Netlify: Request error fetching deploys for site {site_id}: {e}")
        return None

    # async def poll_deployment_status(
    #     self,
    #     site_id: str,
    #     deploy_id: str = None,
    #     callback: any = None,
    #     timeout_seconds: int = 600,  # 10 minutes timeout
    #     poll_interval_seconds: int = 20,  # Check every 20 seconds
    # ) -> dict | None:
    #     """
    #     Polls Netlify for the deployment status of a specific deploy or the latest deploy for a site.

    #     Args:
    #         site_id (str): The ID of the Netlify site.
    #         deploy_id (str, optional): The specific deploy ID to poll.
    #                                    If None, polls the latest deploy for the site.
    #         timeout_seconds (int): Maximum time to wait for the deploy to complete.
    #         poll_interval_seconds (int): How often to check the status.

    #     Returns:
    #         dict | None: The final deploy object if the deploy completes (ready or error)
    #                      within the timeout, or None if it times out or an initial error occurs.
    #     """
    #     start_time = time.time()
    #     target_deploy_id = deploy_id

    #     print(
    #         f"Netlify Polling: Starting for site_id='{site_id}'. Timeout: {timeout_seconds}s, Interval: {poll_interval_seconds}s."
    #     )

    #     if not target_deploy_id:
    #         print(
    #             f"Netlify Polling: No specific deploy_id provided, attempting to fetch the latest deploy for site '{site_id}'."
    #         )
    #         # Allow some time for the deploy to be initiated if we're polling immediately after site creation
    #         await asyncio.sleep(5)  # Small initial delay
    #         latest_deploy = await self.get_latest_site_deploy(site_id)
    #         if not latest_deploy or not latest_deploy.get("id"):
    #             print(
    #                 f"Netlify Polling: Could not fetch the latest deploy for site '{site_id}'. Aborting poll."
    #             )
    #             return None
    #         target_deploy_id = latest_deploy["id"]
    #         print(f"Netlify Polling: Monitoring latest deploy_id='{target_deploy_id}'.")
    #     else:
    #         print(
    #             f"Netlify Polling: Monitoring specific deploy_id='{target_deploy_id}'."
    #         )

    #     while True:
    #         current_time = time.time()
    #         elapsed_time = current_time - start_time

    #         if elapsed_time > timeout_seconds:
    #             print(
    #                 f"Netlify Polling: Timeout exceeded for deploy_id='{target_deploy_id}'. Waited {elapsed_time:.2f} seconds."
    #             )
    #             return {
    #                 "id": target_deploy_id,
    #                 "state": "timeout",
    #                 "error_message": "Polling timed out",
    #             }

    #         deploy_status = await self.get_deploy(target_deploy_id)

    #         if not deploy_status:
    #             # Error fetching deploy status, likely logged by get_deploy
    #             print(
    #                 f"Netlify Polling: Failed to retrieve status for deploy_id='{target_deploy_id}'. Aborting poll."
    #             )
    #             return {
    #                 "id": target_deploy_id,
    #                 "state": "fetch_error",
    #                 "error_message": "Could not retrieve deploy status",
    #             }

    #         state = deploy_status.get("state")
    #         context = deploy_status.get(
    #             "context"
    #         )  # e.g., "production", "deploy-preview"
    #         print(
    #             f"Netlify Polling: Deploy_id='{target_deploy_id}', State='{state}', Context='{context}', Elapsed='{elapsed_time:.0f}s'"
    #         )

    #         if state == "ready":
    #             print(
    #                 f"Netlify Polling: Deploy_id='{target_deploy_id}' succeeded! State: '{state}'."
    #             )
    #             return deploy_status
    #         elif state == "error":
    #             error_message = deploy_status.get(
    #                 "error_message", "No specific error message provided by Netlify."
    #             )
    #             print(
    #                 f"Netlify Polling: Deploy_id='{target_deploy_id}' failed. State: '{state}'. Error: {error_message}"
    #             )
    #             # You might want to look at deploy_status.get('build_log_url') or similar if available
    #             return deploy_status
    #         elif state in [
    #             "building",
    #             "enqueued",
    #             "processing",
    #             "new",
    #             "prepared",
    #         ]:  # Common pending states
    #             # Continue polling
    #             pass
    #         else:
    #             print(
    #                 f"Netlify Polling: Deploy_id='{target_deploy_id}', encountered unexpected state: '{state}'. Continuing to poll."
    #             )
    #             raise HTTPException(
    #                 status_code=500,
    #                 detail="Something went wrong during netlify deploy phase",
    #             )
    #             # Continue polling, but this might be an edge case or new state.

    #         await asyncio.sleep(poll_interval_seconds)

    async def get_build_log(self, build_id: str) -> str | None:
        """
        Fetches the raw build log for a given build_id.
        The API endpoint is /builds/{build_id}/log
        """
        if not build_id:
            return None
        url = f"builds/{build_id}/log"
        response = await self.api_client.get(url)

        return response.text

    def parse_error_from_log(
        self, log_content: str, num_lines_context: int = 15
    ) -> str:
        """
        Tries to extract relevant error messages from a build log.
        This is heuristic and might need adjustments based on common build errors.
        """
        if not log_content:
            return "Log content was empty."

        lines = log_content.splitlines()
        error_keywords = [
            "error:",
            "err!",
            "failed",
            "failure",
            "exception",
            "build failed",
            "command failed",
            "exit status",
            "not found",
        ]  # Case-insensitive

        # Look for Netlify's "Build failed" summary first
        netlify_failure_summary_start = -1
        netlify_failure_summary_end = -1

        for i, line in enumerate(lines):
            if "Build failed with exit code" in line or "Build script failed" in line:
                netlify_failure_summary_start = max(0, i - 2)  # a bit of context before
                # Try to find the end of this summary block or just take a few lines
                for j in range(i, min(len(lines), i + 10)):
                    if (
                        lines[j].strip() == ""
                        or "Finished" in lines[j]
                        or "Cleaning up" in lines[j]
                    ):
                        netlify_failure_summary_end = j
                        break
                else:
                    netlify_failure_summary_end = min(len(lines), i + 5)
                break

        if netlify_failure_summary_start != -1:
            return "\n".join(
                lines[netlify_failure_summary_start:netlify_failure_summary_end]
            )

        # If Netlify summary not found, look for general error keywords
        error_lines_indices: List[int] = []
        for i, line in enumerate(lines):
            for keyword in error_keywords:
                if keyword in line.lower():
                    error_lines_indices.append(i)
                    break  # Found a keyword in this line, move to next line

        if error_lines_indices:
            # Extract snippets around the first few error occurrences
            extracted_errors = []
            # Get context around the first error, and maybe the last if different
            first_error_idx = error_lines_indices[0]
            start = max(0, first_error_idx - num_lines_context // 2)
            end = min(len(lines), first_error_idx + num_lines_context // 2 + 1)
            extracted_errors.append(
                f"... (around line {first_error_idx + 1}) ...\n"
                + "\n".join(lines[start:end])
            )

            if len(error_lines_indices) > 1:
                last_error_idx = error_lines_indices[-1]
                if (
                    last_error_idx > first_error_idx + num_lines_context
                ):  # if it's a distinct error
                    start = max(0, last_error_idx - num_lines_context // 2)
                    end = min(len(lines), last_error_idx + num_lines_context // 2 + 1)
                    extracted_errors.append(
                        f"... (around line {last_error_idx + 1}) ...\n"
                        + "\n".join(lines[start:end])
                    )

            return "\n\n".join(extracted_errors)

        # If no specific error keywords found, return the last N lines as they often contain the final error
        return "No specific error keywords found in log. Last lines:\n" + "\n".join(
            lines[-num_lines_context:]
        )

    async def poll_deployment_status(
        self,
        site_id: str,
        deploy_id: str | None = None,  # Python 3.9+ for Optional
        # callback: Any = None, # 'Any' is fine, or be more specific if you know the signature
        timeout_seconds: int = 600,
        poll_interval_seconds: int = 20,
    ):
        start_time = time.time()
        target_deploy_id = deploy_id

        print(
            f"Netlify Polling: Starting for site_id='{site_id}'. Timeout: {timeout_seconds}s, Interval: {poll_interval_seconds}s."
        )

        if not target_deploy_id:
            print(
                f"Netlify Polling: No specific deploy_id provided, attempting to fetch the latest deploy for site '{site_id}'."
            )
            await asyncio.sleep(5)
            latest_deploy = await self.get_latest_site_deploy(site_id)
            if not latest_deploy or not latest_deploy.get("id"):
                print(
                    f"Netlify Polling: Could not fetch the latest deploy for site '{site_id}'. Aborting poll."
                )
                return {
                    "id": "unknown",
                    "site_id": site_id,
                    "state": "fetch_error",
                    "error_message": "Could not fetch the latest deploy to monitor.",
                    "detailed_error_message": "Could not determine the deploy ID to monitor after site creation.",
                }
            target_deploy_id = latest_deploy["id"]
            print(f"Netlify Polling: Monitoring latest deploy_id='{target_deploy_id}'.")
        else:
            print(
                f"Netlify Polling: Monitoring specific deploy_id='{target_deploy_id}'."
            )

        while True:
            current_time = time.time()
            elapsed_time = current_time - start_time

            if elapsed_time > timeout_seconds:
                print(
                    f"Netlify Polling: Timeout exceeded for deploy_id='{target_deploy_id}'. Waited {elapsed_time:.2f} seconds."
                )
                return {
                    "id": target_deploy_id,
                    "site_id": site_id,
                    "state": "timeout",
                    "error_message": "Polling timed out waiting for deployment completion.",
                    "detailed_error_message": f"Deployment did not complete (reach 'ready' or 'error' state) within {timeout_seconds} seconds.",
                }

            deploy_status = await self.get_deploy(target_deploy_id)

            if not deploy_status:
                print(
                    f"Netlify Polling: Failed to retrieve status for deploy_id='{target_deploy_id}'. Aborting poll."
                )
                return {
                    "id": target_deploy_id,
                    "site_id": site_id,
                    "state": "fetch_error",
                    "error_message": "Could not retrieve deploy status from Netlify API.",
                    "detailed_error_message": "An error occurred while trying to fetch the current status of the deploy.",
                }

            state = deploy_status.get("state")
            context = deploy_status.get("context")
            print(
                f"Netlify Polling: Deploy_id='{target_deploy_id}', State='{state}', Context='{context}', Elapsed='{elapsed_time:.0f}s'"
            )

            if state == "ready":
                print(
                    f"Netlify Polling: Deploy_id='{target_deploy_id}' succeeded! State: '{state}'."
                )
                return deploy_status  # Contains all deploy info
            elif state == "error":
                error_message_summary = deploy_status.get(
                    "error_message", "No summary error message provided."
                )
                print(
                    f"Netlify Polling: Deploy_id='{target_deploy_id}' failed. State: '{state}'. Summary: {error_message_summary}"
                )

                # build_id = deploy_status.get("build_id")
                # detailed_error_message = (
                #     "Could not retrieve build log or build_id was missing."
                # )
                # if build_id:
                #     print(
                #         f"Netlify Polling: Fetching build log for build_id='{build_id}'..."
                #     )
                #     log_content = await self.get_build_log(build_id)
                #     if log_content:
                #         detailed_error_message = self.parse_error_from_log(log_content)
                #         print(
                #             f"Netlify Polling: Detailed error from log:\n{detailed_error_message}"
                #         )
                #     else:
                #         detailed_error_message = (
                #             "Build log was empty or could not be fetched."
                #         )
                # else:
                #     print(
                #         f"Netlify Polling: No build_id found in deploy status for deploy_id='{target_deploy_id}'. Cannot fetch detailed log."
                #     )

                # deploy_status["detailed_error_message"] = detailed_error_message
                return deploy_status  # Return the deploy status dictionary, now augmented with detailed_error_message

            elif state in [
                "building",
                "enqueued",
                "processing",
                "new",
                "prepared",  # Netlify added this state
            ]:
                pass  # Continue polling
            else:
                # This case might need careful handling.
                # For now, we'll assume it's an unrecoverable or unexpected state from Netlify's side for this polling logic.
                print(
                    f"Netlify Polling: Deploy_id='{target_deploy_id}', encountered UNEXPECTED state: '{state}'. This indicates a problem or a new Netlify state not handled."
                )
                # You might want to return an error here instead of raising HTTPException directly from a library function
                # The HTTPException was from your original code, if this is a library, returning a dict is better.
                deploy_status["detailed_error_message"] = (
                    f"Polling stopped due to unexpected state: {state}. Review Netlify logs."
                )
                # If you MUST raise, ensure it's appropriate for the caller.
                # raise HTTPException(
                #     status_code=500,
                #     detail=f"Netlify deploy {target_deploy_id} entered unexpected state: {state}",
                # )
                return (
                    deploy_status  # Return the status with the unexpected state noted.
                )

            await asyncio.sleep(poll_interval_seconds)

    # def _get_github_installation_id(self, github_repo_owner: str) -> int | None:
    #     # ... (same as before) ...
    #     print(f"Netlify: Fetching GitHub installations for Netlify account...")
    #     try:
    #         response = requests.get(f"{self.NETLIFY_API_BASE}/github/installations", headers=self.headers)
    #         response.raise_for_status()
    #         installations = response.json()
    #         for inst in installations:
    #             if inst.get("account", {}).get("login", "").lower() == github_repo_owner.lower():
    #                 print(f"Netlify: Found matching GitHub installation ID: {inst['id']} for owner '{github_repo_owner}'")
    #                 return inst["id"]
    #         print(f"Netlify: WARN - No specific GitHub installation found for owner '{github_repo_owner}'.")
    #         if installations and len(installations) == 1:
    #              print(f"Netlify: Using the only available installation ID: {installations[0]['id']}")
    #              return installations[0]['id']
    #         return None
    #     except requests.exceptions.HTTPError as http_err:
    #         print(f"Netlify: HTTP error fetching GitHub installations: {http_err} - {http_err.response.text}")
    #         return None
    #     except requests.exceptions.RequestException as req_err:
    #         print(f"Netlify: Request error fetching GitHub installations: {req_err}")
    #         return None

    async def create_site_and_link_repo(
        self,
        github_repo_full_name: str,
        github_repo_id: int,
        github_default_branch: str,
        is_private_github_repo: bool,
        build_command: str,
        publish_directory: str,
        env_vars: dict = None,
        custom_site_name: str = None,
        target_team_slug: str = None,  # New: To specify team via body
        github_repo_owner_for_installation_lookup: str = None,
        installation_id: str = None,
    ) -> dict | None:
        if not github_repo_owner_for_installation_lookup:
            github_repo_owner_for_installation_lookup = github_repo_full_name.split(
                "/"
            )[0]

        site_payload = {
            "repo": {
                "provider": "github",
                "repo": github_repo_full_name,
                "repo_id": github_repo_id,
                "private": is_private_github_repo,
                "branch": github_default_branch,
                "cmd": build_command,
                "dir": publish_directory,
                "allowed_branches": [github_default_branch],
            },
            "build_settings": {
                "cmd": build_command,
                "dir": publish_directory,
            },
        }
        if installation_id:
            site_payload["repo"]["installation_id"] = installation_id
        if custom_site_name:
            site_payload["name"] = custom_site_name.lower().replace(" ", "-")
        if env_vars:
            site_payload["build_settings"]["env"] = env_vars

        create_site_url = "/api/v1/sites"
        if target_team_slug:
            site_payload["account_slug"] = target_team_slug
            print(
                f"Netlify: Using general /sites endpoint, targeting team slug in body: {target_team_slug}"
            )
        else:
            print(
                "Netlify: Using general /sites endpoint. Site will be created under PAT owner's default account."
            )

        print(
            f"Netlify: Attempting to create site with payload: {json.dumps(site_payload, indent=2)}"
        )
        print(f"Netlify: API Endpoint URL: {create_site_url}")

        try:
            logger.info(f"API key check: {self.auth_token}")
            response = await self.api_client.post(create_site_url, site_payload)
            site_data = response
            # ... (rest of success handling as before) ...
            print(
                f"Netlify: Successfully created site: {site_data.get('name')} ({site_data.get('site_id')})"
            )
            print(
                f"Netlify: Default URL: {site_data.get('ssl_url') or site_data.get('url')}"
            )
            print(f"Netlify: Admin URL: {site_data.get('admin_url')}")
            return site_data
        except HTTPException as http_err:
            # ... (error handling as before) ...
            print(f"Netlify: HTTP error creating site: {http_err} - {http_err.detail}")
            raise
        except Exception as req_err:
            print(f"Netlify: Request error creating site: {req_err}")
            raise HTTPException(
                500, details="Something went wrong with linking provider and github"
            )

    async def get_site_deploy_status(self, site_id: str, deploy_id: str = None):
        # ... (same as before) ...
        if deploy_id:
            deploy_url = f"api/v1/deploys/{deploy_id}"
        else:
            deploys_url = f"api/v1/sites/{site_id}/deploys"
            try:
                response = await self.api_client.get(deploys_url)
                deploys = response
                if not deploys:
                    print(f"Netlify: No deploys found yet for site {site_id}.")
                    return None
                deploy_id = deploys[0]["id"]
                deploy_url = f"/api/v1/deploys/{deploy_id}"
                print(
                    f"Netlify: Monitoring deploy_id: {deploy_id} for site_id: {site_id}"
                )
            except Exception as e:
                print(f"Netlify: Error fetching deploys for site {site_id}: {e}")
                return None
        try:
            response = await self.api_client.get(deploy_url)
            deploy_data = response
            print(
                f"Netlify: Deploy '{deploy_id}' status: {deploy_data.get('state')}, Context: {deploy_data.get('context')}"
            )
            return deploy_data
        except Exception as e:
            print(f"Netlify: Error fetching deploy status for {deploy_id}: {e}")
            return None


# --- Example Usage ---
# async def main():
# --- Configuration ---

#     azure_config = AzureADOConfig()
#     # Azure DevOps Repository Details
#     AZURE_ORG = azure_config.ADO_ORG
#     AZURE_PROJECT = azure_config.ADO_PROJECT
#     AZURE_REPO = "auto_deploy_project_cli_4"
#     DEFAULT_BRANCH = "main" # or "master", etc.
#     BUILD_CMD = "npm run build" # or "yarn build", "hugo", etc.
#     PUBLISH_DIRECTORY = "dist" # or "public", "build", "_site", etc.


#     arc = AdoHelper()
#     new_repo = await arc.create_repository(AZURE_REPO, True, 'React_Tailwindcss_Template')

#     # print("Newly created Repo: ", new_repo)
#     netlify_credentials = NetlifyConfig()
#     connector = NetlifyConnector(credentials=netlify_credentials)
#     helper = NetlifyHelper()
#     site_data = await connector.create_netlify_site('auto_deploy_app_cli_4')

#     # --- Link Repository ---
#     # Ensure your Netlify account has been connected to Azure DevOps through the Netlify UI first.
#     # This establishes the necessary OAuth connection.
#     print("\nAttempting to link Azure repository...")
#     # await asyncio.sleep(20)
#     site_id = await helper.authorize_netlify_with_azure_devops(
#         azure_org_name=AZURE_ORG,
#         azure_project_name=AZURE_PROJECT,
#         azure_repo_name=AZURE_REPO,
#         branch_name=DEFAULT_BRANCH,
#         build_command=BUILD_CMD,
#         publish_dir=PUBLISH_DIRECTORY,
#         site_name=site_data.get('name', 'N/A'))

#     await helper.create_netlify_webhooks(
#         netlify_site_id=site_id,
#         azure_repo_name=AZURE_REPO,
#         ado_project_id=arc.get_project_id(AZURE_PROJECT),
#         ado_repo_id=arc.get_repo_id(AZURE_REPO),
#         branch_name=DEFAULT_BRANCH
#     )

#     if site_id:
#         print("\n--- Updated Site ID ---", site_id)

#     else:
#         print("\nFailed to link repository.")

# if __name__ == "__main__":
#     asyncio.run(main())
