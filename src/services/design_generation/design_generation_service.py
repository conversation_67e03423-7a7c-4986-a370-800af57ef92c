import json
import re
import textwrap
from itertools import islice
from typing import Dict, List, Union, Any, Tuple, Optional

from src.model.request_schemas import DARequest
from src.services.generation.avaplus_generation import AVAPlusGeneration
from src.settings.settings import DACreds
from src.utils.logger import AppLogger

logger = AppLogger("Design Generation Service").get_logger()


class DesignGenerationService:
    def __init__(self, provider: AVAPlusGeneration, credentials: DACreds):
        self.credentials = credentials
        self.provider = provider

    async def EE_MLO_PROMPT_ENHANCER(self, request: dict) -> str:
        """s
        Enhance the user-provided prompt using the Prompt Enhancer API.

        This function sends a user_prompt to the Prompt Enhancer API with the provided user input
        and additional context such as platform, framework, design library, and design style.
        It returns the enhanced prompt as a string.

        Args:
            request (dict): A dictionary containing the following keys:
                - user_input (str): The user's input prompt.
                - platform (str): The platform for which the design is being generated.
                - framework (str): The framework being used.
                - design_library (str): The design library to be used.

        Returns:
            str: The enhanced prompt returned by the API.

        Raises:
            Exception: If an error occurs during the API call.
        """
        logger.info("---- STEP 1: Calling The Prompt Enhancer\n")
        try:
            # Append additional context from the schema
            # payload_prompt = f"""
            #     user_input: {user_prompt["user_input"]}\n
            #     Platform: {user_prompt["platform"]}\n
            #     Framework: {user_prompt["framework"]}\n
            #     Design Library: {user_prompt["design_library"]}\n
            #     Design Style: {user_prompt["design_style"]}"
            # """

            payload_prompt = f"""
                user_input: {request["requirements"]}\n
            """

            payload = DARequest(
                mode="EE_MLO_NEW_P2A_PROMPT_ENHANCER",
                promptOverride=False,
                userSignature=request["userSignature"],
                prompt=payload_prompt,
                image=None,
                useCaseIdentifier="EE_MLO_NEW_P2A_PROMPT_ENHANCER" + self.credentials.DA_USECASE_IDENTIFIER,
            )

            da_response = await self.provider.generate(payload=payload.model_dump())
            return da_response

        except Exception as e:
            logger.error(f"Error calling EE_MLO_NEW_P2A_PROMPT_ENHANCER: {e}", exc_info=True)
            return None

    async def generate_project_brief(self, request: dict) -> dict:
        try:

            payload = DARequest(
                mode="EE_MLO_RCG_INTRO",
                promptOverride=False,
                userSignature=request["userSignature"],
                image=None,
                prompt=request["project_description"],
                useCaseIdentifier=f"EE_MLO_RCG_INTRO{self.credentials.DA_USECASE_IDENTIFIER}",
            )
            data = await self.provider.generate(
                payload=payload.model_dump(), extract_markdown="json"
            )
            return await self.provider.handle_json(data)
        except Exception as e:
            logger.error(f"Unexpected error : {e}")

    async def EE_MLO_CSS_CONFIG_GENERATOR(self, visual_interaction_data: str) -> str:
        """
        Generate css and config file using the visual-interaction-design tag from the Enhanced Prompt Agent API.

        This function sends a request to the CSS Generator Agent API with the provided
        user prompt and returns a css file as an output in str format.

        Args:
            request (str): The enhanced prompt to be sent to the API.

        Returns:
            str: A CSS and config file returned by the API.

        Raises:
            Exception: If an error occurs during the API call.
        """
        logger.info("---- STEP 2: Calling The CSS and CONFIG Generator Agent\n")
        try:
            payload = DARequest(
                mode="EE_MLO_NEW_P2A_CSS_CONFIG",
                promptOverride=False,
                userSignature=self.credentials.DA_USER_SIGNATURE,
                prompt=json.dumps(visual_interaction_data),
                image=None,
                useCaseIdentifier="EE_MLO_NEW_P2A_CSS_CONFIG" + self.credentials.DA_USECASE_IDENTIFIER,
            )

            da_response = await self.provider.generate(payload=payload.model_dump())
            # da_response = await self.provider.handle_json(da_response)
            return da_response

        except Exception as e:
            logger.error(f"Error calling EE_MLO_NEW_P2A_CSS_CONFIG: {e}", exc_info=True)
            return None 

    async def EE_MLO_LAYOUT_CODE_GENERATOR(self, layout_info_data: str) -> str:
        """
        Generate all layout components code and document the steps followed by the LLM.

        This function sends a request to the Layout Generator Agent API with the provided
        data file and returns a dictionary containing the header, sidebar, footer, and other
        component codes. Additionally, it documents the steps followed by the LLM to generate
        these components.

        Args:
            request (str): The data to be sent to the API.

        Returns:
            str: A str containing the layout codes and the steps followed by the LLM.

        Raises:
            Exception: If an error occurs during the API call.
        """
        logger.info("---- STEP 4: Calling The Layout Code Generator Agent\n")
        try:
            payload = DARequest(
                mode="EE_MLO_NEW_P2A_LAYOUT_CODE_GENERATOR",
                promptOverride=False,
                userSignature=self.credentials.DA_USER_SIGNATURE,
                prompt=layout_info_data,
                image=None,
                useCaseIdentifier="EE_MLO_NEW_P2A_LAYOUT_CODE_GENERATOR" + self.credentials.DA_USECASE_IDENTIFIER,
            )

            da_response = await self.provider.generate(payload=payload.model_dump())  
            # da_response = await self.provider.handle_json(da_response)  
            return da_response

        except Exception as e:
            logger.error(f"Error calling EE_MLO_NEW_P2A_LAYOUT_CODE_GENERATOR: {e}", exc_info=True)
            return None     
        
    async def EE_MLO_COMPONENT_CODE_GENERATOR(self, layout_info_data: str) -> str:
        """
        Generate all components code and document the steps followed by the LLM.

        This function sends a request to the Layout Generator Agent API with the provided
        data file and returns a dictionary containing the header, sidebar, footer, and other
        component codes. Additionally, it documents the steps followed by the LLM to generate
        these components.

        Args:
            request (str): The data to be sent to the API.

        Returns:
            str: A str containing the layout codes and the steps followed by the LLM.

        Raises:
            Exception: If an error occurs during the API call.
        """
        logger.info("---- STEP 3: Calling The Component Code Generator Agent\n")
        try:
            payload = DARequest(
                mode="EE_MLO_NEW_P2A_COMPONENT_CODE_GENERATOR",
                promptOverride=False,
                userSignature=self.credentials.DA_USER_SIGNATURE,
                prompt=layout_info_data,
                image=None,
                useCaseIdentifier="EE_MLO_NEW_P2A_COMPONENT_CODE_GENERATOR" + self.credentials.DA_USECASE_IDENTIFIER,
            )

            da_response = await self.provider.generate(payload=payload.model_dump())
            # da_response = await self.provider.handle_json(da_response)
            return da_response

        except Exception as e:
            logger.error(f"Error calling EE_MLO_NEW_P2A_COMPONENT_CODE_GENERATOR: {e}", exc_info=True)
            return None

    async def EE_MLO_PAGE_CODE_GENERATOR(self, page_data: str) -> str:
        """
        Generate page code using the Page Code Generator API.

        This function processes the provided page data string, which contains layout-info,
        component details (props, interface, and export function name), and page-type info.
        It sends a request to the Page Code Generator API and returns the LLM's steps to
        write and think the code, along with the generated code.

        Args:
            page_data (str): A string containing layout-info, component details, and page-type info.

        Returns:
            str: A str containing the LLM's steps and the generated code.

        Raises:
            Exception: If an error occurs during the API call.
        """
        logger.info("---- STEP 5: Calling The Page Code Generator\n")
        try:
            payload = DARequest(
                mode="EE_MLO_NEW_P2A_PAGE_CODE_GENERATOR",
                promptOverride=False,
                userSignature=self.credentials.DA_USER_SIGNATURE,
                prompt=page_data,
                image=None,
                useCaseIdentifier="EE_MLO_NEW_P2A_PAGE_CODE_GENERATOR" + self.credentials.DA_USECASE_IDENTIFIER,
            )

            da_response = await self.provider.generate(payload=payload.model_dump())
            # da_response = await self.provider.handle_json(da_response)
            return da_response

        except Exception as e:
            logger.error(f"Error calling EE_MLO_NEW_P2A_PAGE_CODE_GENERATOR: {e}", exc_info=True)
            return None

    """
    All Post Processing Helper Functions
    """

    @staticmethod
    def _safer_clean_json_string(s: str) -> str:
        """
        Removes only C-style block comments (/* ... */) from a JSON-like string.
        This is safer than removing other comment types that might appear in valid strings.
        """
        # Remove C-style comments: /* ... */
        s_no_comments = re.sub(r'/\*.*?\*/', '', s, flags=re.DOTALL)
        return s_no_comments.strip()

    @staticmethod
    def _parse_json_with_fallback(json_str: str) -> Optional[Dict[str, Any]]:
        """
        Tries to parse a JSON string directly. If it fails, it runs a safe
        cleaning function and tries to parse again.
        """
        try:
            # First attempt: assume valid JSON
            return json.loads(json_str)
        except json.JSONDecodeError:
            # First attempt failed, try cleaning and parsing again
            try:
                cleaned_str = DesignGenerationService._safer_clean_json_string(json_str)
                return json.loads(cleaned_str)
            except json.JSONDecodeError:
                # Both attempts failed, the data is truly malformed
                return None
        
    @staticmethod
    def application_info(xml_data: str) -> Union[Tuple[str, str, Dict[str, Any], Dict[str, Any], Dict[str, Any], str], None]:
        """
        Extracts, processes, and validates specific information from an XML-like data string.

        This function parses the input string to find and extract content from six specific tags,
        ensuring the output format for each is strictly maintained.

        - project_info: from <objective> -> string
        - visual_design_pattern: from <visual-interaction-design> -> string
        - components_required_pattern: from <components-required> -> valid json (dict)
        - layout_info_pattern: from <layout-info> -> valid json (dict)
        - page_type_pattern: from <page-type> -> valid json (dict)
        - user_journey_pattern: from <user-journey-mapping> -> string

        Args:
            xml_data (str): The XML-like data as a string.

        Returns:
            Union[Tuple[str, str, Dict, Dict, Dict, str], None]: A tuple containing the six 
            processed data points in the specified order and format if all are found and 
            valid. Otherwise, returns None.
        """
        # Define regex patterns for each required tag
        patterns = {
            'project_info': r"<objective>(.*?)</objective>",
            'visual_design': r"<visual-interaction-design>(.*?)</visual-interaction-design>",
            'layout_components_required': r"<layout-components-required>(.*?)</layout-components-required>",
            'components_required': r"<components-required>(.*?)</components-required>",
            'layout_info': r"<layout-info>(.*?)</layout-info>",
            'page_type': r"<page-type>(.*?)</page-type>",
            'user_journey': r"<user-journey-mapping>(.*?)</user-journey-mapping>",
            'target_audience': r"<target-audience>(.*?)</target-audience>",
            'global_structure': r"<global-structure>(.*?)</global-structure>"
        }

        # Find all matches using re.DOTALL to handle multiline content
        matches = {key: re.search(pattern, xml_data, re.DOTALL) for key, pattern in patterns.items()}

        # Ensure all required tags are present in the input
        if not all(matches.values()):
            return None

        try:
            # --- 1. Extract and Process String Data ---
            project_info = matches['project_info'].group(1).strip()
            visual_info = matches['visual_design'].group(1).strip()
            user_journey_info = matches['user_journey'].group(1).strip()
            target_audience_info = matches['target_audience'].group(1).strip()
            global_structure_info = matches['global_structure'].group(1).strip()

            # --- 2. Extract and Process JSON Data using the robust fallback parser ---
            layout_component_required_json = DesignGenerationService._parse_json_with_fallback(matches['layout_components_required'].group(1))
            components_required_json = DesignGenerationService._parse_json_with_fallback(matches['components_required'].group(1))
            layout_info_json = DesignGenerationService._parse_json_with_fallback(matches['layout_info'].group(1))
            page_type_json = DesignGenerationService._parse_json_with_fallback(matches['page_type'].group(1))

            # Check if any of the JSON parsing attempts ultimately failed
            if not all([layout_component_required_json, components_required_json, layout_info_json, page_type_json]):
                return None

            # --- 3. Return the final, validated tuple ---
            return (
                project_info,
                visual_info,
                layout_component_required_json,
                components_required_json,
                layout_info_json,
                page_type_json,
                user_journey_info,
                target_audience_info,
                global_structure_info
            )

        except AttributeError as e:
            # This handles the case where a .group(1) might fail, though the check
            # for all matches should prevent this.
            logger.error(f"Error occured while parsing the application info: {str(e)}")
            return None
        
    
    @staticmethod
    def parse_text_context(text_context: str) -> dict:
        """
        Parse the given text context to extract narrative steps and code blocks.

        This function processes a text context containing narrative steps and code blocks
        wrapped in `<mlo-write>` and `<mlo-code>` tags. It extracts the narrative
        steps and code blocks into a structured JSON format.

        Args:
            text_context (str): The input text containing narrative steps and code blocks.

        Returns:
            str: A JSON string representing the extracted narrative steps and code blocks.
        """

        output_structure = {"narrative_steps": {}, "code_blocks": []}
        step_counter = 1
        current_position = 0  # Keep track of where we are in the text

        # Remove any <mlo-image>...</mlo-image> tags to avoid confusion with narrative text
        text_context = re.sub(r'<mlo-image>.*?</mlo-image>', '', text_context, flags=re.DOTALL)

        # Pattern to find <mlo-write> blocks and capture file path and content
        write_pattern = re.compile(r'<mlo-write\s+file_path="([^"]+)">(.*?)</mlo-write>', re.DOTALL)

        # Iterate through matches of the <mlo-write> tag
        for match in write_pattern.finditer(text_context):
            # --- Extract narrative text BEFORE the current code block ---
            narrative_chunk = text_context[current_position : match.start()].strip()
            if narrative_chunk:
                # Check if this chunk is inside <mlo-code>
                code_block_search_area = text_context[:match.start()]
                last_code_open = code_block_search_area.rfind('<mlo-code>')
                last_code_close = code_block_search_area.rfind('</mlo-code>')

                # Only add narrative if it's not just whitespace or inside a previously closed code block
                if last_code_open > last_code_close or last_code_open == -1:
                    # Split the chunk further if it contains the </mlo-code> closing tag
                    parts = re.split(r'(</mlo-code>)', narrative_chunk)
                    for i, part in enumerate(parts):
                        part = part.strip()
                        if not part:
                            continue

                        # Handle the part before </mlo-code> if it exists
                        if part != '</mlo-code>' and i < len(parts) - 1 and parts[i + 1] == '</mlo-code>':
                            output_structure["narrative_steps"][f"step_{step_counter}"] = part
                            step_counter += 1
                        # Handle the part after </mlo-code> or if no closing tag was found in the chunk
                        elif part != '</mlo-code>' and (i == 0 or parts[i - 1] != '</mlo-code>'):
                            # Further split by <mlo-code> opening tag
                            sub_parts = re.split(r'(<mlo-code>)', part)
                            for sub_part in sub_parts:
                                sub_part = sub_part.strip()
                                if not sub_part or sub_part == '<mlo-code>':
                                    continue
                                output_structure["narrative_steps"][
                                    f"step_{step_counter}"
                                ] = sub_part
                                step_counter += 1

            # --- Extract the code block itself ---
            file_path = match.group(1)
            content = match.group(2).strip()
            output_structure["code_blocks"].append(
                {"fileName": file_path, "content": content}
            )

            # Update current position to the end of this match
            current_position = match.end()

        # --- Extract any remaining narrative text AFTER the last code block ---
        remaining_narrative = text_context[current_position:].strip()
        if remaining_narrative:
            # Split the remaining chunk by <mlo-code> tags
            parts = re.split(r'(<mlo-code>|</mlo-code>)', remaining_narrative)
            for part in parts:
                part = part.strip()
                if not part or part == '<mlo-code>' or part == '</mlo-code>':
                    continue
                output_structure["narrative_steps"][f"step_{step_counter}"] = part
                step_counter += 1

        # Convert the final structure to a JSON string
        json_output = json.dumps(output_structure, indent=4)
        json_output = json.loads(json_output)
        return json_output
    
    @staticmethod
    def parse_image_block(text_context: str) -> list[dict]:
        """
        Parses and extracts the <mlo-image> block from the text context.

        This function specifically looks for a single <mlo-image> tag, extracts
        its content, and returns it in a structured list. It ignores all other tags.

        Args:
            text_context (str): The input text which may contain an <mlo-image> tag.

        Returns:
            list[dict]: A list containing a dictionary for the found image.
                        Returns an empty list if no tag is found.
        """
        image_blocks = []
        # Pattern to find the <mlo-image> block and capture its content.
        image_pattern = re.compile(r'<mlo-image>(.*?)</mlo-image>', re.DOTALL)
        image_match = image_pattern.search(text_context)

        if image_match:
            # Extract the description content from the tag
            description = image_match.group(1).strip()
            if description:
                image_blocks.append({"description": description})

        return image_blocks

    @staticmethod
    def extract_component_details(code_blocks: list) -> list:
        """
        Extracts all interface definitions and the exported default function/component name
        from TypeScript component code blocks.

        Args:
            code_blocks: A list of dictionaries, each with 'fileName' and 'content'.

        Returns:
            A list of dictionaries. Each dictionary has the fileName as a key,
            and its value is another dictionary with "interfaces" and "export function name".
            The "interfaces" key holds a dictionary of {interfaceName: interfaceBodyString}.
        """
        results = []

        # Regex for the default exported component name:
        # Catches `export default MyComponent;` or `export default function MyComponent ...`
        # Group 1: Component name
        fn_pattern = re.compile(r"export\s+default\s+(?:function\s+)?([A-Za-z_]\w*)")

        # Regex for interface definitions:
        # - `interface\s+`: Matches "interface" keyword.
        # - `([A-Za-z_]\w*(?:\s*<[^>]+>)?(?:\s*<[^>]+>)?)`: Group 1 (Interface Name).
        #   - `[A-Za-z_]\w*`: Basic interface name.
        #   - `(?:\s*<[^>]+>)?`: Optional non-capturing group for generics like `<T>`.
        #     Allows for one level of simple generics in the name.
        # - `\s*(?:extends\s+[\w\s,.<>\[\]:]+)?`: Optional non-capturing group for `extends SomeBase, OtherBase`.
        #   - `[\w\s,.<>\[\]:]+`: Matches characters allowed in extended type names.
        # - `\s*\{`: Matches the opening curly brace.
        # - `([\s\S]*?)`: Group 2 (Interface Body). Captures everything inside, non-greedily.
        # - `\}`: Matches the closing curly brace.
        interface_pattern = re.compile(
            r"interface\s+([A-Za-z_]\w*(?:\s*<[^>]+>)?)\s*(?:extends\s+[\w\s,.<>\[\]:]+)?\s*\{([\s\S]*?)\}",
            re.MULTILINE,
        )

        for block in code_blocks:
            fileName = block["fileName"]
            content = block["content"]

            component_name = None
            interfaces_data = {}  # To store {interfaceName: interfaceBodyString}

            # 1. Find the exported default function/component name
            fn_match = fn_pattern.search(content)
            if fn_match:
                component_name = fn_match.group(1)

            # 2. Find all interface definitions
            for match in interface_pattern.finditer(content):
                interface_name = match.group(
                    1
                ).strip()  # Full name, e.g., "MyProps" or "MyProps<T>"
                interface_body = match.group(2).strip()  # Raw content inside the braces
                interfaces_data[interface_name] = interface_body

            file_details = {
                "interface and props": interfaces_data,
                "export function name": component_name,
            }
            results.append({fileName: file_details})

        return results

    # App.tsx Generation code
    @staticmethod
    def _to_kebab_case(name: str) -> str:
        """Converts a string to kebab-case, handling spaces and CamelCase."""
        # Remove common suffixes like "Page" or "View"
        name = re.sub(r'(Page|View)$', '', name)
        # Insert a space before capital letters (for CamelCase)
        name = re.sub(r'(?<!^)(?=[A-Z])', ' ', name)
        # Replace non-alphanumeric characters with a space
        name = re.sub(r'[^a-zA-Z0-9\s]', ' ', name)
        # Replace multiple spaces/hyphens with a single hyphen and convert to lowercase
        return re.sub(r'[\s-]+', '-', name).lower().strip('-')

    @staticmethod
    def _to_pascal_case(name: str) -> str:
        """Converts a string to PascalCase, handling spaces, hyphens, and CamelCase."""
        # Insert a space before capital letters (for CamelCase/PascalCase)
        name = re.sub(r'(?<!^)(?=[A-Z])', ' ', name)
        # Replace non-alphanumeric characters with a space
        name = re.sub(r'[^a-zA-Z0-9]+', ' ', name)
        # Capitalize each word and join
        return "".join(word.capitalize() for word in name.split())

    @staticmethod
    def generate_app_tsx_from_page_types(
        page_type_data: Dict[str, str],
    ) -> List[Dict[str, str]]:
        """
        Generate a React App.tsx file from a dictionary of page types.

        Parameters:
            page_type_data: A dictionary where keys are component names (e.g., "DashboardOverviewPage")
                            and values are descriptions (str).
                            The first entry in this dictionary (by insertion order)
                            will be mapped to the root path "/".

        The generated file will include:
        - Standard imports (Toaster, Sonner, TooltipProvider, etc.)
        - Imports for each unique page component plus a NotFound component.
        - A <Routes> block with <Route> entries derived from the page_type_data keys,
          followed by a catch-all `*` route to NotFound.
        - The first page from page_type_data is mapped to "/", others are kebab-cased.
        """
        # Sanitize page names to be valid PascalCase component names
        sanitized_page_data = {
            DesignGenerationService._to_pascal_case(name): desc
            for name, desc in page_type_data.items()
        }
        
        pairs = []
        page_names = list(sanitized_page_data.keys())

        # Determine the root page name. If page_type_data is not empty,
        # the first page (by insertion order) becomes the root page.
        root_page_name = page_names[0] if page_names else None

        for page_name in page_names:
            if page_name == root_page_name:
                # The root page (first page from input) is assigned the path "/"
                route_path = "/"
            else:
                # Other pages are converted to kebab-case for their paths
                route_path = f"/{DesignGenerationService._to_kebab_case(page_name)}"
            pairs.append((route_path, page_name))

        # Sort pairs by route path for consistent ordering, putting "/" first.
        pairs.sort(key=lambda x: (x[0] != "/", x[0]))


        # Collect unique page names for imports (add NotFound)
        unique_pages = sorted({page for _, page in pairs})
        if "NotFound" not in unique_pages:
            unique_pages.append("NotFound") # Ensure NotFound is always there

        # Header imports
        header = textwrap.dedent("""\
        import { Toaster } from "@/components/ui/toaster";
        import { Toaster as Sonner } from "@/components/ui/sonner";
        import { TooltipProvider } from "@/components/ui/tooltip";
        import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
        import { BrowserRouter, Routes, Route } from "react-router-dom";
        """)

        # Page component imports (default imports)
        page_imports = "\n".join(
            f'import {page} from "./pages/{page}";' for page in unique_pages
        )

        # Route entries
        route_entries = "\n".join(
            f'          <Route path="{route}" element={{<{page} />}} />'
            for route, page in pairs
        )

        # Assemble App.tsx content
        content = "\n\n".join(
            [
                header,
                page_imports,
                "const queryClient = new QueryClient();",
                textwrap.dedent("""
            const App = () => (
            <QueryClientProvider client={queryClient}>
                <TooltipProvider>
                <Toaster />
                <Sonner />
                <BrowserRouter>
                    <Routes>
            """),
                route_entries
                + '\n          {/* catch-all */}\n          <Route path="*" element={<NotFound />} />',
                textwrap.dedent("""
                    </Routes>
                </BrowserRouter>
                </TooltipProvider>
            </QueryClientProvider>
            );

            export default App;
            """),
            ]
        )

        # Structure: [{"fileName": "src/App.tsx", "content": content}]
        output_files = [{"fileName": "src/App.tsx", "content": content}]

        return output_files
    
    @staticmethod
    def replace_image_links(input_json_data: list, code_data: list) -> list:
        """
        Replaces placeholder image URLs in code files with actual URLs from a mapping.

        Args:
            input_json_data: A list of objects, where each object contains a page
                             name as a key and a dictionary of dummy-to-real
                             URL mappings as the value.
            code_data: A list of file objects, each with a 'fileName' and
                       'content' string.

        Returns:
            A list of the modified code data objects, with all placeholder URLs
            replaced by their corresponding actual URLs.
        """
        # 1. Create a single, unified dictionary for URL replacement.
        # This flattens the structure from [{'homepage': {...}}, {'menupage': {...}}]
        # into a single map like {'dummy_url1': 'real_url1', ...}.
        if isinstance(input_json_data, str):
            input_json_data = json.loads(input_json_data)
        else:
            # It's already a list, so we can use it directly.
            input_json_data = input_json_data
            
        if isinstance(code_data, str):
            code_data = json.loads(code_data)
        else:
            # It's already a list, so we can use it directly.
            code_data = code_data

        url_map = {}
        for page_object in input_json_data:
            for _, mappings in page_object.items():
                url_map.update(mappings)

        # 2. Iterate through each file's content and perform the replacements.
        # We create a deep copy to avoid modifying the original input list in-place.
        modified_code_data = json.loads(json.dumps(code_data))

        for file_object in modified_code_data:
            content = file_object.get("content", "")
            # Sequentially replace each placeholder URL found in the map.
            for placeholder_url, actual_url in url_map.items():
                content = content.replace(placeholder_url, actual_url)
            
            # Update the content in the new dictionary.
            file_object["content"] = content

        # 3. Return the modified list of dictionaries.
        return modified_code_data

