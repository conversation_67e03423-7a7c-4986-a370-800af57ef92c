# import random
# import chromadb
# import pandas as pd
# import re
# import dotenv
# import chromadb.utils.embedding_functions as embedding_functions
# from langchain_core.documents import Document
# from datetime import datetime
# import json
# from src.settings.settings import AzureOpenAICreds
# from src.settings.db_config import ChromaDbCreds

# # from app.db.crud_design_system import get_all_items
# # from app.db.mongodb import get_collection

# azure_creds = AzureOpenAICreds()
# chromadb_creds = ChromaDbCreds()


# class EmbeddingManager:
#     def __init__(self):
#         dotenv.load_dotenv()

#         self.embeddings = embedding_functions.OpenAIEmbeddingFunction(
#             api_key=azure_creds.AZURE_VISION_API_KEY,
#             api_base=azure_creds.AZURE_VISION_ENDPOINT,
#             api_type="azure",
#             api_version="2024-02-01",
#             model_name=azure_creds.AZURE_EMBEDDING_DEPLOYMENT_NAME
#         )
#         self.chroma_client = chromadb.HttpClient(host=chromadb_creds.MLO_CHROMADB_ENDPOINT, port=8000)
#         print(f"connecting to VDB:{chromadb_creds.MLO_CHROMADB_ENDPOINT}. Fetching collection : {chromadb_creds.MLO_CHROMADB_COLLECTION_NAME}")
#         self.documents = []
#         self.collection = self.chroma_client.get_or_create_collection(chromadb_creds.MLO_CHROMADB_COLLECTION_NAME,embedding_function=self.embeddings)
#         self.data = None

#     def create_embeddings(self, document_path):
#         print('Creating embeddings')
#         df = pd.read_csv(document_path,encoding_errors='replace')

#         for index, row in df.iterrows():
#             metadata = {
#                 "Type": row['Type'],
#                 "Component": row['Component'],
#                 "Code": row['Code'],
#                 "Domain": row['Domain'],
#                 "Design System": row['Design System']
#             }
#             document = Document(page_content=row['Description'], metadata=metadata)
#             self.documents.append(document)
#             self.collection.add(ids=str(index), metadatas=metadata, documents=row['Description'])

#     def delete_collection(self,collection_name):
#         self.chroma_client.delete_collection(name=collection_name)

#     def preprocess_text(self, text):
#         # Convert to lowercase and extract keywords
#         text = text.lower().strip()
#         keywords = re.findall(r'\b\w+\b', text)
#         return keywords

#     def search_similar(self, chroma_collection, query_text, column):
#         print(f"Searching for '{query_text}' in column '{column}'", datetime.now())

#         if column == 'Description':
#             result = chroma_collection.query(query_texts=[query_text], n_results=3)
#         else:
#             query_keywords = self.preprocess_text(query_text)

#             result = []
#             for doc in self.documents:
#                 metadata_value = doc.metadata.get(column, "")
#                 metadata_keywords = self.preprocess_text(metadata_value)
#                 if any(keyword in metadata_keywords for keyword in query_keywords):
#                     result.append(doc)
#                 if any(query in metadata_value for query in query_keywords):
#                     result.append(doc)

#         print("Search complete", datetime.now())
#         return result

#     def load_json(self, file_path):
#         with open(file_path, 'r') as file:
#             self.data = json.load(file)
#             return self.data

#     def helper_function(self, file, domain, design_sys):

#         section_docs = self.search_similar(self.collection, file["description"], "Description")
#         section_metadatas = section_docs['metadatas'][0]
#         print('helper function: file:', section_metadatas)
#         new_section_docs = []
#         for doc in section_metadatas:
#             # domain_string = doc['Domain']
#             # design_sys_string = doc['Design System']
#             # if domain in domain_string and design_sys in design_sys_string:
#             new_section_docs.append(doc)
#         if new_section_docs:
#             file["code"] = self.get_code(new_section_docs[0]['Code'])
#             print(file["code"])
#         else:
#             file["code"] = self.get_code(section_metadatas[0]['Code'])
#             print(file["code"])

#     def get_templates(self,file, depth = 0):
#         domain = self.data["domain"]
#         design_sys = self.data["designSystem"]
#         if depth == 0:
#             for section in file["layout"]:
#                 self.helper_function(section, domain, design_sys)
#                 if "elements" in section:
#                     self.get_templates(section["elements"], depth + 1)
#                 elif "subsections" in section:
#                     self.get_templates(section["subsections"], depth + 1)
#         elif "subsection" in file[0]:
#             for subsection in file:
#                 self.helper_function(subsection, domain, design_sys)
#                 if "elements" in subsection:
#                     self.get_templates(subsection["elements"], depth + 1)
#                 elif "subsections" in subsection:
#                     self.get_templates(subsection["subsections"], depth + 1)
#         else:
#             for element in file:
#                 element_docs = self.search_similar(self.collection, element["tag"], "Component")
#                 if element_docs:
#                     new_element_docs = []
#                     for element_doc in element_docs:
#                         domain_string = element_doc.metadata.get("Domain", "")
#                         if domain in domain_string:
#                             new_element_docs.append(element_doc)
#                     if new_element_docs:
#                         element["code"] = self.get_code(new_element_docs[0].metadata.get("Code", ""))
#                         print(element["type"], element["code"])
#                     else:
#                         element["code"] = self.get_code(element_docs[0].metadata.get("Code", ""))
#                         print(element["type"], element["code"])
#                 if "elements" in element:
#                     self.get_templates(element["elements"], depth + 1)
#                 elif "subsections" in element:
#                     self.get_templates(element["subsections"], depth + 1)

#         # self.data = data
#         with open('data.json', 'w') as json_file:
#             json.dump(self.data, json_file, indent=4)

#     def get_template_code(self, layout_details, randomize=False):
#         if not isinstance(layout_details, dict) or "elements" not in layout_details:
#             raise ValueError("Invalid layout details format")
#             # Generate the composite description for the layout
#         section_query = self.generate_composite_descriptions(layout_details)
#         section_document = self.similarity_search(section_query)
#         index = 0
#         if randomize:
#             index = random.randint(1, len(section_document.get("metadatas", [])) - 1)
#         selected_section_metadata = section_document['metadatas'][index]
#          # Build the result string
#         result = (
#             f"Design Layout (JSON): \n{json.dumps(layout_details, indent=4)}\n"
#             f"Existing Code Snippet:\n{selected_section_metadata[0]['Code']}\n"
#         )
#         return result

#     def similarity_search(self, query):
#         return self.collection.query(query_texts=[query], n_results=3)


#     def generate_composite_descriptions(self,section):
#         section_desc = section['description']
#         if 'subsections' in section:
#             for subsection in section['subsections']:
#                 section_desc += subsection['description']

#         return section_desc

#     def find_templates(self, file, depth = 0):
#         self.data = file
#         self.get_templates(file, depth)
#         return self.data


#     def get_code(self, text: str):
#         return text.split("Code:")

#     # def create_embeddings_chromadb(self, mongo_db_collection_name: str, vector_db_collection_name: str):
#     #     try:
#     #         print(f"Creating embeddings in vector db for collection: {vector_db_collection_name}")
#     #         collection = get_collection(mongo_db_collection_name)
#     #         items = get_all_items(collection)
#     #         self.collection = self.chroma_client.get_or_create_collection(
#     #             vector_db_collection_name, embedding_function=self.embeddings
#     #         )

#     #         for item in items:
#     #             metadata = {
#     #                 "Type": item.get('Type'),
#     #                 "Component": item.get('Component'),
#     #                 "Code": item.get('Code'),
#     #                 "Domain": item.get('Domain'),
#     #                 "Design System": item.get('Design System')
#     #             }
#     #             doc_content = item.get('Description')
#     #             document_obj = Document(page_content=doc_content, metadata=metadata)
#     #             self.documents.append(document_obj)
#     #             self.collection.add(ids=str(item['_id']), metadatas=metadata, documents=doc_content)

#     #         return items

#     #     except Exception as e:
#     #         error_message = str(e)
#     #         raise Exception(f"Failed to create embeddings in ChromaDB: {error_message}")
