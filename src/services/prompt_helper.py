import json
from typing import Any, Dict, List


from src.utils.logger import AppLogger

logging = AppLogger("CG_S").get_logger()


class PromptHelper:
    # --- Helper Function to Categorize Files ---
    @staticmethod
    def _categorize_files(
        files_to_generate: List[str], framework: str
    ) -> Dict[str, Any]:
        categories = {
            "config": [],
            "core": [],
            "styles": [],
            "logic": [],
            "components": {},
            "header": [],
            "sidebar": [],
            "main_content": [],
            "root": [],
            "unknown": [],
        }

        component_files_extensions = {
            "angular": [
                ".component.ts",
                ".component.html",
                ".component.css",
                ".component.scss",
            ],
            "react": [".jsx", ".tsx", ".module.css", ".module.scss", ".css", ".scss"],
            "vue": [".vue"],
        }

        logic_dirs = [
            "services",
            "models",
            "hooks",
            "store",
            "api",
            "utils",
            "types",
            "interfaces",
            "lib",
            "shared",
        ]

        root_app_files = {
            "angular": [
                "app.component.ts",
                "app.component.html",
                "app.component.css",
                "app.module.ts",
                "app.config.ts",
                "app.routes.ts",
            ],
            "react": [
                "App.jsx",
                "App.tsx",
                "index.js",
                "main.jsx",
                "layout.jsx",
                "layout.tsx",
            ],
            "vue": ["App.vue", "main.js", "main.ts", "router.js", "store.js"],
        }
        core_files = ["main.ts", "index.js", "index.html", "polyfills.ts"]

        config_files = [
            ".gitignore",
            "package.json",
            "angular.json",
            "tsconfig.json",
            "vite.config.js",
            "next.config.js",
            "vue.config.js",
            "jsconfig.json",
            "postcss.config.js",
            ".editorconfig",
            "tsconfig.app.json",
            "tsconfig.spec.json",
        ]

        # Add design system files that were already generated
        # Note: tailwind.config.js might also be in config_files list, handle potential duplicates if necessary
        design_system_config_files = [
            "tailwind.config.js",
            "tailwind.config.ts",
            "material.module.ts",
            "bootstrap.config.js",
        ]
        design_system_style_files = [
            "styles.css",
            "index.css",
            "global.css",
            "styles.scss",
            "index.scss",
            "theme.ts",
            "theme.js",
            "material-theme.scss",
            "material-custom.scss",
            "bootstrap.scss",
            "bootstrap-custom.scss",
            "_variables.scss",
        ]

        files_processed = set()  # To avoid double categorization

        # Prioritize specific known files
        for file_path in files_to_generate:
            base_name = file_path.split("/")[-1]

            # Design system files (already generated, track them)
            if (
                base_name in design_system_config_files
                or base_name in design_system_style_files
            ):
                if file_path not in files_processed:
                    categories["styles"].append(file_path)
                    files_processed.add(file_path)
                continue

            # Config files
            if base_name in config_files or file_path in config_files:
                if file_path not in files_processed:
                    categories["config"].append(file_path)
                    files_processed.add(file_path)
                continue

            # Root app files
            if any(file_path.endswith(rf) for rf in root_app_files.get(framework, [])):
                if file_path not in files_processed:
                    categories["root"].append(file_path)
                    files_processed.add(file_path)
                continue

            # Core files
            if base_name in core_files:
                if file_path not in files_processed:
                    categories["core"].append(file_path)
                    files_processed.add(file_path)
                continue

        # Categorize remaining based on paths and extensions
        for file_path in files_to_generate:
            if file_path in files_processed:
                continue  # Already categorized

            path_parts = file_path.split("/")

            # Root Files
            if "app" in path_parts and any(
                path_parts[-1].endswith(rf) for rf in root_app_files.get(framework, [])
            ):
                categories["root"].append(file_path)
                files_processed.add(file_path)
                continue

            # Special Component Files (Header, Sidebar, Main Content)
            if "components" in path_parts or any(
                file_path.endswith(ext)
                for ext in component_files_extensions.get(framework, [])
            ):
                # Try to find component name (usually the directory name before the file)
                component_name = "unknown_component"
                if len(path_parts) > 2 and path_parts[-2] != "components":
                    component_name = path_parts[-2]
                elif len(path_parts) > 1 and path_parts[-1].split(".")[0] not in [
                    "index",
                    "main",
                    "app",
                    "styles",
                ]:
                    # Handle flat component structures
                    potential_name = path_parts[-1].split(".")[0]
                    # Basic check if it resembles a component name (e.g., PascalCase for React/Angular)
                    if potential_name[0].isupper():
                        component_name = potential_name

                # Check for header component
                if component_name.lower() in [
                    "header",
                    "appheader",
                    "app-header",
                    "navbar",
                    "navigation",
                    "nav",
                    "topbar",
                    "top-bar",
                ]:
                    categories["header"].append(file_path)
                    files_processed.add(file_path)
                    continue
                # Check for sidebar component
                elif component_name.lower() in [
                    "sidebar",
                    "appsidebar",
                    "app-sidebar",
                    "sidenav",
                    "side-nav",
                    "leftmenu",
                    "left-menu",
                ]:
                    categories["sidebar"].append(file_path)
                    files_processed.add(file_path)
                    continue
                # Check for main content component
                elif component_name.lower() in [
                    "maincontent",
                    "main-content",
                    "content",
                    "main",
                    "dashboard",
                    "home",
                    "container",
                    "content-area",
                ]:
                    categories["main_content"].append(file_path)
                    files_processed.add(file_path)
                    continue
                # Regular component
                else:
                    if component_name not in categories["components"]:
                        categories["components"][component_name] = []
                    categories["components"][component_name].append(file_path)
                    files_processed.add(file_path)
                    continue

            # Logic Files (Services, Models, Hooks, etc.)
            if any(logic_dir in path_parts for logic_dir in logic_dirs):
                categories["logic"].append(file_path)
                files_processed.add(file_path)
                continue

            # If none of the above, mark as unknown
            categories["unknown"].append(file_path)
            files_processed.add(file_path)  # Mark as processed even if unknown

        # Clean up empty component entries if any were misidentified
        categories["components"] = {
            k: v for k, v in categories["components"].items() if v
        }

        # logging.info(f"Categorized files: {json.dumps(categories, indent=2)}")
        return categories

    # --- Helper Function to Create Prompts ---
    @staticmethod
    def _create_prompt_for_batch(
        batch_type: str,
        target_files: List[str],
        framework: str,
        design_library: str,
        available_context_code: Dict[str, str],
        detailed_analysis_summary: str,
        component_name: str = None,
    ) -> str:
        """
        Generates a highly specific prompt for the LLM to generate code for a batch of files.

        Args:
            batch_type: The category of files being generated ('config', 'core', 'logic', 'component', 'root').
            target_files: A list of full file paths to be generated in this batch.
            framework: The frontend framework (e.g., 'angular', 'react', 'vue').
            design_library: The design system/library used (e.g., 'tailwindcss', 'css-variables', 'material', 'bootstrap').
            available_context_code: A dictionary mapping file paths to their code content for files already generated or provided
                                      (e.g., design system foundation, previously generated batches). This provides context.
            detailed_analysis_summary: The text summary describing the application's requirements, layout, and component details.
            component_name: The name of the component (only used when batch_type is 'component').

        Returns:
            A string containing the detailed prompt for the LLM.
        """

        # --- 1. Role Definition ---
        role = f"""
            # ROLE: Expert AI {framework.capitalize()} Frontend Developer

            You are an expert-level AI specializing in generating high-quality, production-ready frontend code using the **{framework.capitalize()}** framework and the **{design_library}** styling approach.

            Your expertise includes:
            - Deep knowledge of {framework.capitalize()} best practices and patterns
            - Pixel-perfect implementation of designs using {design_library}
            - Writing clean, maintainable, and efficient code
            - Creating reusable components and services
            - Implementing proper error handling and data validation

            Your task is to generate the *complete* and *syntactically correct* code content for a specified set of files based *only* on the provided context("available_context_code") code and instructions. Adherence to best practices for {framework.capitalize()} is paramount.
            """

        # --- 2. Core Task ---
        goal = (
            """
        # GOAL: Generate Code for Target Files

        Your primary objective is to generate the full code content for the following file(s):
        """
            + "\n".join([f"- `{f}`" for f in target_files])
            + "\n"
            + """
        You will output the result as a single JSON object where keys are the exact file paths provided above, and values are strings containing the complete code for each respective file.
        """
        )

        # --- 3. Provided Context ---
        # Use json.dumps for reliable formatting of the context code structure
        # context_design_summary = {
        #     "description": "Design system foundation files (styles, tailwind config, etc.) provided for context.",
        #     "files": available_context_code
        # }

        context_code_summary = {
            "description": "Code for related files (design system, previously generated logic/components) provided for context.",
            "files": available_context_code,
        }

        inputs = f"""
        # INPUTS (Context & Requirements):

        1.  **Framework:** `{framework}`
        2.  **Design Library/Approach:** `{design_library}`
        3.  **Target Files to Generate:** `{json.dumps(target_files)}`
        4.  **Detailed Analysis Summary:**
            ```text
            {detailed_analysis_summary}
            ```
            *   This summary describes the overall application, layout, features, and specific requirements for components/logic relevant to the `Target Files`. Use this as the primary guide for *what* to build.

        5.  **Available Context Code (`available_context_code`):**
            ```json
            {json.dumps(context_code_summary, indent=2)}
            ```
            *   You MUST thoroughly analyze and utilize the code content from these related files to ensure:

        6. Package.josn :
        **CRITICAL - DEPENDENCY VERSION COMPATIBILITY:**
            * Use EXACTLY the following versions for core dependencies to ensure compatibility:
            ```json
            {{
                "dependencies": {{
                      "@angular/animations": "^17.3.0",
                      "@angular/common": "^17.3.0",
                      "@angular/compiler": "^17.3.0",
                      "@angular/core": "^17.3.0",
                      "@angular/forms": "^17.3.0",
                      "@angular/platform-browser": "^17.3.0",
                      "@angular/platform-browser-dynamic": "^17.3.0",
                      "@angular/router": "^17.3.0",
                      "@fortawesome/angular-fontawesome": "^1.0.0",
                      "@fortawesome/fontawesome-svg-core": "^6.7.2",
                      "@fortawesome/free-solid-svg-icons": "^6.7.2",
                      "apexcharts": "^3.48.0",
                      "ng-apexcharts": "^1.15.0",
                      "rxjs": "~7.8.0",
                      "tslib": "^2.3.0",
                      "zone.js": "~0.14.3"
                }},
                "devDependencies": {{
                      "@angular-devkit/build-angular": "^17.3.13",
                      "@angular/cli": "^17.3.13",
                      "@angular/compiler-cli": "^17.3.0",
                      "autoprefixer": "^10.4.21",
                      "postcss": "^8.5.3",
                      "tailwindcss": "^3.4.17",
                      "typescript": "~5.4.2"
                }}
            }}```
        """

        # --- 4. Specific Instructions based on Batch Type ---
        instructions_header = (
            f"\n# SPECIFIC INSTRUCTIONS for Batch Type: '{batch_type.upper()}'\n"
        )
        specific_instructions = ""

        if batch_type == "config":
            specific_instructions = f"""
        -   Generate standard, functional configuration files appropriate for a **{framework.capitalize()}** project using **{design_library}**.
        -   **`package.json`**: Include essential dependencies for {framework} and {design_library}. Include basic scripts (`start`, `build`, `test`).
        -   **CRITICAL - DEPENDENCY VERSION COMPATIBILITY:**
            * Use EXACTLY the following versions for core dependencies to ensure compatibility:
            ```json
            {{
                "dependencies": {{
                      "@angular/animations": "^17.3.0",
                      "@angular/common": "^17.3.0",
                      "@angular/compiler": "^17.3.0",
                      "@angular/core": "^17.3.0",
                      "@angular/forms": "^17.3.0",
                      "@angular/platform-browser": "^17.3.0",
                      "@angular/platform-browser-dynamic": "^17.3.0",
                      "@angular/router": "^17.3.0",
                      "@fortawesome/angular-fontawesome": "^1.0.0",
                      "@fortawesome/fontawesome-svg-core": "^6.7.2",
                      "@fortawesome/free-solid-svg-icons": "^6.7.2",
                      "apexcharts": "^3.48.0",
                      "ng-apexcharts": "^1.15.0",
                      "rxjs": "~7.8.0",
                      "tslib": "^2.3.0",
                      "zone.js": "~0.14.3"
                }},
                "devDependencies": {{
                      "@angular-devkit/build-angular": "^17.3.13",
                      "@angular/cli": "^17.3.13",
                      "@angular/compiler-cli": "^17.3.0",
                      "autoprefixer": "^10.4.21",
                      "postcss": "^8.5.3",
                      "tailwindcss": "^3.4.17",
                      "typescript": "~5.4.2"
                }}
            }}```
            * For ANY additional packages needed, ensure they are compatible with Angular 17.3.x and TypeScript 5.4.x
            * When adding new packages, research and verify compatibility with the existing dependencies
            * If adding UI component libraries, ensure they are compatible with both Angular 17.3.x and Tailwind CSS 3.4.x
            * NEVER downgrade core Angular packages or TypeScript from the versions specified above
            * If you need to add packages not listed above, use the LATEST STABLE version that is COMPATIBLE with Angular 17.3.x
        """
        elif batch_type == "core":
            specific_instructions = f"""
        -   Generate the core application entry point and bootstrapping files standard for **{framework.capitalize()}**.
        -   **`src/main.ts` (or equivalent js/jsx)**: Initialize and mount the root application component. Set up providers or configurations required at the root level based on the analysis summary (if any).
        -   **`src/index.html`**: Create the main HTML structure. Include the root element (e.g., `<app-root>`, `<div id="root">`) where the application will be mounted. Link essential CSS files (like the main `styles.css`). Add necessary meta tags.
        """
        elif batch_type == "logic":
            specific_instructions = f"""
    -       Generate TypeScript/JavaScript definitions based **solely and strictly** on the data structures and exact values visually depicted in the **provided image**. The `detailedAnalysisSummary` should be considered an accurate textual representation of that image content. Do not infer properties, data, or functionalities not explicitly shown in the image.

    -       **Services (e.g., `*.service.ts`)**:
            *   **Class Declaration**: Create the service class structure (e.g., using Angular's `@Injectable({{ providedIn: 'root' }})` if applicable).
            *   **Serve Only Image Data**: **Crucially**, import the image-derived dummy data constant created above. All service methods must return data derived *exclusively* from this constant. Use appropriate wrappers like `of()` from RxJS for `Observable` return types or return directly/use `Promise.resolve()` as suitable for the framework and method signature.
            *   **Methods Reflecting Image Data Operations**: Implement methods that simulate common operations on the static image data (e.g., `getAllItems()`, `getItemById(id: number)`). Ensure method signatures use the exact interfaces defined from the image data and return types like `Observable<Model[]>`, `Observable<Model | undefined>`, etc.
            *   **NO API Calls / Comment Out API Logic**: **Do NOT inject `HttpClient`** or implement any logic for fetching data from external APIs. The service's sole purpose is to provide the static data shown in the image for demonstration. If generating within existing code that has API calls, **comment out** the `HttpClient` injection and any HTTP request logic within the methods, replacing it with the return of the image-based dummy data.
            *   **Simple Constructor**: The constructor should generally be empty, unless required by the {framework.capitalize()} framework itself (e.g., basic DI setup without `HttpClient`).

    -       **General**:
            *   Adhere strictly to {framework.capitalize()} conventions for naming, file structure, and code style.
            *   Ensure necessary imports are included.
            *   The final code must function as a self-contained display or manipulation of the **static data captured from the image**.
        """
        elif batch_type == "component":
            component_files_str = ", ".join(
                [f"`{f.split('/')[-1]}`" for f in target_files]
            )

            # Add specific context about component ordering and dependencies with enhanced layout guidance
            component_context = ""
            if component_name.lower() in [
                "header",
                "appheader",
                "navbar",
                "navigation",
                "nav",
                "topbar",
            ]:
                component_context = """
                **IMPORTANT CONTEXT:** You are generating the **HEADER** component, which is the FIRST component in the sequential generation process.
                This component will be used as context for all subsequent components (sidebar, other components, and main content).
                Ensure this component is self-contained and doesn't reference other components that haven't been generated yet.

                **LAYOUT REQUIREMENTS:**
                * The header should use proper flex properties (`flex`, `items-center`, `justify-between`) for alignment
                * Ensure the header has appropriate height and padding
                * Position elements correctly (logo/brand on left)
                * Use appropriate z-index to ensure the header appears above other content
                * Apply proper responsive behavior using Tailwind's responsive prefixes (sm:, md:, lg:, etc.)
                """
            elif component_name.lower() in [
                "sidebar",
                "appsidebar",
                "sidenav",
                "side-nav",
                "leftmenu",
                "left-menu",
            ]:
                component_context = """
                **IMPORTANT CONTEXT:** You are generating the **SIDEBAR** component, which is the SECOND component in the sequential generation process.
                Other components have already been generated and is available in the context.
                This sidebar component will be used as context for all subsequent components (other components and main content).
                You can reference the other component if needed, but ensure this component doesn't reference other components that haven't been generated yet.

                **LAYOUT REQUIREMENTS:**
                * The sidebar should use `flex flex-col` for vertical stacking of navigation items
                * Use `h-screen` or `h-full` with proper overflow handling (`overflow-y-auto`)
                * Ensure proper spacing between navigation items
                * Apply proper responsive behavior, potentially hiding on smaller screens
                * Use appropriate z-index to ensure proper layering
                """
            elif component_name.lower() in [
                "maincontent",
                "main-content",
                "content",
                "main",
                "dashboard",
                "home",
                "container",
            ]:
                component_context = """
                **IMPORTANT CONTEXT:** You are generating the **MAIN CONTENT** component, which is the LAST component in the sequential generation process.
                All other components (header, sidebar, and other components) have already been generated and are available in the context.
                You can reference any previously generated components as needed to ensure consistency and proper integration.
                This component should integrate with the existing layout structure defined by the other components like header and sidebar components.

                **LAYOUT REQUIREMENTS:**
                * The main content should use `flex-1` to take up remaining space
                * Use `overflow-auto` to handle content overflow
                * Apply proper padding and spacing
                * For dashboard layouts, use a combination of flex and grid layouts appropriately:
                  - Use `flex flex-wrap` for rows of cards/metrics with negative margins to handle spacing
                  - Use `grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3` for responsive grid layouts
                  - Ensure proper gap between grid items with `gap-2` or similar
                * For charts, ensure the container has a defined height (e.g., `h-64` or `h-80`)
                * Apply proper responsive behavior using Tailwind's responsive prefixes
                """
            else:
                component_context = """
                **IMPORTANT CONTEXT:** You are generating a component that comes AFTER the other components like header and sidebar components but BEFORE the main content component.
                The header and sidebar components have already been generated and are available in the context.
                You can reference these components if needed, but ensure this component doesn't reference the main content component which hasn't been generated yet.

                **LAYOUT REQUIREMENTS:**
                * Use appropriate flex or grid properties based on the component's purpose
                * Ensure proper spacing and alignment
                * For card-like components, use `rounded-lg shadow-md` with appropriate padding
                * For list components, use `divide-y` for separators between items
                * Apply proper responsive behavior using Tailwind's responsive prefixes
                * For nested components, ensure proper parent-child relationships with appropriate CSS
                """

            specific_instructions = f"""
            **Core Task:** Generate the code for the Angular component named **`{component_name}`**.

            **Target Files:** Create the following files: {component_files_str}.

            {component_context}

            **Key Constraints & Requirements:**

            1.  **Angular Standalone Component:**
                *   The component MUST be **standalone** (`standalone: true` in the `@Component` decorator).
                *   Include all necessary imports directly in the component's TypeScript file. Automatically import `CommonModule` (for `*ngIf`/`*ngFor`), `FormsModule` (if `ngModel` is needed), `RouterModule` - for using routing-related directives like `routerLink`, `router-outlet`, etc., and any child components used.

            2.  **Pixel-Perfect UI from Image:**
                *   Reproduce the component's appearance **EXACTLY** as shown in the provided image(s).
                *   Match **all** visual details: layout, spacing, sizing, colors, fonts, borders, shadows, etc.
                *   Use **Tailwind CSS** utility classes directly within the HTML template (`.html`) for all styling. Adhere strictly to the visual representation in the image based on the design system files in the context. Reference theme values from `tailwind.config.js` if applicable from the context. Minimize or avoid custom CSS files unless absolutely necessary for pixel-perfect replication. Ensure responsiveness if implied by the design.

            3.  **Exact Data & Content from Image:**
                *   Use the **EXACT** text, labels, numbers, and data values visible in the image.
                *   **NO PLACEHOLDERS.** Implement all content precisely as shown.
                *   Create mock/dummy data directly within the component's TypeScript file that is a **PRECISE REPLICA** of the data presented in the image. Do **NOT** simulate API calls or services for data fetching; use the static, image-derived data.

            4.  **Data Modeling (Inside Component TS):**
                *   "Whenever declaring a model to specify a type or interface, use optional chaining (?.) to prevent runtime errors when accessing properties of the model, especially in cases where the model might be undefined or null."
                *   Define necessary TypeScript `interface` or `type` definitions **directly within the component's `.ts` file**. Do **NOT** reference external `models.ts` files.
                *   These interfaces/types MUST **exactly mirror** the structure and property names implied by the data shown in the image. No extra fields, no missing fields *visibly present* in the image data.
                *   Use the optional modifier (`?`) ONLY if a property is clearly absent or empty for *some* (but not all) items shown in the image. If a property is present for all displayed items, it MUST be required.
                *   Export a constant for the dummy data using `UPPER_SNAKE_CASE` (e.g., `IMAGE_DATA`). This constant must hold the exact data extracted from the image.
                *   Use `PascalCase` for interface/type names (e.g., `UserItem`, `ChartDataPoint`).
                *   Ensure all defined interfaces, types, and the dummy data constant are exported from the `.ts` file if potentially needed elsewhere (though primarily for internal use).

            5.  **Technology Stack:**

                - **Framework:** Angular v17+ — use modern features like **standalone components**, **`@Input()` / `@Output()`**, and **control flow syntax** (`@if`, `@for`, etc.) when appropriate.  
                - **Styling:** Use **Tailwind CSS** utility classes directly in the `.html` template.  
                - **Mock Data:** Assume mock/static data — do not implement HTTP calls or services unless specified.

                ---

                ### **Font Awesome Icons (MANDATORY + CONSISTENT):**

                #### Usage Guidelines:

                1. **TypeScript Imports:**
                   ```ts
                   import {{ FontAwesomeModule }} from '@fortawesome/angular-fontawesome';
                   import {{ faHome, faSearch, faUser, faCog }} from '@fortawesome/free-solid-svg-icons';
                   import {{ faCalendar, faBell }} from '@fortawesome/free-regular-svg-icons';
                   import {{ faTwitter, faFacebook }} from '@fortawesome/free-brands-svg-icons';
                   ```
                2. **Component Class Setup:**
                   ```ts
                   export class MyComponent {{
                     faHome = faHome;
                     faSearch = faSearch;
                     // Define only the icons you actually use
                   }}
                   ```
                3. **Template Usage:**
                   ```html
                   <fa-icon [icon]="faHome"></fa-icon>
                   ```
                4. **Module Imports:**  
                   Add `FontAwesomeModule` to the component's `imports` array in its `@Component()` metadata.

                > **IMPORTANT:** Do not mix icon libraries or use raw SVGs. Always stick to Font Awesome.

                ---

                ### **ApexCharts (CRITICAL – Charts Setup)**

                #### Imports & Module Setup:

                - **TypeScript Imports:**
                  ```ts
                  import {{ NgApexchartsModule }} from 'ng-apexcharts';
                  import {{
                    ApexAxisChartSeries,
                    ApexChart,
                    ApexXAxis,
                    ApexTitleSubtitle,
                    ApexStroke,
                    ApexDataLabels,
                    ApexFill,
                    ApexYAxis,
                    ApexTooltip,
                    ApexLegend,
                    ApexMarkers,
                    ApexGrid,
                    ApexTheme,
                    ApexNonAxisChartSeries,
                    ApexResponsive,
                    ApexPlotOptions
                  }} from 'ng-apexcharts';
                  ```
                - **Module Imports:**  
                  Add `NgApexchartsModule` to the component's `imports` array.

                ---

                ### **Chart Options Setup:**

                #### 1. Define a Strongly Typed Interface:
                ```ts
                export interface ChartOptions {{
                  series: ApexAxisChartSeries;
                  chart: ApexChart;
                  xaxis: ApexXAxis;
                  yaxis: ApexYAxis | ApexYAxis[];
                  stroke: ApexStroke;
                  tooltip: ApexTooltip;
                  dataLabels: ApexDataLabels;
                  fill: ApexFill;
                  colors: string[];
                  legend: ApexLegend;
                  markers: ApexMarkers;
                  grid: ApexGrid;
                  title?: ApexTitleSubtitle;
                }}
                ```

                #### 2. Initialize `chartOptions` to Avoid TS2564:
                ```ts
                chartOptions: ChartOptions = {{
                  series: [],
                  chart: {{ type: 'line', height: 350 }},
                  xaxis: {{ categories: [] }},
                  yaxis: {{}},
                  stroke: {{}},
                  tooltip: {{}},
                  dataLabels: {{ enabled: false }},
                  fill: {{}},
                  colors: [],
                  legend: {{}},
                  markers: {{}},
                  grid: {{}},
                  title: {{ text: '' }}
                }};
                ```

                #### 3. Strong Typing for Dynamic Data (Avoid TS7053):
                ```ts
                type Period = 'daily' | 'weekly' | 'monthly';
                const selectedPeriod: Period = 'daily';
                const seriesData = STOCK_CHART_DATA.seriesData[selectedPeriod];
                ```

                ---

                ### **Chart Template Setup:**

                ```html
                <div id="chart" class="h-64">
                  <apx-chart
                    [series]="chartOptions.series"
                    [chart]="chartOptions.chart"
                    [xaxis]="chartOptions.xaxis"
                    [yaxis]="chartOptions.yaxis"
                    [stroke]="chartOptions.stroke"
                    [tooltip]="chartOptions.tooltip"
                    [dataLabels]="chartOptions.dataLabels"
                    [fill]="chartOptions.fill"
                    [colors]="chartOptions.colors"
                    [legend]="chartOptions.legend"
                    [markers]="chartOptions.markers"
                    [grid]="chartOptions.grid"
                  ></apx-chart>
                </div>
                ```

                ---

                ### **Design System Color Usage:**

                > **IMPORTANT:**  
                > - **Pull all colors** (for fills, strokes, markers, grid lines, etc.) **directly from your Tailwind configuration or `styles.css` design system files**.  
                > - Ensure the chart's colors match the design **as closely as possible** to the reference image.  

                ---

                ### **Additional Notes:**

                - **Container Height:** Set a fixed height (e.g., `h-64`, `h-80`).  
                - **Gradient Fills & Animations:** Use ApexCharts' gradient and animation settings when shown in the design.  
                - **Responsiveness:** Set `chart.width = '100%'` and configure `responsive` options.  
                - **Axes, Tooltips, Legends, Markers, Grid:** Match formatting and styling from the reference image.  
                - **Time Period Selection:** If needed, implement with Angular event bindings (`(click)`, `(change)`, etc.).

                ---

                Let me know if you'd like a starter boilerplate component stub based on these guidelines!

            6.  **Consistency with Previously Generated Components:**
                *   **CRITICAL:** Review the available context code to understand the structure and styling of previously generated components.
                *   Ensure your component is consistent with the existing components in terms of styling, naming conventions, and overall architecture.
                *   If your component needs to interact with previously generated components, make sure to reference them correctly.
                *   Avoid duplicating functionality that already exists in previously generated components.

            7.  **TypeScript Type Safety:**
                *   **CRITICAL - TYPESCRIPT BEST PRACTICES:**
                    * Always use proper TypeScript types for all variables, parameters, and return values
                    * Use interfaces for complex data structures
                    * For chart configurations, use the specific chart library interfaces (e.g., `ChartOptions` for ApexCharts)
                    * Add type guards when working with potentially undefined values:
                      ```typescript
                      // INCORRECT - Don't do this
                      const value = someFunction();
                      return '$' + value.toFixed(2);

                      // CORRECT - Do this instead
                      const value = someFunction();
                      return typeof value === 'number' ? '$' + value.toFixed(2) : value;
                      ```
                    * Use non-null assertions (`!`) only when you're absolutely certain a value cannot be null/undefined
                    * For callback functions in chart configurations, always include proper type annotations:
                      ```typescript
                      tooltip: {{
                        formatter: function(value: number): string {{
                          return '$' + value.toFixed(2);
                        }}
                      }}
                      ```
                    * When accessing object properties that might be undefined, use optional chaining (`?.`)
                    * Use proper type casting with `as` when necessary, but prefer type guards when possible
                    * For event handlers, specify the event type:
                      ```typescript
                      handleClick(event: MouseEvent): void {{
                        // Handle click event
                      }}
                      ```

            **File-Specific Instructions:**

            *   **TypeScript (`.ts`):**
                *   Define the `@Component` decorator with `standalone: true`, `selector`, `templateUrl`, `styleUrls` (or `styles` if minimal), and `imports` array.
                *   Implement component properties/state using the exact dummy data constant.
                *   Define component logic and any necessary event handler methods (e.g., `onClick()`, `onSubmit()`).
                *   Define and export interfaces/types and the dummy data constant as specified above.
            *   **HTML (`.html`):**
                *   Structure the markup to be a pixel-perfect match to the image.
                *   Use Angular's template syntax (`{{ }}`, `[]`, `()`, `*ngIf`, `*ngFor`, etc.) correctly.
                *   Apply Tailwind CSS classes directly to elements for styling.
                *   Use the EXACT text, labels, and values from the image.
            *   **Styling (`.css`/`.scss`, if necessary):**
                *   **CRITICAL - CSS BEST PRACTICES:**
                    * Primarily use Tailwind CSS utility classes directly in the HTML template
                    * Do NOT use Tailwind's @apply directive in component CSS files - it's not supported in the default Angular setup
                    * For chart styling that requires CSS, use proper Angular ::ng-deep syntax with specific selectors:
                      ```css
                      :host ::ng-deep .apexcharts-canvas {{
                        width: 100% !important;
                        height: 100% !important;
                      }}
                      ```
                    * For component-specific styles that can't be achieved with Tailwind, use standard CSS properties instead of @apply:
                      ```css
                      /* INCORRECT - Don't use this */
                      .element {{
                        @apply flex h-screen overflow-hidden;
                      }}

                      /* CORRECT - Use this instead */
                      .element {{
                        display: flex;
                        height: 100vh;
                        overflow: hidden;
                      }}
                      ```
                    * Keep external CSS minimal and focused on specific needs that Tailwind can't address
                    * For responsive styles, use standard media queries or Tailwind's responsive utility classes
                    * Ensure all CSS selectors are specific to avoid conflicts
            """

        elif batch_type == "root":
            specific_instructions = """
            **Core Task:** Generate the root-level files that are necessary for the application to run, integrating all previously generated components.

            **IMPORTANT CONTEXT:** You are generating the ROOT files AFTER all components (header, sidebar, other components, and main content) have been generated sequentially.
            All components are available in the context code and should be properly integrated into the root files.

            **Key Requirements:**

            1. **Component Integration:**
               * Review ALL previously generated components in the context code to understand their structure and dependencies.
               * Integrate ALL components in a logical layout structure, with header, sidebar, and main content in their appropriate positions.
               * Ensure proper routing configuration if the application requires multiple views.

            2. **Duplicate Prevention:**
               * **CRITICAL:** Do NOT create duplicate components or reimplementations of existing components.
               * If you find multiple components with similar functionality, choose the most appropriate one and use it consistently.
               * Reference existing components by their exact selectors as defined in their respective TypeScript files.

            3. **Technical Requirements:**
               * Ensure all necessary imports and dependencies are included in the root files.
               * Follow Angular best practices for application structure and component organization.
               * Configure proper module imports for all standalone components.
               * Set up proper routing if required by the application design.

            4. **File Structure:**
               * Generate complete and syntactically correct root files (app.component.ts, app.component.html, app.routes.ts, etc.).
               * Ensure the root files are properly structured and follow best practices for Angular.
               * Include appropriate error handling and loading states if needed.

            5. **Consistency:**
               * Maintain consistent styling and behavior across the entire application.
               * Ensure the application has a cohesive look and feel by properly integrating all components.

            6. **CRITICAL - Root Layout Structure:**
               * The root app component MUST use a proper layout structure with these exact Tailwind classes:
               ```html
               <div class="flex h-screen overflow-hidden"> <!-- Root container -->
                 <!-- Header component goes here -->
                 <div class="flex flex-1 overflow-hidden"> <!-- Main content wrapper -->
                   <!-- Sidebar component goes here -->
                   <main class="flex-1 overflow-auto p-4"> <!-- Main content area -->
                     <!-- Router outlet or main content components go here -->
                   </main>
                 </div>
               </div>
               ```
               * Ensure the header spans the full width of the screen and is positioned at the top
               * Ensure the sidebar is positioned correctly beside the main content area
               * Ensure the main content area takes up the remaining space with proper overflow handling
               * Use proper z-index values to ensure correct layering of components
               * Apply appropriate responsive behavior for different screen sizes

               * In the root component, ensure the following imports:
               ```
               // In app.component.ts
               import { Component } from '@angular/core';
               import { RouterOutlet } from '@angular/router';
               import { CommonModule } from '@angular/common';

               // Import icon modules if needed in the root component
               import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';

               // Import chart modules if needed in the root component
               import { NgApexchartsModule } from 'ng-apexcharts';

               // Import all generated components with their exact names and paths
               import { HeaderComponent } from './components/header/header.component';
               import { SidebarComponent } from './components/sidebar/sidebar.component';
               // ... other component imports

               @Component({
                 selector: 'app-root',
                 standalone: true,
                 imports: [
                   CommonModule,
                   RouterOutlet,
                   FontAwesomeModule, // Include if using Font Awesome icons
                   NgApexchartsModule, // Include if using ApexCharts in the root component
                   HeaderComponent,
                   SidebarComponent,
                   // ... other component imports
                 ],
                 templateUrl: './app.component.html',
                 styleUrls: ['./app.component.css']
               })
               export class AppComponent {
                 title = 'angular-app';
                 // Add any root component properties or methods here
               }
               ```
            """

        # --- 5. Output Format & Constraints ---
        output_req = f"""
        # OUTPUT REQUIREMENTS & CONSTRAINTS:

        *   **Strict JSON Output:** Your output **MUST** be a single, valid JSON object.
        *   **JSON Structure:** The JSON object's keys **MUST** be the exact `Target Files` paths provided in the INPUTS section. The values **MUST** be strings containing the complete, generated code content for the corresponding file path key.
        *   **Complete Code:** Each file's content string must be the full source code, syntactically correct for its file type ({framework}, HTML, CSS, JSON, etc.).
        *   **No Extra Text:** Output **ONLY** the raw JSON object. Do **NOT** include ```json markdown markers, introductory sentences, explanations, apologies, or any text outside the JSON structure itself.
        *   **Adherence to Context:** Base the generated code *solely* on the `Detailed Analysis Summary` and utilize the patterns/styles from `Available Context Code` and the specified `{framework}/{design_library}`. Do not invent features or styles not described.
        *   **Code Quality Requirements:**
            * **Readability:** Write clean, well-formatted code with consistent indentation and meaningful variable/function names.
            * **Comments:** Include appropriate comments for complex logic, but avoid excessive commenting of obvious code.
            * **Error Handling:** Implement proper error handling where appropriate.
            * **Type Safety:** Use strong typing (TypeScript interfaces, types) for all data structures.
            * **Modularity:** Ensure components and services are modular and follow the single responsibility principle.
        *   **Placeholders for Uncertainty:** If the provided information is insufficient to fully implement a specific part of a file, use clear, standard placeholders (e.g., `// TODO: Implement logic based on API response structure`, `<!-- Placeholder for complex form elements -->`) within the code. **Still output the complete file structure.** If you cannot generate a file *at all* due to missing critical information, output its filename as the key and a single-line comment string like `// ERROR: Insufficient information in the summary to generate this file.` as the value.
        *   **Framework Best Practices:** Ensure the generated code follows idiomatic patterns and conventions for **{framework.capitalize()}**.
        *   **Consistency:** Maintain consistency with existing code in the context. Use the same naming conventions, code style, and patterns.
        """

        # --- Combine Prompt Sections ---
        prompt = f"{role}{goal}{inputs}{instructions_header}{specific_instructions}{output_req}"
        return prompt
