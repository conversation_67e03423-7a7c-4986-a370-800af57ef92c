from typing import List
import wcag_contrast_ratio as wcag

from src.model.base_schema import ColorContrastItem


def hex_to_rgb_normalized(hex_color):
    """Convert hex color to normalized RGB tuple (values between 0.0 and 1.0)."""
    hex_color = hex_color.lstrip("#")
    return tuple(int(hex_color[i : i + 2], 16) / 255.0 for i in (0, 2, 4))


def check_contrast_ratio(
    text_background_pairs: List[ColorContrastItem],
) -> List[ColorContrastItem]:
    """Check contrast ratio for the given text and background color pairs."""
    results = []

    for pair in text_background_pairs:
        text = pair["text"]
        text_color = pair["textColor"]
        background_color = pair["backgroundColor"]

        # Assuming hex_to_rgb_normalized and wcag functions are defined elsewhere
        text_rgb = hex_to_rgb_normalized(text_color)
        background_rgb = hex_to_rgb_normalized(background_color)

        contrast_ratio = wcag.rgb(text_rgb, background_rgb)

        meets_aa = wcag.passes_AA(contrast_ratio)
        meets_aaa = wcag.passes_AAA(contrast_ratio)

        result = ColorContrastItem(
            text=text,
            textColor=text_color,
            backgroundColor=background_color,
            wcag_aa=meets_aa,
            wcag_aaa=meets_aaa,
            result=meets_aaa and meets_aa,
        )

        results.append(result)

    return results


# List of text and background color pairs
color_pairs = [
    {"text": "Welcome Back", "textColor": "#5F3DC4", "backgroundColor": "#FFFFFF"},
    {
        "text": "Please sign in to continue to your account.",
        "textColor": "#6C757D",
        "backgroundColor": "#FFFFFF",
    },
    {
        "text": "Sign in to your account",
        "textColor": "#000000",
        "backgroundColor": "#FFFFFF",
    },
    {
        "text": "Enter username or email",
        "textColor": "#6C757D",
        "backgroundColor": "#FFFFFF",
    },
]

# Check contrast ratio for each pair
check_contrast_ratio(color_pairs)
