import cv2
import numpy as np
import base64
from io import BytesIO
from PIL import Image


def apply_color_blindness_simulation(image, matrix):
    """Apply color blindness simulation using the given transformation matrix."""
    # Convert image to float and normalize to [0, 1]
    image = image / 255.0

    # Apply the transformation matrix
    transformed_image = np.dot(image, matrix.T)

    # Clip values to stay within [0, 1] range
    transformed_image = np.clip(transformed_image, 0, 1)

    # Convert back to 8-bit integer format
    transformed_image = (transformed_image * 255).astype(np.uint8)

    return transformed_image


def simulate_color_blindness(data_uri):
    # Decode Data URI to image
    encoded_data = data_uri.split(",")[1]
    nparr = np.frombuffer(base64.b64decode(encoded_data), np.uint8)
    image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)  # Convert BGR to RGB

    # Define color blindness matrices
    protanopia_matrix = np.array(
        [[0.567, 0.433, 0], [0.558, 0.442, 0], [0, 0.242, 0.758]]
    )

    deuteranopia_matrix = np.array([[0.625, 0.375, 0], [0.7, 0.3, 0], [0, 0.3, 0.7]])

    tritanopia_matrix = np.array(
        [[0.95, 0.05, 0], [0, 0.433, 0.567], [0, 0.475, 0.525]]
    )

    # Apply simulations
    protanopia_image = apply_color_blindness_simulation(image_rgb, protanopia_matrix)
    deuteranopia_image = apply_color_blindness_simulation(
        image_rgb, deuteranopia_matrix
    )
    tritanopia_image = apply_color_blindness_simulation(image_rgb, tritanopia_matrix)

    # Convert the results to Data URIs
    def convert_to_data_uri(img_array):
        pil_img = Image.fromarray(img_array)
        buffered = BytesIO()
        pil_img.save(buffered, format="PNG")
        encoded_img = base64.b64encode(buffered.getvalue()).decode("utf-8")
        return f"data:image/png;base64,{encoded_img}"

    # Return Data URIs for each simulated image
    protanopia_data_uri = convert_to_data_uri(protanopia_image)
    deuteranopia_data_uri = convert_to_data_uri(deuteranopia_image)
    tritanopia_data_uri = convert_to_data_uri(tritanopia_image)

    return {
        "deuteranopia": deuteranopia_data_uri,
        "tritanopia": tritanopia_data_uri,
        "protanopia": protanopia_data_uri,
    }
