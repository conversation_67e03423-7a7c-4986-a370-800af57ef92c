import json
from src.settings.settings import DACreds
from src.model.request_schemas import DARequest, DesignAnalyzerRequest
from src.model.base_schema import AnalysisResult
from src.utils.api_helper.api_client import APIClient
from src.services.design_analysis.color_blindness_test import simulate_color_blindness
from src.services.design_analysis.color_contrast import check_contrast_ratio
from src.utils.design_analyzer.image_utils import overlay_grid
from src.utils.design_generation.parser import (
    get_content_inside_markdown,
    validate_response,
)

da_creds = DACreds()


async def get_design_analysis(design: DesignAnalyzerRequest):

    # Perform Color contrast test
    # Perform color blindness test
    cb_simulations = simulate_color_blindness(design.datauri)
    # Create grid overlay
    grid_overlay = overlay_grid(design.datauri, 50)
    images = [
        design.datauri,
        grid_overlay,
        cb_simulations["tritanopia"],
        cb_simulations["protanopia"],
        cb_simulations["deuteranopia"],
    ]
    api_client = APIClient(
        da_creds.DA_BASE_URL, headers={"access-key": da_creds.DA_ACCESS_KEY}
    )
    payload = DARequest(
        mode="MLO_DESIGN_ANALYZER",
        promptOverride=False,
        userSignature=da_creds.DA_USER_SIGNATURE,
        useCaseIdentifier="MLO_DESIGN_ANALYZER" + da_creds.DA_USECASE_IDENTIFIER,
        image=images,
        prompt="Provide the design analysis.",
    )
    response = await api_client.post(
        da_creds.DA_API_ENDPOINT, data=payload.model_dump()
    )
    validated_response = validate_response(response)

    if validated_response is None:
        return {"status_code": 500, "error": "Invalid response format"}
    json_string = get_content_inside_markdown(
        validated_response.response.choices[0].text, "json"
    )
    response_as_json = json.loads(json_string)
    color_contrast_test_result = check_contrast_ratio(response_as_json["colorContrast"])
    result = AnalysisResult(**response_as_json)
    result.colorContrast = color_contrast_test_result
    result.gridImage = grid_overlay
    for blindness_type in result.colorBlindnessTest:
        blindness_type.datauri = cb_simulations[blindness_type.type.lower()]
    return result
    # Perform fitts test
    # Perform typography analysis
    # Provide suggestions for improvement
