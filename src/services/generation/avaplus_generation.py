# src/services/generation/digital_ascendar_generation.py
import json
from src.exceptions.exception import <PERSON><PERSON>erverError, DAValueError
from src.model.response_schemas import DAResponse
from src.services.generation.base_generation import BaseGeneration
from src.settings.settings import DACreds
from src.utils.api_helper.api_client import APIClient
from src.utils.decorators.avaplus_decorator import handle_exceptions_and_log
from src.utils.helpers.text_parser import get_content_inside_markdown
from src.utils.logger import AppLogger
from src.model.request_schemas import DARequest

logger = AppLogger(__name__).get_logger()


class AVAPlusGeneration(BaseGeneration):
    def __init__(self) -> None:
        # Lazy initialization - credentials will be loaded when needed
        self._credentials = None
        self._api_client = None

    @property
    def credentials(self):
        """Lazy load credentials"""
        if self._credentials is None:
            self._credentials = DACreds()
        return self._credentials

    @property
    def api_client(self):
        """Lazy load API client"""
        if self._api_client is None:
            self._api_client = APIClient(
                self.credentials.DA_BASE_URL, {"access-key": self.credentials.DA_ACCESS_KEY}
            )
        return self._api_client

    @handle_exceptions_and_log(logger)
    async def generate(self, payload: dict, extract_markdown: str = None):
        response = await self.api_client.post(
            self.credentials.DA_API_ENDPOINT, data=payload
        )

        response = DAResponse(**response)
        choices = response.response.choices
        if not choices:
            logger.error("No choices found in the DA response")
            raise DAServerError("No choices found in the DA response")

        code = choices[0].text

        if not code:
            logger.error("No text found in the choice")
            raise DAValueError("No text found in the choice")

        if extract_markdown is not None:
            code = get_content_inside_markdown(
                data=code, markdown=extract_markdown, find_last=False
            )
            if code == -1:
                return await self.get_complete_response(response, extract_markdown)
            elif code == -2:
                return self.extract_full_response(response, extract_markdown)

        return code

    @handle_exceptions_and_log(logger)
    async def get_complete_response(self, partial_response: DAResponse, until: str):
        prompt = (
            "User : "
            + partial_response.response.executedPrompt
            + "\n"
            + "AI :"
            + partial_response.response.choices[0].text
        )
        retry_request = DARequest(
            mode="MLO_RESPONSE_COMPLETER",
            promptOverride=False,
            useCaseIdentifier=f"MLO_RESPONSE_COMPLETER{self.credentials.DA_USECASE_IDENTIFIER}",
            userSignature=self.credentials.DA_USER_SIGNATURE,
            image=None,
            prompt=prompt,
        )
        return await self.generate(retry_request, until)

    def extract_full_response(self, response: DAResponse, markdown: str):
        logger.warning("Merging responses")
        return get_content_inside_markdown(
            data=response.response.executedPrompt + response.response.choices[0].text,
            markdown=markdown,
            find_last=True,
        )

    @handle_exceptions_and_log(logger)
    async def fix_json(self, error: str, json: str):
        json_fix_data = DARequest(
            mode="MLO_FIX_JSON",
            promptOverride=False,
            userSignature=self.credentials.DA_USER_SIGNATURE,
            image=None,
            prompt=json + "\nHere is the error : " + error,
            useCaseIdentifier=f"MLO_FIX_JSON{self.credentials.DA_USECASE_IDENTIFIER}",
        )
        corrected_json = await self.generate(json_fix_data.model_dump(), "json")
        return corrected_json

    @handle_exceptions_and_log(logger)
    async def handle_json(self, json_text):
        try:
            data = json.loads(json_text)
            return data
        except json.JSONDecodeError as e:
            corrected_json = await self.fix_json(e.msg, json_text)
            corrected_data = json.loads(corrected_json)
            return corrected_data

    @handle_exceptions_and_log(logger)
    def prepare_payload(self, data: dict):
        return DARequest(
            mode=data.mode,
            promptOverride=data.promptOverride,
            useCaseIdentifier=data.mode + data.useCaseIdentifier,
            userSignature=data.userSignature,
            image=data.image,
            prompt=data.prompt,
        )
