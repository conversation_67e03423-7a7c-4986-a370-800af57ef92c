# src/services/generation/azure_generation.py
from src.services.generation.base_generation import BaseGeneration

# from src.settings.settings import AzureOpenAICreds
# from src.utils.api_helper.api_client import APIClient
from src.utils.logger import AppLogger
from src.model.request_schemas import DARequest


class AzureGeneration(BaseGeneration):
    def __init__(self) -> None:
        # self.credentials = AzureOpenAICreds()
        # self.api_client = APIClient(
        #     self.credentials.AZURE_BASE_URL,
        #     {"access-key": self.credentials.AZURE_ACCESS_KEY},
        # )
        self.logger = AppLogger("azure_generation").get_logger()

    async def generate(self, data: DARequest, extract_markdown: str = None):
        # Azure-specific implementation
        pass
