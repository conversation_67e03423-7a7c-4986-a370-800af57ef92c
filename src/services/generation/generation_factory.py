# src/services/generation/generation_factory.py
from src.services.generation.azure_generation import AzureGeneration
from src.services.generation.avaplus_generation import AVAPlusGeneration


class GenerationFactory:
    @staticmethod
    def get_generation(provider: str):
        if provider == "azure":
            return AzureGeneration()
        elif provider == "avaplus":
            return AVAPlusGeneration()
        else:
            raise ValueError(f"Unknown provider: {provider}")
