# src/enums/error_codes.py
from enum import Enum


class APIErrorCode(str, Enum):
    # Generic Errors
    INTERNAL_SERVER_ERROR = "INTERNAL_SERVER_ERROR"
    VALIDATION_ERROR = "VALIDATION_ERROR"
    AUTHENTICATION_FAILED = "AUTHENTICATION_FAILED"
    PERMISSION_DENIED = "PERMISSION_DENIED"
    RESOURCE_NOT_FOUND = "RESOURCE_NOT_FOUND"
    METHOD_NOT_ALLOWED = "METHOD_NOT_ALLOWED"
    REQUEST_TIMEOUT = "REQUEST_TIMEOUT"
    TOO_MANY_REQUESTS = "TOO_MANY_REQUESTS"

    # Business Logic Errors (General)
    BUSINESS_LOGIC_ERROR = "BUSINESS_LOGIC_ERROR"  # Generic for DAValueError

    # Specific Feature Errors (Examples - add your own)
    USER_ALREADY_EXISTS = "USER_ALREADY_EXISTS"
    INVALID_INPUT_PARAMETER = "INVALID_INPUT_PARAMETER"
    EXTERNAL_SERVICE_UNAVAILABLE = "EXTERNAL_SERVICE_UNAVAILABLE"

    # Debugging / Development
    DEBUG_NOT_IMPLEMENTED = "DEBUG_NOT_IMPLEMENTED"
    DEBUG_PLACEHOLDER = "DEBUG_PLACEHOLDER"

    # You can add more specific codes as your application grows
    DB_CONNECTION_ERROR = "DB_CONNECTION_ERROR"
