from pydantic import BaseModel
from .base_schema import (
    BaseResponse,
    BaseSchema,
    BrandCustomizationDetails,
    GeneratedDesign,
    ResponseContent,
    Choice,
)
from typing import List, Optional, Any, Literal
from pydantic import Field


# General Response Models
class HeartbeatResponse(BaseResponse):
    status: str


class DataResponse(BaseResponse):
    data: dict


class ExampleResponse(BaseSchema):
    id: int
    name: str
    age: int
    message: str


class Response(BaseModel):
    id: str
    executedPrompt: str
    object: str
    choices: List[Choice]


# DA Response Models
class DAResponse(BaseModel):
    response: Response


# HTML Content Response Models
class HTMLContent(BaseModel):
    html: str


# Code Response Models
class CodeResponse(BaseModel):
    status_code: int
    code: str


class CodeImageResponse(BaseModel):
    status_code: int
    code: str


# Image Response Models
class ImageBase64Response(BaseModel):
    status_code: int
    image_base64: str
    error: str = None


# Image to Code Response Models
class ImageToCodeResponse(BaseModel):
    id: str = Field(default_factory=str, alias="_id")
    prompt: Optional[str] = None
    projectName: str
    projectType: Literal["NEW_PRODUCT", "LEGACY_MODERNIZATION"]
    author: str
    image: Optional[List[str]] = None
    code: Optional[str] = None
    createdAt: Optional[str] = None
    updatedAt: str
    history: Optional[List[str]] = None


class ImageToCodeRecordResponse(BaseModel):
    id: str
    projectName: str


# Wireframe Response Models
class WireframeDetailsResponse(BaseModel):
    id: str = Field(default_factory=str, alias="_id")
    appName: str
    prompt: str
    industry: str
    designSystem: str
    brandCustomization: Optional[BrandCustomizationDetails]
    userRequests: Optional[List[str]]
    generatedDesign: Optional[List[GeneratedDesign]]
    createdAt: Optional[str] = None
    updatedAt: str


class WireframeMetaDataResponse(BaseModel):
    id: str
    appName: str


class Wireframe_PageSection(BaseModel):
    id: int
    name: str
    description: str
    content: List[Any]


class Wireframe_WebPage(BaseModel):
    name: str
    pageDescription: str
    sections: List[Wireframe_PageSection]


class Wireframe_BrandCustomization(BaseModel):
    logo: str
    colorPalette: dict  # {"primary": str, "secondary": str, "tertiary": str}


class Wireframe_Pages_Description(BaseModel):
    description: str


class Wireframe_WebsiteTheme(BaseModel):
    theme: str
    brandCustomization: Wireframe_BrandCustomization
    pages: List[Wireframe_WebPage]


# Research Response Models
class ResearchResponse(BaseModel):
    status_code: int
    response: ResponseContent


class JobStateData(BaseModel):
    status: str
    log: str
    progress: str
    progress_description: str
    history: List
    metadata: List
    prev_metadata: List


class JobStateResponse(BaseModel):
    status_code: int
    details: JobStateData


# Created Design Response Models
class CreatedDesignResponse(BaseModel):
    id: str
    model_config = {
        "populate_by_name": True,
        "alias_generator": lambda field_name: (
            "_id" if field_name == "id" else field_name
        ),
    }
