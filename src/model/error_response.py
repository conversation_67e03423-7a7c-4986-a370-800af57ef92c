# src/schemas/error_response.py
from typing import Optional, List, Union, Any, Dict
from pydantic import BaseModel, Field

from src.model.error_codes import APIErrorCode


class ErrorDetail(BaseModel):
    loc: Optional[List[Union[str, int]]] = None
    msg: str
    type: str
    ctx: Optional[Dict[str, Any]] = None  # Context for validation errors


class StandardErrorResponse(BaseModel):
    error_code: APIErrorCode = Field(
        ..., description="Application-specific error code."
    )
    message: str = Field(
        ...,
        description="A human-readable message providing more details about the error.",
    )
    details: Optional[Union[List[ErrorDetail], str, Dict[str, Any]]] = Field(
        None,
        description="Optional detailed information, like validation errors or other context.",
    )

    class Config:
        use_enum_values = (
            True  # Important for FastAPI to serialize Enum members to their values
        )
