# Imports
from typing import Optional, List, Dict, Any, Tuple
from pydantic import BaseModel, Field
from bson import ObjectId
from datetime import datetime
from enum import Enum
import cv2
import numpy as np
import os
from dataclasses import dataclass


# Base Models
class BaseResponse(BaseModel):
    status: str
    message: Optional[str] = None


class BaseSchema(BaseModel):
    class Config:
        from_attributes = True


# Payload Models
class UserPayload(BaseModel):
    mode: Optional[str] = None
    useCaseIdentifier: Optional[str] = None
    userSignature: Optional[str] = None
    prompt: Optional[str] = None
    image: Optional[List[str]] = None
    conversations: Optional[List[str]] = None


class UpdatePayload(BaseModel):
    prompt: Optional[str] = None
    projectName: Optional[str] = None
    projectType: Optional[str] = None
    image: Optional[List[str]] = None
    code: Optional[str] = None
    history: Optional[List[str]] = None
    updatedAt: Optional[datetime] = None
    appName: Optional[str] = None
    industry: Optional[str] = None
    designSystem: Optional[str] = None
    brandCustomization: Optional[str] = None
    generatedDesign: Optional[str] = None
    userRequests: Optional[List[str]] = None


class FinalPayload(BaseModel):
    processedPages: Optional[List[Dict[str, Any]]] = None
    remainingPages: Optional[Dict[str, Any]] = None


# Custom ObjectId Handling
class PyObjectId(ObjectId):
    @classmethod
    def __get_pydantic_json_schema__(cls, schema: dict) -> dict:
        schema.update({"type": "string", "example": "5f7f7f7f7f7f7f7f7f7f7f7f"})
        return schema

    @classmethod
    def __get_validators__(cls):
        yield cls.validate
        yield cls.to_string

    @classmethod
    def validate(cls, v):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid objectid")
        return ObjectId(v)

    @classmethod
    def to_string(cls, v):
        return str(v)


# Recipe Models
class RecipeSettings(BaseModel):
    temperature: Optional[float] = None


class Recipe(BaseModel):
    name: Optional[str] = None
    recipeSettings: Optional[RecipeSettings] = None


# Choice Model
class Choice(BaseModel):
    text: str
    index: str
    identifier: str


# Schema Models
class Schema_TemplateDetails(BaseModel):
    name: str
    descrption: str
    section_list: str
    code: str


class Schema_TemplateName(BaseModel):
    name: str = Field(description="Name of the Template")


class Schema_UserInput(BaseModel):
    project_name: str = Field(description="Project name provided by the user")
    description: str = Field(description="User description about the webpage in detail")
    webpage_sketch: str = Field(
        description="(optional) User hand-drawn image or wireframe of the webpage"
    )
    device: str = Field(description="Desktop or mobile")
    technology: str = Field(description="React JS or Angular")


class Schema_SectionItems(BaseModel):
    title: str = Field(description="User-provided title")
    description: str = Field(
        description="Description of style, section design, and format"
    )
    children: list[str,] = Field(
        description="List of all the child HTML tags of the section, including all nested child tags."
    )
    mandatory_children: list[str,] = Field(
        description="List of all the child HTML tags are always required in the section. Should be a subset of attribute 'children'"
    )


class Schema_Section(BaseModel):
    SectionName: str = Field(description="Title of the section")
    SectionItems: Schema_SectionItems = Field(
        description="Defines a class that stores details for each section."
    )


class Schema_WebProject(BaseModel):
    project_title: str = Field(description="Project title")
    section_list: list[Schema_Section] = Field(description="List of sections")


class Schema_SectionID(BaseModel):
    section_ID: str = Field(description="13 digit number that stored as a string")


class Schema_WebSiteInfo(BaseModel):
    project_title: str = Field(description="The title of the project.")
    num_of_pages: str = Field(
        description="The number of pages in the webpage, represented as a string."
    )
    is_static: str = Field(
        description="Indicates whether the webpage is static. This is a boolean value stored as strings 'True' and 'False'."
    )
    webpage_description: str = Field(
        description="A detailed description of the webpage."
    )
    webpage_features: list[str] = Field(
        description="A list of in-depth feature descriptions that describe the functionality available to users on the webpage and are required by the webpage."
    )
    is_db_required: str = Field(
        description="Indicates whether a database is required for the webpage. This is a boolean value stored as strings 'True' and 'False'."
    )


class Schema_WebSite_Page_Details(BaseModel):
    template_name: str = Field(description="Name of the Template")
    page_title: str = Field(description="The title of the page.")
    path: str = Field(description="Path of the page from root")
    is_static: str = Field(
        description="Indicates whether the page is static. This is a boolean value stored as strings 'True' and 'False'."
    )
    page_description: str = Field(description="A detailed description of the page.")
    page_features: list[str] = Field(
        description="A list of in-depth frontend feature descriptions that describe the functionality available to users on the page and are required by the page."
    )
    is_db_required: str = Field(
        description="Indicates whether a database is required for the page. This is a boolean value stored as strings 'True' and 'False'."
    )


class Schema_WebSite_Pages(BaseModel):
    template_name: str = Field(description="Name of the Template")
    pages: List[Schema_WebSite_Page_Details] = Field(
        description="List of object that stores details of page in a website."
    )


class Schema_WebSite_Page_Sections(BaseModel):
    template_name: str = Field(description="Name of the Template")
    page_title: str = Field(description="The title of the page.")
    path: str = Field(description="Path of the page from root")
    page_description: str = Field(description="A detailed description of the page.")
    section_list: list[Schema_Section] = Field(description="List of sections")


class Schema_WebPage_All_Page_Sections(BaseModel):
    pages: list[Schema_WebSite_Page_Sections] = Field(
        description="List of object that stores details of page in a website."
    )


# Update Project Request Model
class UpdateProjectRequest(BaseModel):
    js_content: str


# Image Data Model
class ImageData(BaseModel):
    type: str = Field(
        ...,
        example="base64",
        description="Type of image input: 'base64', 'path', or 'url'",
    )
    data: str = Field(
        ..., description="The base64 string, file path, or URL of the image"
    )


# Design System Models
class DesignSystem(BaseModel):
    id: Optional[str] = Field(default=None, alias="_id")
    type: str = Field(alias="Type")
    component: str = Field(alias="Component")
    description: str = Field(alias="Description")
    code: str = Field(alias="Code")
    domain: Optional[str] = Field(alias="Domain")
    design_system: Optional[str] = Field(alias="Design System")

    class Config:
        model_config = {
            "allow_population_by_field_name": True,
            "json_encoders": {ObjectId: str},
        }


# Design Analysis Models
class ColorContrastItem(BaseModel):
    text: str
    textColor: str
    backgroundColor: str
    wcag_aa: Optional[bool] = None
    wcag_aaa: Optional[bool] = None
    result: Optional[bool] = None


class AlignmentItem(BaseModel):
    element: str
    improvements: List[str]


class ColorBlindnessTestItem(BaseModel):
    type: str
    suggestions: List[str]
    datauri: Optional[str] = None


class AnalysisResult(BaseModel):
    colorContrast: List[ColorContrastItem]
    alignment: List[AlignmentItem]
    colorBlindnessTest: List[ColorBlindnessTestItem]
    gridImage: Optional[str] = None


# Generated Design Model
class GeneratedDesign(BaseModel):
    htmlCode: str
    design: str


# Brand Customization Models
class BrandCustomization(BaseModel):
    logo: str
    color_palette: List[str] = Field(alias="colorPalette")


class BrandCustomizationDetails(BaseModel):
    logo: str
    colorPalette: List[str] = Field(alias="colorPalette")


# Config Model
class Config:
    populate_by_name = True


# Response Content Model
class ResponseContent(BaseModel):
    type: str
    data: List[str]


# Conversation Model
class Conversation(BaseModel):
    content: Optional[str] = None
    role: Optional[str] = None


# Split Models
# class SplitType(Enum):
#     HORIZONTAL = 'h'
#     VERTICAL = 'v'
#     NONE = 'NA'

# class SplitPoint(BaseModel):
#     position: int
#     type: SplitType

# class SplitResult(BaseModel):
#     success: bool
#     large_sections: List[Tuple[int, str, str]]
#     section_areas: Dict[str, float]
#     split_type: SplitType

# Image Section Model
# class ImageSection(BaseModel):
#     height: int
#     width: int
#     area: int
#     path: str
#     image:np.ndarray

#     class Config:
#         arbitrary_types_allowed = True  # Allow arbitrary types

#     @classmethod
#     def from_path(cls, image_path: str):
#         """Create an ImageSection from a file path."""
#         image = cv2.imread(image_path)
#         if image is None:
#             raise ValueError(f"Could not read image at {image_path}")
#         return cls(image, image_path)

#     @classmethod
#     def from_section(cls, parent: 'ImageSection', y_start: Optional[int] = None, y_end: Optional[int] = None,
#                     x_start: Optional[int] = None, x_end: Optional[int] = None):
#         """Create a new ImageSection from a subsection of an existing one."""
#         y_start = y_start if y_start is not None else 0
#         y_end = y_end if y_end is not None else parent.height
#         x_start = x_start if x_start is not None else 0
#         x_end = x_end if x_end is not None else parent.width

#         section = parent.image[y_start:y_end, x_start:x_end]
#         return cls(section, parent.path)  # Note: path is just for reference

# # Optimal Result Models
# class OptimalHorizontalResult(BaseModel):
#     min_height: int
#     splits: List[int]

# class OptimalVerticalResult(BaseModel):
#     min_width: int
#     splits: List[int]

## Code generation Splitting Image schemas


class SplitType(Enum):
    HORIZONTAL = "h"
    VERTICAL = "v"
    NONE = "NA"


@dataclass
class SplitPoint:
    position: int
    type: SplitType


@dataclass
class SplitResult:
    success: bool
    large_sections: List[Tuple[int, str, str]]
    section_areas: Dict[str, float]
    split_type: SplitType


class ImageSection:
    def __init__(self, image: np.ndarray, path: str):
        self.image = image
        self.path = path
        self.height, self.width = image.shape[:2]
        self.area = self.height * self.width

    @classmethod
    def from_path(cls, image_path: str):
        """Create an ImageSection from a file path."""
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"Could not read image at {image_path}")
        return cls(image, image_path)

    @classmethod
    def from_section(
        cls,
        parent: "ImageSection",
        y_start: Optional[int] = None,
        y_end: Optional[int] = None,
        x_start: Optional[int] = None,
        x_end: Optional[int] = None,
    ):
        """Create a new ImageSection from a subsection of an existing one."""
        y_start = y_start if y_start is not None else 0
        y_end = y_end if y_end is not None else parent.height
        x_start = x_start if x_start is not None else 0
        x_end = x_end if x_end is not None else parent.width

        section = parent.image[y_start:y_end, x_start:x_end]
        return cls(section, parent.path)  # Note: path is just for reference

    def save(self, output_path: str) -> bool:
        """Save this image section to a file."""
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        success = cv2.imwrite(output_path, self.image)
        if not success:
            print(f"Failed to save image to {output_path}")
        return success


class ColorAnalyzer:
    @staticmethod
    def get_unique_colors_horizontal(
        image: np.ndarray, y_start: int, y_end: int
    ) -> int:
        """Count unique colors in a horizontal strip of the image."""
        strip = image[y_start:y_end, :]
        return len(np.unique(strip.reshape(-1, strip.shape[-1]), axis=0))

    @staticmethod
    def get_unique_colors_vertical(image: np.ndarray, x_start: int, x_end: int) -> int:
        """Count unique colors in a vertical strip of the image."""
        strip = image[:, x_start:x_end]
        return len(np.unique(strip.reshape(-1, strip.shape[-1]), axis=0))


class ProjectStatusV2(BaseModel):
    progress: str
    status: str
    log: str
    progress_description: str
    history: str
    metadata: str
    # Using List[dict] to represent a list of JSON objects.
    # updated_at: datetime

    class Config:
        # Add any custom configuration here, like ORM mode if needed.
        use_enum_values = (
            False  # In case you want to use Enums for `progress` and `status`
        )
