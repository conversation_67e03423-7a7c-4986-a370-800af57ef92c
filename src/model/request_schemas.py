from datetime import datetime
from enum import Enum
import json
from typing import Any, List, Literal, Optional

from pydantic import BaseModel, Field

from .base_schema import (
    BrandCustomization,
    BrandCustomizationDetails,
    Conversation,
    GeneratedDesign,
    PyObjectId,
)


class EnumEncoder(json.JSONEncoder):
    """Custom JSON encoder to handle Enum objects by using their value."""

    def default(self, obj):
        if isinstance(obj, Enum):
            return obj.value
        return super().default(obj)


class AppTypeEnum(str, Enum):
    APP = "app_generation"
    WIREFRAME = "wireframe_generation"


class ProjectStateEnum(str, Enum):
    ACTIVE = "ACTIVE"
    ARCHIVED = "ARCHIVED"
    DELETED = "DELETED"


class ProjectStatusEnum(str, Enum):
    PENDING = "PENDING"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"


class AppProgressEnum(str, Enum):
    OVERVIEW = "OVERVIEW"
    SEED_PROJECT_INITIALIZED = "SEED_PROJECT_INITIALIZED"
    FILE_QUEUE = "FILE_QUEUE"
    LAYOUT_ANALYZED = "LAYOUT_ANALYZED"
    DESIGN_SYSTEM_MAPPED = "DESIGN_SYSTEM_MAPPED"
    COMPONENTS_CREATED = "COMPONENTS_CREATED"
    PAGES_GENERATED = "PAGES_GENERATED"
    CODE_GENERATION = "CODE_GENERATION"
    BUILD_STARTED = "BUILD_STARTED"
    BUILD_SUCCEEDED = "BUILD_SUCCEEDED"
    BUILD_FAILED = "BUILD_FAILED"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
    BUILD = "BUILD"
    DEPLOY = "DEPLOY"


# Wireframe Related Models
class WireframeRequest(BaseModel):
    prompt: str


class WireframeGenerateRequest(BaseModel):
    layout_details: str = Field(alias="layoutDetails")
    brand_customization: BrandCustomization = Field(alias="brandCustomization")


class WireframePromptRequest(BaseModel):
    prompt: str
    code: str
    section_code: str


class CreateWireframeRequest(BaseModel):
    appName: str
    prompt: str
    industry: str
    designSystem: str
    brandCustomization: Optional[BrandCustomizationDetails]
    userRequests: Optional[List[str]]
    generatedDesign: Optional[List[GeneratedDesign]]


class AddWireframeRequest(BaseModel):
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    appName: str
    prompt: str
    industry: str
    designSystem: str
    brandCustomization: Optional[BrandCustomizationDetails] = None
    userRequests: Optional[List[str]] = None
    generatedDesign: Optional[List[GeneratedDesign]] = None
    createdAt: str = Field(default_factory=lambda: datetime.utcnow().isoformat())
    updatedAt: str = Field(default_factory=lambda: datetime.utcnow().isoformat())


class UpdateWireframeRequest(BaseModel):
    id: str
    appName: str
    prompt: str
    industry: str
    designSystem: str
    brandCustomization: Optional[BrandCustomizationDetails] = None
    generatedDesign: Optional[List[GeneratedDesign]] = None
    userRequests: Optional[List[str]] = None


class Wireframe_PageSection(BaseModel):
    id: int
    name: str
    description: str
    content: List[Any]


class DARequest(BaseModel):
    mode: str
    promptOverride: bool
    useCaseIdentifier: str
    userSignature: str
    image: Optional[List[str]]
    prompt: str


class DAChatRequest(BaseModel):
    mode: str = None
    conversations: List[Conversation]
    useCaseIdentifier: str = None
    userSignature: str = None


class DAConversationRequest(BaseModel):
    mode: str
    useCaseIdentifier: str
    userSignature: str
    conversations: List[Conversation]


class ConversationAPIRequest(BaseModel):
    mode: str = Field(
        ...,
        description="Mode identifier for the conversation (e.g., 'EE_MLO_TESTING_GEMINI')",
    )
    useCaseIdentifier: str = Field(..., description="Use case identifier string")
    userSignature: str = Field(..., description="User's email or identifier")
    conversations: List[Conversation] = Field(
        ..., description="List of conversation objects with role and content"
    )
    # image: Optional[List[str]] = Field(None, description="List of image data URIs")


# Image to Code Related Models
class ImageRequest(BaseModel):
    mode: Optional[str] = Field(..., example="<Your Use Case>")
    prompt: Optional[str] = Field(
        ..., example="What are the difference between these images?"
    )
    image: Optional[List[str]] = Field(
        ...,
        example=[
            "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABAAAAAQ",
            "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABAAAAAQ",
        ],
    )


class ImageRequestBeta(BaseModel):
    image: Optional[List[str]] = Field(
        ...,
        example=[
            "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABAAAAAQ",
            "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABAAAAAQ",
        ],
    )


class ImageRequest1(BaseModel):
    mode: str
    prompt: str
    image: List[str]


class CodeGenerationRequest(BaseModel):
    project_name: str
    project_description: str
    project_type: AppTypeEnum
    project_state: ProjectStateEnum
    platform: str
    framework: str
    design_library: str
    image: Optional[List[str]] = Field(
        ...,
        example=[
            "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABAAAAAQ",
            "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAABAAAAAQ",
        ],
    )


class ProjectBuildSwitchRequest(BaseModel):
    project_id: str
    commit_id: str


class ReactCodeRequest(BaseModel):
    user_input: str  # user prompt
    platform: str  # mobile/web
    framework: str  # react/angular
    design_library: str  # tailwind/AVA/MatrialUI
    design_style: Optional[str]  # Glassmorphic/Neumorphic/Flat


class ImageToCodeAuthorRequest(BaseModel):
    author: str


class AddImageToCodeRequest(BaseModel):
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    prompt: Optional[str] = None
    projectName: str
    projectType: Literal["NEW_PRODUCT", "LEGACY_MODERNIZATION"]
    author: str
    image: Optional[List[str]] = None
    code: Optional[str] = None
    history: Optional[List[str]] = None
    createdAt: str = Field(default_factory=lambda: datetime.utcnow().isoformat())
    updatedAt: str = Field(default_factory=lambda: datetime.utcnow().isoformat())


class CreateImageToCodeRequest(BaseModel):
    prompt: Optional[str] = None
    projectName: str
    projectType: Literal["NEW_PRODUCT", "LEGACY_MODERNIZATION"]
    author: str
    image: Optional[List[str]] = None
    code: Optional[str] = None
    history: Optional[List[str]] = None


class UpdateImageToCodeRequest(BaseModel):
    id: str
    prompt: Optional[str] = None
    projectName: Optional[str] = None
    projectType: Optional[Literal["NEW_PRODUCT", "LEGACY_MODERNIZATION"]] = None
    image: Optional[List[str]] = None
    code: Optional[str] = None
    history: Optional[List[str]] = None


# Other Models
class HTMLContentRequest(BaseModel):
    html_content: str


class CodeConvertRequest(BaseModel):
    technology: str
    code: str


class PromptRequest(BaseModel):
    prompt: str


class CodeRegenerationRequest(BaseModel):
    prompt: str = Field(
        ...,
        description="Stringified JSON containing code, user_request, and optionally image",
    )
    image: Optional[List[str]] = Field(
        None, description="Optional list of image data URIs"
    )


class BuildErrorRequest(BaseModel):
    error: str


class DesignAnalyzerRequest(BaseModel):
    datauri: str
    tests: List[str]


class WireframeGeneratorRequest(BaseModel):
    prompt: str
    device: str  # mobile/web
    project_type: AppTypeEnum
    image: Optional[List[str]] = Field(
        None, description="Optional list of image data URIs for wireframe generation"
    )


class CodeFile(BaseModel):
    fileName: str
    content: str


class WireframeRegenerateRequest(BaseModel):
    code: Optional[List[CodeFile]] = Field(
        None, description="List of existing code files to regenerate"
    )
    user_request: str = Field(
        ..., description="User request for wireframe regeneration"
    )


class LayoutDetailsRequest(BaseModel):
    appName: str
    industry: str
    prompt: str
    design_system: str = Field(alias="designSystem")


class EmbeddingRequest(BaseModel):
    mongo_db_collection_name: str
    vector_db_collection_name: str


class ResearchRequest(BaseModel):
    step: str
    prompt: str


# Define the Pydantic schema
class SmartPromptRequest(BaseModel):
    type: AppTypeEnum
    prompt: str
    image: Optional[List[str]]
    userSignature: str


class ProjectCreateRequest(BaseModel):
    project_name: str
    project_description: str
    project_type: AppTypeEnum
    project_state: ProjectStateEnum


class CommitDownloadRequest(BaseModel):
    commit_id: str
