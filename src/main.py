from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>
from fastapi.staticfiles import StaticFiles
import redis.asyncio as redis_async
# Import moved to avoid race condition
# from src.settings.settings import Settings, settings_loader
from src.routers import api_router, streaming_routes
from src.model.response_schemas import DataResponse, HeartbeatResponse
from fastapi.middleware.cors import CORSMiddleware


from src.sse_manager import SSEManager
from src.core.sse_pubsub import RedisStreamPublisher

# --- App Lifespan for managing connections ---


@asynccontextmanager
async def lifespan(app: FastAPI):
    print("Starting lifespan...")

    # Import here to avoid race condition
    from src.settings.settings import Settings, settings_loader

    # 1. Load API settings first
    print("Loading API settings...")
    await settings_loader.load_api_settings()
    print(f"API settings loaded: {settings_loader._api_settings}")

    # 2. Create and initialize settings instance after API data is loaded
    settings = Settings()
    print("Initializing settings...")
    settings.initialize()  # Explicit initialization
    app.state.settings = settings
    print("Settings initialized successfully")

    # 3. Use app.state.settings instead of global settings for Redis configuration
    redis_config = app.state.settings.redis_config

    # Initialize Redis connection
    redis_client = redis_async.from_url(
        redis_config.REDIS_URL,
        decode_responses=True,
        socket_timeout=redis_config.REDIS_SOCKET_TIMEOUT,
        socket_connect_timeout=redis_config.REDIS_SOCKET_CONNECT_TIMEOUT,
        retry_on_timeout=redis_config.REDIS_RETRY_ON_TIMEOUT,
        health_check_interval=30,  # Periodically check connection health
    )
    app.state.redis_client = redis_client  # Keep if other parts need raw client

    app.state.sse_manager = SSEManager(app.state.redis_client)
    app.state.sse_manager.start_listener_task()

    # The publisher must use the same stream name as the listener.
    app.state.redis_stream_publisher = RedisStreamPublisher(
        redis_client=app.state.redis_client,
        shared_stream_name=app.state.sse_manager.stream_name,
    )

    yield

    # Code to run on application shutdown
    print("Application shutting down...")
    await app.state.sse_manager.stop_listener_task()
    await app.state.redis_client.close()


app = FastAPI(title="Experience Studio API", lifespan=lifespan)

origins = [
    "http://localhost",
    "http://localhost:8000",
    "http://localhost:4201",
    "https://kind-island-0ff6ddd0f.6.azurestaticapps.net/",
    # Add more origins as needed
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=False,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(api_router)
app.include_router(streaming_routes.streaming_router, tags=["Streaming"])

app.mount("/static", StaticFiles(directory="src/static"), name="static")


@app.get("/")
async def root():
    return DataResponse(
        status="complete", data={"message": "Welcome to the FastAPI application!"}
    )


# Heartbeat endpoint
@app.get("/heartbeat", response_model=HeartbeatResponse)
async def heartbeat():
    return HeartbeatResponse(status="ok")
