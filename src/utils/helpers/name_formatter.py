import re
from typing import <PERSON><PERSON>


def format_repository_name(
    project_name: str, user_id: str, project_id: str
) -> <PERSON><PERSON>[str, str]:
    """
    Format a project name into a valid repository name with user and project ID suffix.

    Args:
        project_name: The original project name
        user_id: User identifier
        project_id: Project identifier

    Returns:
        Tuple containing:
        - final_repo_name: The complete repository name
        - final_name_part: Just the formatted project name part (without suffix)
    """
    # 1. Convert to lowercase
    initial_name = project_name.lower()

    # 2. Replace all invalid characters with a hyphen
    #    [^a-z0-9-] matches any character that is NOT a lowercase letter, a digit, or a hyphen.
    #    The '+' after it ensures that multiple consecutive invalid characters are replaced by a single hyphen.
    initial_name = re.sub(r"[^a-z0-9-]+", "-", initial_name)

    # 3. Remove leading or trailing hyphens that might have been created
    initial_name = initial_name.strip("-")

    # Create suffix with user ID and project ID
    suffix = f"-{str(user_id)[-4:]}-{project_id[-4:]}"

    # 4. Calculate max length for the name part to not exceed 64 chars total
    max_name_len = 64 - len(suffix)

    # 5. Truncate the sanitized name and clean up any trailing hyphen from the cut
    final_name_part = initial_name[:max_name_len].strip("-")

    # 6. Combine to create the final, valid repository name
    final_repo_name = f"{final_name_part}{suffix}"

    return final_repo_name, final_name_part
