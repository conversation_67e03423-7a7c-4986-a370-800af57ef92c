from bs4 import BeautifulSoup


class HTMLBodyGenerator:

    # design generation v3
    def design_generation_baseHtml_template(self, has_sidebar):
        """
        Returns a Jinja2 template string based on whether a sidebar is needed.
        """
        if has_sidebar:
            return """
            <!DOCTYPE html>
            <html>
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
                    <script src="https://cdn.tailwindcss.com"></script>
                </head>
                <body class="flex h-screen">
                    <!-- Sidebar -->
                    <div class="sidebar-wrapper overflow-y-auto border-2">
                        {{ sidebar|safe }}
                    </div>
                    
                    <!-- Main Content -->
                    <div class="main-content flex-1 flex flex-col overflow-y-auto">
                        {{ dynamic_sections|safe }}
                    </div>
                </body>
            </html>
            """
        else:
            return """
            <!DOCTYPE html>
            <html>
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
                    <script src="https://cdn.tailwindcss.com"></script>
                </head>
                <body>
                    <div class="dynamic-sections">
                        {{ dynamic_sections|safe }}
                    </div>
                </body>
            </html>
            """

    # design generation V2
    def design_generation_build_html_document(
        html_fragments, logo_url, color_palette, design_template_flag
    ):
        """
        Combines HTML fragments into a single, well-structured HTML document based on the chosen template.
        """

        def create_base_html(has_sidebar, has_header, has_footer):
            base_html = """
                <!DOCTYPE html>
                <html>
                    <head></head>
                    <body class="flex h-screen">
            """
            if has_sidebar:
                base_html += """
                    <!-- Sidebar -->
                    <div class="overflow-y-auto border-2" id="sidebar-wrapper"> </div>
                """

            base_html += """
                    <div class="main-content flex flex-1 flex-col overflow-y-auto" id="main-content-wrapper">
            """

            if has_header:
                base_html += """
                        <!-- Header -->
                        <header class="p-3 items-center" id="header-wrapper"> </header>
                """

            base_html += """
                        <!-- Section -->
                        <!-- <section class="section-unrecognized p-4" id="section-wrapper"> </section> -->
            """

            if has_footer:
                base_html += """
                        <!-- Footer -->
                        <footer class="p-4 mt-auto" id="footer-wrapper">
                            <div class="items-center"> </div>
                        </footer>
                """

            base_html += """
                    </div>
                    </body>
                </html>
            """
            return base_html

        has_sidebar = any("sidebar" in fragment for fragment in html_fragments)
        has_header = any("header" in fragment for fragment in html_fragments)
        has_footer = any("footer" in fragment for fragment in html_fragments)
        base_html = create_base_html(
            has_sidebar and design_template_flag, has_header, has_footer
        )

        soup = BeautifulSoup(base_html, "html.parser")

        cdn_links = """
            <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/sadiqnaizam/css-hosting@main/theme.css"/>
            <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">

            <script crossorigin="anonymous" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" src="https://code.jquery.com/jquery-3.7.1.min.js"></script>
            <script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>
            <script src="https://cdn.jsdelivr.net/gh/sadiqnaizam/css-hosting/dashboard.js"></script>
            <script src="https://cdn.tailwindcss.com"></script>

        """
        soup.head.append(BeautifulSoup(cdn_links, "html.parser"))

        def handle_sidebar(component_soup, soup):
            sidebar_container = soup.find("div", id="sidebar-wrapper")
            if sidebar_container:
                sidebar_container.append(component_soup)
                print("----- Sidebar Inserted -----")
            else:
                print("----- Sidebar Container Not Found in Main Soup -----")

        def handle_header(component_soup, soup):
            header_section = soup.find("header", class_="p-3 items-center")
            if header_section:
                header_section.append(component_soup)
                print("inserted header")
            else:
                print("header not found")

        def handle_footer(component_soup, soup):
            footer_section = soup.find("footer", class_="p-4 mt-auto")
            if footer_section:
                footer_content = component_soup.find("footer")
                footer_div = footer_section.find("div", class_="items-center")
                if footer_div:
                    footer_div.append(footer_content)
                    print("inserted footer")
                else:
                    footer_section.append(footer_content)
                    print("inserted footer")

        def handle_unrecognized(component_soup, soup, section_name):
            new_section = soup.new_tag(
                "section",
                **{
                    "class": "section " + section_name + " p-8 flex justify-center",
                    "style": "padding: 1rem;",
                    "id": "section-wrapper",
                },
            )
            new_section.append(component_soup)

            footer_section = soup.find("footer", id="footer-wrapper")
            if footer_section:
                footer_section.insert_before(new_section)
                print("inserted " + section_name + " component before footer")
            else:
                main_content = soup.find("div", id="main-content-wrapper")
                if main_content:
                    main_content.append(new_section)
                    print("inserted " + section_name + " component into main-content")

        for fragment in html_fragments:
            for section_name, html_code in fragment.items():
                component_soup = BeautifulSoup(html_code, "html.parser")

                if section_name == "sidebar" and design_template_flag:
                    handle_sidebar(component_soup, soup)
                elif section_name == "header":
                    handle_header(component_soup, soup)
                elif section_name == "footer":
                    handle_footer(component_soup, soup)
                else:
                    handle_unrecognized(component_soup, soup, section_name)

        return soup.prettify()

    # code Generation
    def generate_html_body(self, image_list):
        """
        Generates HTML body code based on a list of image names.

        Naming convention:
          - A name starting with "h" means it’s a horizontal group (a row).
          - A name starting with "v" means it’s a vertical group (a column).
          - An underscore ("_") denotes a nested group.

        Examples:
          1. ["h1.png", "h2_v1.png", "h2_v2.png"]
             → first row: a full-width h1 div;
               second row: a flex container with two columns (h2_v1 and h2_v2).

          2. ["h1.png", "h2.png", "h3.png", "h4.png"]
             → each image is rendered as its own row.

          3. ["v1.png", "v2.png", "v3.png", "v4.png"]
             → all images (with “v” prefix) are wrapped in a single flex container so they appear side by side.

          4. ["v1.png", "v2_h1.png", "v2_h2.png", "v2_h3.png"]
             → top-level there are two groups (v1 and v2).
                The first is a single div;
                the second group’s children (v2_h1, v2_h2, v2_h3) are arranged vertically.

          5. ["whole_image.png"]
             → Since it doesn’t start with “h” or “v”, it’s rendered as one div (with no extra margin).
        """
        from collections import OrderedDict

        class Node:
            def __init__(self, key=None):
                self.key = key
                self.children = OrderedDict()
                self.full = None

        def insert_node(root, parts, full_name):
            if not parts:
                return
            part = parts[0]
            if part not in root.children:
                root.children[part] = Node(part)
            node = root.children[part]
            if len(parts) == 1:
                node.full = full_name
            else:
                insert_node(node, parts[1:], full_name)

        root = Node()
        for image, _ in image_list:
            full = image.split(".")[0]
            parts = full.split("_")
            insert_node(root, parts, full)

        def render_node(node, indent_level=1):
            indent = "    " * indent_level
            html_lines = []

            if node.key is None:
                children = list(node.children.values())
                if (
                    children
                    and all(
                        child.key and child.key[0].lower() == "v" for child in children
                    )
                    and len(children) > 1
                ):
                    html_lines.append(f'{indent}<div class="flex">')
                    for child in children:
                        html_lines.append(render_node(child, indent_level + 1))
                    html_lines.append(f"{indent}</div>")
                else:
                    for child in children:
                        html_lines.append(render_node(child, indent_level))
                return "\n".join(html_lines)

            if node.children:
                group_type = node.key[0].lower()
                container_class = "flex" if group_type == "h" else "flex flex-col"
                html_lines.append(f'{indent}<div class="{container_class}">')
                for child in node.children.values():
                    html_lines.append(render_node(child, indent_level + 1))
                html_lines.append(f"{indent}</div>")
            else:
                html_lines.append(f'{indent}<div id="{node.full}"></div>')
            return "\n".join(html_lines)

        html_output = ["<body>"]
        html_output.append(render_node(root, indent_level=1))
        html_output.append("</body>")
        return "\n".join(html_output)
