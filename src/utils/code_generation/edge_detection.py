import string
import cv2
import numpy as np
import matplotlib.pyplot as plt
import base64
from io import BytesIO
from PIL import Image


def image_to_data_uri(image_path: str) -> str:
    """
    Convert an image (PNG or JPEG) to a data URI format.

    Args:
        image_path (str): Path to the image file.

    Returns:
        str: Data URI of the image.
    """
    with open(image_path, "rb") as image_file:
        encoded_string = base64.b64encode(image_file.read()).decode("utf-8")
        mime_type = "image/png" if image_path.lower().endswith(".png") else "image/jpeg"
        return f"data:{mime_type};base64,{encoded_string}"


def data_uri_to_image(data_uri: str, output_path: str) -> None:
    """
    Convert a data URI back into an image (PNG or JPEG) and save it.

    Args:
        data_uri (str): Data URI of the image.
        output_path (str): Path to save the output image.

    Returns:
        None
    """
    header, encoded = data_uri.split(",")
    image_data = base64.b64decode(encoded)
    image = Image.open(BytesIO(image_data))
    image.save(output_path)


def process_edges_image(image_uris: list[str]) -> list[str]:
    """
    Process a list of image URIs, apply edge detection, and return processed images in URI format.

    Args:
        image_uris (list): List of image URIs.

    Returns:
        list: List of processed images in URI format.
    """
    processed_image_uris = []

    for image_uri in image_uris:
        # Convert URI to image and save as 'image.png'
        data_uri_to_image(image_uri, "image.png")

        # Load the image
        image = cv2.imread("image.png")

        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

        # Apply adaptive histogram equalization to enhance contrast
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8, 8))
        enhanced = clahe.apply(gray)

        # Apply adaptive thresholding to binarize the image
        thresh = cv2.adaptiveThreshold(
            enhanced, 255, cv2.ADAPTIVE_THRESH_MEAN_C, cv2.THRESH_BINARY_INV, 15, 10
        )

        # Detect edges using Canny
        edges = cv2.Canny(enhanced, 50, 150, apertureSize=3)

        # Save the processed images
        cv2.imwrite("thresholded_image.png", thresh)
        cv2.imwrite("edges_image.png", edges)

        # Convert processed images back to URI format
        thresholded_uri = image_to_data_uri("thresholded_image.png")
        edges_uri = image_to_data_uri("edges_image.png")

        # Append the processed URIs to the result list
        processed_image_uris.extend([thresholded_uri, edges_uri])

    return processed_image_uris
