import numpy as np
import os
from typing import List, Tuple, Dict
from glob import glob
import time
from PIL import Image
import logging

from src.model.base_schema import SplitType, SplitResult, ImageSection, ColorAnalyzer


class SplitFinder:
    def __init__(self, crop_margin: int = 30):
        self.crop_margin = crop_margin

    def _get_cropped_image(self, image: np.ndarray) -> np.ndarray:
        """Return cropped image with margins removed."""
        return image[
            self.crop_margin : -self.crop_margin, self.crop_margin : -self.crop_margin
        ]

    def find_horizontal_splits(
        self, image_section: ImageSection, min_section_height: int = 30
    ) -> List[int]:
        """Find vertical split points in an image based on color boundaries."""
        sub_image = self._get_cropped_image(image_section.image)
        new_height, new_width = sub_image.shape[:2]

        split_points = []
        y_base = 0

        while y_base < new_height:
            y_current = y_base + 1

            while y_current < new_height:
                unique_colors = ColorAnalyzer.get_unique_colors_horizontal(
                    sub_image, y_base, y_current
                )

                if unique_colors > 1:
                    if (y_current - y_base) >= min_section_height:
                        split_points.append(y_current + self.crop_margin)
                    y_base = y_current + 5
                    break

                y_current += 1

            if y_current >= new_height:
                break

            y_base = y_current

        return split_points

    def find_vertical_splits(
        self, image_section: ImageSection, min_section_width: int = 30
    ) -> List[int]:
        """Find horizontal split points in an image based on color boundaries."""
        sub_image = self._get_cropped_image(image_section.image)
        new_height, new_width = sub_image.shape[:2]

        split_points = []
        x_base = 0

        while x_base < new_width:
            x_current = x_base + 1

            while x_current < new_width:
                unique_colors = ColorAnalyzer.get_unique_colors_vertical(
                    sub_image, x_base, x_current
                )

                if unique_colors > 1:
                    if (x_current - x_base) >= min_section_width:
                        split_points.append(x_current + self.crop_margin)
                    x_base = x_current + 5
                    break

                x_current += 1

            if x_current >= new_width:
                break

            x_base = x_current

        return split_points


class OptimalSplitFinder:
    def __init__(self, split_finder: SplitFinder):
        self.split_finder = split_finder

    def find_optimal_horizontal_height(
        self,
        image_section: ImageSection,
        initial_height: int = 30,
        step: int = 5,
        min_allowed_height: int = 10,
    ) -> Dict:
        """Find the optimal minimum height that results in 3-4 splits."""

        def get_num_splits(height: int) -> int:
            return len(self.split_finder.find_horizontal_splits(image_section, height))

        current_height = initial_height
        each_split = {}

        while current_height >= min_allowed_height:
            num_splits = get_num_splits(current_height)
            print(f"Testing height: {current_height}px, Splits: {num_splits}")
            each_split[current_height] = num_splits

            if 3 <= num_splits <= 4:
                return {
                    "min_height": current_height,
                    "splits": self.split_finder.find_horizontal_splits(
                        image_section, current_height
                    ),
                }

            if num_splits < 3:
                upper_bound = current_height
                search_height = current_height - step

                while search_height >= min_allowed_height:
                    num_splits = get_num_splits(search_height)
                    print(f"Testing height: {search_height}px, Splits: {num_splits}")
                    each_split[search_height] = num_splits

                    if num_splits >= 3:
                        print(f"Number of splits found: {each_split}")
                        return {
                            "min_height": search_height,
                            "splits": self.split_finder.find_horizontal_splits(
                                image_section, search_height
                            ),
                        }

                    search_height -= step

                print(f"Number of splits found: {each_split}")
                best_height = max(each_split, key=each_split.get)
                return {
                    "min_height": best_height,
                    "splits": self.split_finder.find_horizontal_splits(
                        image_section, best_height
                    ),
                }

            current_height -= step

        # If we get here, use the minimum allowed height
        return {
            "min_height": min_allowed_height,
            "splits": self.split_finder.find_horizontal_splits(
                image_section, min_allowed_height
            ),
        }

    def find_optimal_vertical_width(
        self,
        image_section: ImageSection,
        initial_width: int = 30,
        step: int = 5,
        min_allowed_width: int = 10,
    ) -> Dict:
        """Find the optimal minimum width that results in 3-5 splits."""

        def get_num_splits(width: int) -> int:
            return len(self.split_finder.find_vertical_splits(image_section, width))

        current_width = initial_width
        each_split = {}

        while current_width >= min_allowed_width:
            num_splits = get_num_splits(current_width)
            print(f"Testing width: {current_width}px, Splits: {num_splits}")
            each_split[current_width] = num_splits

            if 3 <= num_splits <= 5:
                return {
                    "min_width": current_width,
                    "splits": self.split_finder.find_vertical_splits(
                        image_section, current_width
                    ),
                }

            if num_splits < 3:
                upper_bound = current_width
                search_width = current_width - step

                while search_width >= min_allowed_width:
                    num_splits = get_num_splits(search_width)
                    print(f"Testing width: {search_width}px, Splits: {num_splits}")
                    each_split[search_width] = num_splits

                    if num_splits >= 3:
                        print(f"Number of splits found: {each_split}")
                        return {
                            "min_width": search_width,
                            "splits": self.split_finder.find_vertical_splits(
                                image_section, search_width
                            ),
                        }

                    search_width -= step

                print(f"Number of splits found: {each_split}")
                best_width = max(each_split, key=each_split.get)
                return {
                    "min_width": best_width,
                    "splits": self.split_finder.find_vertical_splits(
                        image_section, best_width
                    ),
                }

            current_width -= step

        # If we get here, use the minimum allowed width
        return {
            "min_width": min_allowed_width,
            "splits": self.split_finder.find_vertical_splits(
                image_section, min_allowed_width
            ),
        }


class SplitCombiner:
    @staticmethod
    def combine_small_horizontal_splits(
        image_section: ImageSection,
        split_points: List[int],
        threshold_percentage: float = 0.2,
    ) -> List[int]:
        """Combine initial horizontal splits if they make up less than the threshold percentage of the image."""
        total_height = image_section.height
        threshold_height = total_height * threshold_percentage

        all_points = [0] + split_points + [total_height]
        section_heights = [
            all_points[i + 1] - all_points[i] for i in range(len(all_points) - 1)
        ]

        # Check if we need to combine splits
        combine_count = 0
        cumulative_height = 0

        for i, height in enumerate(section_heights):
            cumulative_height += height
            if cumulative_height < threshold_height:
                combine_count += 1
            else:
                break

        # If we need to combine splits
        if combine_count > 1:
            print(
                f"Combining first {combine_count} splits (total {cumulative_height}px, {cumulative_height/total_height:.1%} of image)"
            )
            new_split_points = split_points[combine_count - 1 :]
            return new_split_points

        return split_points

    @staticmethod
    def combine_vertical_splits(
        image_section: ImageSection,
        split_points: List[int],
        threshold_percentage: float = 0.25,
    ) -> List[int]:
        """
        Combine initial vertical splits according to specified rules:
        1. If first N splits < 25% of image area, combine them
        2. Combine all remaining splits into one section
        """
        total_width = image_section.width
        total_area = image_section.area

        all_points = [0] + split_points + [total_width]
        section_widths = [
            all_points[i + 1] - all_points[i] for i in range(len(all_points) - 1)
        ]

        # Calculate section areas (assuming full height)
        section_areas = [width * image_section.height for width in section_widths]
        section_percentages = [area / total_area for area in section_areas]

        # Print initial section information
        for i, (width, area, percentage) in enumerate(
            zip(section_widths, section_areas, section_percentages)
        ):
            print(
                f"Initial Section {i+1}: {width}px wide, {area}px² ({percentage:.1%} of image)"
            )

        # Check how many initial splits to combine
        combine_first_count = 0
        cumulative_percentage = 0

        for i, percentage in enumerate(section_percentages):
            cumulative_percentage += percentage
            if cumulative_percentage < threshold_percentage:
                combine_first_count += 1
            else:
                break

        # Need at least 2 sections to combine the first ones
        if combine_first_count <= 1 or combine_first_count >= len(section_percentages):
            combine_first_count = 1

        # Create new split points - only one after the combined first sections
        if len(split_points) > 1:
            print(
                f"Combining first {combine_first_count} sections (total {cumulative_percentage:.1%} of image)"
            )
            print(
                f"Combining remaining {len(section_percentages) - combine_first_count} sections"
            )

            # If we have something to combine, return just one split point
            if combine_first_count > 0 and combine_first_count < len(
                section_percentages
            ):
                return [split_points[combine_first_count - 1]]

        return split_points


class ImageSplitter:
    def __init__(self):
        self.split_finder = SplitFinder()
        self.optimal_split_finder = OptimalSplitFinder(self.split_finder)
        self.split_hierarchy = []  # Track split hierarchy

    def _save_section(
        self, image_section: ImageSection, output_path: str
    ) -> Tuple[bool, int]:
        """Save an image section and return success status and area."""
        success = image_section.save(output_path)
        return success, image_section.area

    def attempt_horizontal_splitting(
        self, image_path: str, output_dir: str, parent_prefix: str = ""
    ) -> SplitResult:
        """
        Attempt to split an image horizontally, returning split points and information on large sections.
        """
        try:
            # Add horizontal split type to hierarchy if successful
            if not parent_prefix:  # Only track top-level splits
                self.split_hierarchy.append(SplitType.HORIZONTAL)

            image_section = ImageSection.from_path(image_path)
            total_area = image_section.area

            # Find optimal splits
            result = self.optimal_split_finder.find_optimal_horizontal_height(
                image_section
            )
            optimal_splits = result["splits"]
            optimal_height = result["min_height"]

            # If no splits found, return unsuccessfully
            if len(optimal_splits) == 0:
                print(f"No horizontal splits found for {image_path}")
                if (
                    not parent_prefix
                ):  # Only remove from hierarchy if it's a top-level attempt
                    self.split_hierarchy.pop()  # Remove the horizontal split type
                return SplitResult(False, [], {}, SplitType.NONE)

            print(f"\nProcessing image horizontally: {os.path.basename(image_path)}")
            print(f"Optimal minimum height: {optimal_height}px")
            print(f"Initial number of splits: {len(optimal_splits)}")

            # Combine small splits if necessary
            final_splits = SplitCombiner.combine_small_horizontal_splits(
                image_section, optimal_splits
            )

            if len(final_splits) != len(optimal_splits):
                print(
                    f"Combined small sections: reduced from {len(optimal_splits)} to {len(final_splits)} splits"
                )

            # If no splits remain after combining, return unsuccessfully
            if len(final_splits) == 0:
                print(f"No horizontal splits remain after combining for {image_path}")
                if (
                    not parent_prefix
                ):  # Only remove from hierarchy if it's a top-level attempt
                    self.split_hierarchy.pop()  # Remove the horizontal split type
                return SplitResult(False, [], {}, SplitType.NONE)

            # Calculate areas and identify large sections
            height = image_section.height
            all_points = [0] + final_splits + [height]

            large_sections = []
            section_areas = {}

            # Split and save sections
            for i in range(len(all_points) - 1):
                start_y = all_points[i]
                end_y = all_points[i + 1]

                section_name = f"{parent_prefix}h{i+1}" if parent_prefix else f"h{i+1}"
                output_filename = f"{section_name}.png"
                output_path = os.path.join(output_dir, output_filename)

                sub_section = ImageSection.from_section(
                    image_section, y_start=start_y, y_end=end_y
                )
                success, area = self._save_section(sub_section, output_path)
                section_percentage = area / total_area

                print(
                    f"Horizontal Section {section_name}: {end_y - start_y}px ({section_percentage:.1%} of image)"
                )
                section_areas[section_name] = section_percentage

                # Check if this section is large (>50% of total area)
                if section_percentage > 0.5:
                    large_sections.append((i, section_name, output_path))
                    print("  ↳ This is a large section (>50%)")

            return SplitResult(
                True, large_sections, section_areas, SplitType.HORIZONTAL
            )

        except Exception as e:
            print(f"Error in horizontal splitting: {e}")
            if (
                not parent_prefix
            ):  # Only remove from hierarchy if it's a top-level attempt
                if (
                    self.split_hierarchy
                    and self.split_hierarchy[-1] == SplitType.HORIZONTAL
                ):
                    self.split_hierarchy.pop()  # Remove the horizontal split type
            return SplitResult(False, [], {}, SplitType.NONE)

    def attempt_vertical_splitting(
        self, image_path: str, output_dir: str, parent_prefix: str = ""
    ) -> SplitResult:
        """
        Attempt to split an image vertically, returning split points and information on large sections.
        """
        try:
            # Add vertical split type to hierarchy if successful
            if not parent_prefix:  # Only track top-level splits
                self.split_hierarchy.append(SplitType.VERTICAL)

            image_section = ImageSection.from_path(image_path)
            total_area = image_section.area

            # Find optimal splits
            result = self.optimal_split_finder.find_optimal_vertical_width(
                image_section
            )
            optimal_splits = result["splits"]
            optimal_width = result["min_width"]

            # If no splits found, return unsuccessfully
            if len(optimal_splits) == 0:
                print(f"No vertical splits found for {image_path}")
                if (
                    not parent_prefix
                ):  # Only remove from hierarchy if it's a top-level attempt
                    self.split_hierarchy.pop()  # Remove the vertical split type
                return SplitResult(False, [], {}, SplitType.NONE)

            print(f"\nProcessing image vertically: {os.path.basename(image_path)}")
            print(f"Optimal minimum width: {optimal_width}px")
            print(f"Initial number of vertical splits: {len(optimal_splits)}")

            # Combine splits if necessary
            final_splits = SplitCombiner.combine_vertical_splits(
                image_section, optimal_splits
            )

            if len(final_splits) != len(optimal_splits):
                print(
                    f"Combined sections: reduced from {len(optimal_splits)} to {len(final_splits)} splits"
                )

            # If no splits remain after combining, return unsuccessfully
            if len(final_splits) == 0:
                print(f"No vertical splits remain after combining for {image_path}")
                if (
                    not parent_prefix
                ):  # Only remove from hierarchy if it's a top-level attempt
                    self.split_hierarchy.pop()  # Remove the vertical split type
                return SplitResult(False, [], {}, SplitType.NONE)

            # Calculate areas and identify large sections
            width = image_section.width
            all_points = [0] + final_splits + [width]

            large_sections = []
            section_areas = {}

            # Split and save sections
            for i in range(len(all_points) - 1):
                start_x = all_points[i]
                end_x = all_points[i + 1]

                section_name = f"{parent_prefix}v{i+1}" if parent_prefix else f"v{i+1}"
                output_filename = f"{section_name}.png"
                output_path = os.path.join(output_dir, output_filename)

                sub_section = ImageSection.from_section(
                    image_section, x_start=start_x, x_end=end_x
                )
                success, area = self._save_section(sub_section, output_path)
                section_percentage = area / total_area

                print(
                    f"Vertical Section {section_name}: {end_x - start_x}px ({section_percentage:.1%} of image)"
                )
                section_areas[section_name] = section_percentage

                # Check if this section is large (>50% of total area)
                if section_percentage > 0.5:
                    large_sections.append((i, section_name, output_path))
                    print("  ↳ This is a large section (>50%)")

            return SplitResult(True, large_sections, section_areas, SplitType.VERTICAL)

        except Exception as e:
            print(f"Error in vertical splitting: {e}")
            if (
                not parent_prefix
            ):  # Only remove from hierarchy if it's a top-level attempt
                if (
                    self.split_hierarchy
                    and self.split_hierarchy[-1] == SplitType.VERTICAL
                ):
                    self.split_hierarchy.pop()  # Remove the vertical split type
            return SplitResult(False, [], {}, SplitType.NONE)

    def process_image(self, image_path: str, output_dir: str) -> List[SplitType]:
        """
        Process an image using a combined horizontal-vertical approach:
        1. Try horizontal splitting first
        2. For large horizontal sections (>50% area), try vertical splitting
        3. If horizontal splitting fails, try vertical splitting
        4. For large vertical sections (>50% area), try horizontal splitting
        5. If neither works, save the whole image

        Returns:
            A list representing the split hierarchy (e.g., [h, v] for horizontal then vertical)
        """
        # Reset split hierarchy for each new image
        self.split_hierarchy = []

        # Create output directory based on image name
        image_name = os.path.splitext(os.path.basename(image_path))[0]
        output_dir = os.path.join(output_dir)
        os.makedirs(output_dir, exist_ok=True)

        print(f"\n=== Processing image: {image_name} ===")
        start_time = time.time()

        # Try horizontal splitting first
        h_result = self.attempt_horizontal_splitting(image_path, output_dir)

        image_names = []
        if h_result.success:
            print(f"\nHorizontal splitting successful for {image_name}")
            print(f"Found {len(h_result.section_areas)} horizontal sections")
            # append image names in the list
            for section_name in h_result.section_areas.keys():
                image_names.append(section_name)

            # Process large horizontal sections with vertical splitting
            for section_idx, section_name, section_path in h_result.large_sections:
                print(
                    f"\nProcessing large horizontal section {section_name} with vertical splitting"
                )
                v_result = self.attempt_vertical_splitting(
                    section_path, output_dir, parent_prefix=f"{section_name}_"
                )

                if v_result.success:
                    print(
                        f"Successfully split large horizontal section {section_name} into vertical sections"
                    )
                    # append image names in the list
                    for section_name in v_result.section_areas.keys():
                        image_names.append(section_name)
                else:
                    print(
                        f"Unable to further split horizontal section {section_name} vertically"
                    )
        else:
            # If horizontal splitting failed, try vertical splitting
            print(
                f"\nHorizontal splitting unsuccessful for {image_name}, attempting vertical splitting"
            )
            v_result = self.attempt_vertical_splitting(image_path, output_dir)

            if v_result.success:
                print(f"\nVertical splitting successful for {image_name}")
                print(f"Found {len(v_result.section_areas)} vertical sections")
                # append image names in the list
                for section_name in v_result.section_areas.keys():
                    image_names.append(section_name)

                # Process large vertical sections with horizontal splitting
                for section_idx, section_name, section_path in v_result.large_sections:
                    print(
                        f"\nProcessing large vertical section {section_name} with horizontal splitting"
                    )
                    h_result_nested = self.attempt_horizontal_splitting(
                        section_path, output_dir, parent_prefix=f"{section_name}_"
                    )

                    if h_result_nested.success:
                        print(
                            f"Successfully split large vertical section {section_name} into horizontal sections"
                        )
                        # append image names in the list
                        for section_name in h_result_nested.section_areas.keys():
                            image_names.append(section_name)
                    else:
                        print(
                            f"Unable to further split vertical section {section_name} horizontally"
                        )
            else:
                # If neither horizontal nor vertical splitting succeeded, save the whole image
                print(
                    f"\nNeither horizontal nor vertical splitting succeeded for {image_name}"
                )
                print("Saving entire image without splitting")
                # append image names in the list
                image_names.append("whole_image")

                output_path = os.path.join(output_dir, "whole_image.png")
                image_section = ImageSection.from_path(image_path)
                self._save_section(image_section, output_path)

        # If no successful splits were found, return [NA]
        if not self.split_hierarchy:
            self.split_hierarchy = [SplitType.NONE]

        end_time = time.time()
        print(f"Processing completed in {end_time - start_time:.2f} seconds")
        # print(f"Split hierarchy: {[split_type.value for split_type in self.split_hierarchy]}")

        return image_names

    def get_split_hierarchy(self) -> List[str]:
        """Return the current split hierarchy as a list of string values."""
        return [split_type.value for split_type in self.split_hierarchy]


class BatchImageProcessor:
    def __init__(self, splitter: ImageSplitter):
        self.splitter = splitter
        self.results = {}

    def process_batch(
        self, input_dir: str, output_base_dir: str, image_pattern: str = "*.png"
    ) -> Dict[str, List[str]]:
        """
        Process multiple images from an input directory using the combined approach.

        Returns:
            A dictionary mapping image names to their split hierarchies
        """
        # Create base output directory if it doesn't exist
        os.makedirs(output_base_dir, exist_ok=True)

        # Get list of all matching images in input directory
        image_paths = glob(os.path.join(input_dir, image_pattern))

        if not image_paths:
            print(f"No images found matching pattern {image_pattern} in {input_dir}")
            return {}

        print(f"Found {len(image_paths)} images to process")

        # Process each image and track results
        for image_path in image_paths:
            try:
                image_name = os.path.splitext(os.path.basename(image_path))[0]
                img_name = self.splitter.process_image(image_path, output_base_dir)
                # self.results[image_name] = [split_type.value for split_type in split_hierarchy]
                self.results[image_name] = img_name
            except Exception as e:
                print(f"Failed to process {image_path}: {str(e)}")
                self.results[os.path.splitext(os.path.basename(image_path))[0]] = [
                    SplitType.NONE.value
                ]
                continue

        return self.results


# Example usage
def process_single_image(image_path: str, output_dir: str) -> List[str]:
    """Process a single image and return the split hierarchy."""
    splitter = ImageSplitter()
    image_name = splitter.process_image(image_path, output_dir)
    return image_name


def process_directory(
    input_dir: str, output_dir: str, pattern: str = "*.png"
) -> Dict[str, List[str]]:
    """Process all images in a directory and return the split hierarchies for each."""
    splitter = ImageSplitter()
    processor = BatchImageProcessor(splitter)
    return processor.process_batch(input_dir, output_dir, pattern)


def filter_images(image_names):
    # Count how many times each prefix appears.
    prefix_count = {}
    for name in image_names:
        # The prefix is defined as the part before '_' (if it exists)
        prefix = name.split("_")[0]
        prefix_count[prefix] = prefix_count.get(prefix, 0) + 1

    # Build the output list. If the prefix appears more than once,
    # we remove the entry that exactly matches the prefix.
    result = []
    for name in image_names:
        prefix = name.split("_")[0]
        # If the prefix appears more than once and the current name
        # is exactly equal to the prefix, skip it.
        if prefix_count[prefix] > 1 and name == prefix:
            continue
        result.append(name)
    return result


def cleanup_directory(filtered_names, directory):
    """
    Removes all files in the specified directory whose base name (without extension)
    is not in the list of filtered_names.

    Args:
        filtered_names (list): A list of base image names to keep.
        directory (str): The directory path where the images are saved.
    """
    # Iterate over all items in the directory.
    for filename in os.listdir(directory):
        # file_path = os.path.join(directory, filename)
        # Only process files (ignore subdirectories)
        if os.path.isfile(directory):
            # Get the base name of the file (without extension)
            base_name, ext = os.path.splitext(filename)
            # If the base name is not in the filtered list, delete the file.
            if base_name not in filtered_names:
                os.remove(directory)
                print(f"Removed: {directory}")


def split_image(image: Image.Image) -> List[str]:
    """
    Splits a single image into vertical sections and returns them as a list of image paths,
    skipping sections that contain only one color or are too small.

    Args:
        image (PIL.Image.Image): The input image to be split.

    Returns:
        List[str]: A list of file paths for the split images.
    """
    try:
        # Save the input image to a file
        # image.save(image_path)

        output_dir = "image_splits_output"
        image_path = "input_image.png"

        logging.info("---- Task: Splitting image into sections")

        split_images_list = process_single_image(image_path, output_dir)

        if not split_images_list:
            logging.warning("---- Status: No valid sections found after splitting")
            return []

        # Add .png extension and directory path to the split images
        split_images_list = [
            os.path.join(output_dir, f"{image}.png") for image in split_images_list
        ]

        logging.info(
            f"---- Status: Successfully split image into {len(split_images_list)} sections"
        )
        logging.info(f"---- Task: Filtering image sections:{split_images_list}")

        return split_images_list

    except Exception as e:
        logging.error(f"---- Error: Failed to process image - {e}", exc_info=True)
        return []
