# from langchain_openai import AzureChatOpenAI
# from langchain.output_parsers import PydanticOutputParser
# from langchain_core.prompts import PromptTemplate
# from src.settings.settings import DACreds, AzureOpenAICreds
# from .section_prompt import SectionPrompt
# from src.schemas.base_schema import (
#     Schema_SectionID, Schema_WebSiteInfo,
#     Schema_WebSite_Pages, Schema_WebSite_Page_Sections,
#     Schema_WebProject, Schema_TemplateName, Schema_WebPage_All_Page_Sections
# )
# from src.schemas.request_schemas import DAImageRequest
# from src.schemas.request_schemas import DARequest
# from src.schemas.response_schemas import DAResponse
# from src.utils.api_helper.api_client import APIClient
# import json
# import httpx
# import aiohttp
# import logging
# from typing import Any, Dict, List, Optional
# from datetime import datetime

# azure_creds = AzureOpenAICreds()
# da_creds = DACreds()

# # Constants
# USECASE_DESCRIPTION_GENERATOR = "MLO_WP_DESCRIPTION_GENERATOR"
# USECASE_PAGE_DESCRIPTION_GENERATOR = "MLO_WP_PAGE_DESCRIPTION_GENERATOR"
# USECASE_PAGE_SECTION_GENERATOR = "MLO_WP_PAGE_SECTION_GENERATOR"
# USECASE_TEMPLATE = "MLO_WP_TEMPLATE"
# USECASE_PROMPT = "MLO_WP_PROMPT"

# logger = logging.getLogger(__name__)

# class WebPageGenerator:

#     def __init__(self, api_client: APIClient, user_signature: str = "", api_endpoint: str = "") -> None:
#         self.deployment_name: str = 'gpt-4-vision-preview'
#         self.section_repo_filepath: str = "./app/webpage_generator/section_repo.json"
#         self.section_repo: dict = {}
#         self.user_signature: str = user_signature
#         self.api_client: APIClient = api_client
#         self.api_endpoint: str = api_endpoint
#         self.section_repo_json_string: str = ""

#         self.load_section()
#         self.llm = AzureChatOpenAI(temperature=0,
#                         deployment_name=self.deployment_name,
#                         api_key=azure_creds.AZURE_VISION_API_KEY,
#                         api_version=azure_creds.AZURE_VISION_API_VERSOIN,
#                         max_tokens=1024,
#                         azure_endpoint=azure_creds.AZURE_VISION_ENDPOINT)
#         self.project_js_file_content: str ="""

# import './App.css';
# {imports}

# function App() {{
#     return (
#         <div className="App">
#             {components}
#         </div>
#     );
# }}

# export default App;
# """

#     async def post(self, url: str, data: Dict[str, Any]):
#         async with aiohttp.ClientSession(headers=self.headers) as session:
#             async with session.post(url, json=data) as response:
#                 return response

#     def load_section(self) -> None:
#         try:
#             with open(self.section_repo_filepath, "r") as file:
#                 self.section_repo = json.load(file)
#                 self.section_repo_json_string = json.dumps(self.section_repo)
#         except Exception as e:
#             logger.error(f"Failed to load the REPO: {str(e)}")

#     @staticmethod
#     def add_input_and_schema(input: str, schema: str) -> str:
#         return """As an example, for the schema {{"properties": {{"foo": {{"title": "Foo", "description": "a list of strings", "type": "array", "items": {{"type": "string"}}}}}}, "required": ["foo"]}} the object {{"foo": ["bar", "baz"]}} is a well-formatted instance of the schema. The object {{"properties": {{"foo": ["bar", "baz"]}}}} is not well-formatted. \n Here is the output schema:```\n{schema}\n```\nHere is the Input in Json String:\n{input}""".format_map({"schema": schema, "input": input})

#     @staticmethod
#     def convert_to_js(data: Any) -> str:
#         if isinstance(data, dict):
#             js_code = []
#             for key, value in data.items():
#                 js_value = WebPageGenerator.convert_to_js(value)
#                 js_code.append(f'{key}: {js_value}')
#             return '{' + ', '.join(js_code) + '}'
#         elif isinstance(data, list) or isinstance(data, tuple):
#             js_values = [WebPageGenerator.convert_to_js(item) for item in data]
#             return '[' + ', '.join(js_values) + ']'
#         elif isinstance(data, bool):
#             return str(data).lower()
#         elif isinstance(data, str):
#             return f'"{data}"'
#         else:
#             return str(data)

#     async def generate_project_description_DA(self, user_input: str=None):
#         parser = PydanticOutputParser(pydantic_object=Schema_WebSiteInfo)
#         instruction = """
#                     You are an expert in generating webpage description.
#                     Yo task is to create webpage descriptions based on the user's input description and
#                     a hand-drawn or wireframe image of the webpage layout.
#                     Generate details for the attributes provided under the Output Format.
#                     If the given user input is not sufficient to answer or calculate the value for an attribute,
#                     then leave the attribute value as 'insufficient_info' and if attribute is a list then empty list.

#                     User Input Format:
#                     {{
#                         "project_name": "Project name provided by the user",
#                         "description": "User description about the webpage in detail",
#                         "webpage_sketch": "(optional) User hand-drawn image or wireframe of the webpage",
#                         "device": "Desktop or mobile",
#                         "technology": "React JS / Angular"
#                     }}

#                     Output Format:
#         """
#         prompt = PromptTemplate(
#             template="{instruction}\n{format_instructions}\nUser Input:\n{input_data}\nONLY RETURN THE JSON. DO NOT ADD ANY OTHER INSTRUCTIONS.\n",
#             input_variables=["input_data","instruction"],
#             partial_variables={"format_instructions": parser.get_format_instructions()},
#         )
#         print(prompt, flush=True)
#         # input_prompt = WebPageGenerator.add_input_and_schema(user_input, Schema_WebSiteInfo.model_json_schema())
#         payload = DARequest(mode=USECASE_PROMPT, promptOverride=False, userSignature=self.user_signature, prompt=prompt.format(input_data=user_input, instruction=instruction))
#         print('input Payload: ', payload, flush=True)
#         print('API Endpoint:', self.api_endpoint)
#         response = await self.api_client.post(self.api_endpoint,data=payload.model_dump())
#         print(f"resp:{response}", flush=True)
#         if not response:
#             #  Write rewrite mechanism with 2 tries
#             return {
#                 "status_code": 500,
#                 "error": "Error communicating with DA server"
#                 }
#         # print('Type of the response is:\n', response.choices[0].text)
#         try:
#             # response = DAResponse.model_validate(response)
#             print('Response:', response)
#             return json.loads(response['response']['choices'][0]['text'])
#         except Exception as e:
#             # TODO - Use GPT to fix issues with JSON
#             print('Error validating the Response from DA: Response -> ', response)
#         # TODO - If model validation fails, use another json parser as a backup to extract text.

#     async def pick_template_DA(self, template_description: str=None, web_description: str=None):
#         parser = PydanticOutputParser(pydantic_object=Schema_TemplateName)
#         instruction = """
# You are an expert in React JS, and your job is to pick a template based on the detailed description of
# multiple templates provided as a JSON string and the webpage description also provided as a JSON string.
# ONLY RETURN THE JSON. DO NOT ADD ANY OTHER INSTRUCTIONS AND KEYS.
#         """
#         prompt = PromptTemplate(
#             template="{instruction}\n{format_instructions}\nTemplates JSON String:\n{templates}\nWeb Description:\n{web_description}\n",
#             input_variables=["templates", "web_description", "instruction"],
#             partial_variables={"format_instructions": parser.get_format_instructions()},
#         )
#         print(prompt, flush=True)
#         # input_prompt = WebPageGenerator.add_input_and_schema(template_description, Schema_TemplateName.model_json_schema())
#         payload = DARequest(mode=USECASE_PROMPT, promptOverride=False, userSignature=self.user_signature, prompt=prompt.format(templates=template_description, web_description=web_description, instruction=instruction))
#         print('input Payload: ', payload, flush=True)
#         print(f"API Endpoint:{self.api_endpoint}", flush=True)
#         response = await self.api_client.post(self.api_endpoint,data=payload.model_dump())
#         # print("Hello", response, flush=True)
#         print(f"resp:{response}", flush=True)
#         if not response:
#             #  Write rewrite mechanism with 2 tries
#             return {
#                 "status_code": 500,
#                 "error": "Error communicating with DA server"
#                 }
#         else:
#             print(response, flush=True)
#         # print('Type of the response is:\n', response.choices[0].text)
#         try:
#             # response = DAResponse.model_validate(response)
#             print('Response:', response)
#             return json.loads(response['response']['choices'][0]['text'])
#         except Exception as e:
#             # TODO - Use GPT to fix issues with JSON
#             print('Error validating the Response from DA: Response -> ', response)
#             return {"Error": f"{e}", "Response":response}
#         # TODO - If model validation fails, use another json parser as a backup to extract text.

#     async def generate_page_descriptions_DA(self, web_description: str=None):
#         parser = PydanticOutputParser(pydantic_object=Schema_WebSite_Pages)
#         instruction = """
# You are an expert in generating description for pages in a webpage.
# Your task is to create descriptions 'pages' based on the input given as JSON String have description of the webpage.
# Generate details for the attributes provided under the Output Format.
# Every Attribute must be generated using the webpage description.

# Output Format:
#         """
#         prompt = PromptTemplate(
#             template="{instruction}\n{format_instructions}\nJSON String :\n{input_data}\n",
#             input_variables=["input_data","instruction"],
#             partial_variables={"format_instructions": parser.get_format_instructions()},
#         )
#         print(prompt, flush=True)
#         payload = DARequest(mode=USECASE_PROMPT, promptOverride=False, userSignature=self.user_signature, prompt=prompt.format(input_data=web_description, instruction=instruction))
#         print('input Payload: ', payload, flush=True)
#         response = await self.api_client.post(self.api_endpoint,data=payload.model_dump())
#         if not response:
#             #  Write rewrite mechanism with 2 tries
#             return {
#                 "status_code": 500,
#                 "error": "Error communicating with DA server"
#                 }
#         # print('Type of the response is:\n', response.choices[0].text)
#         try:
#             # response = DAResponse.model_validate(response)
#             print('Response:', response)
#             return json.loads(response['response']['choices'][0]['text'])
#         except Exception as e:
#             # TODO - Use GPT to fix issues with JSON
#             print('Error validating the Response from DA: Response -> ', response)
#             print(f"Error:{e}")
#         # TODO - If model validation fails, use another json parser as a backup to extract text.

#     async def generate_sections_using_pages_DA(self, page_description: str, template):
#         section_ref_info = """
#                     Here's a reference JSON string for sections. It's formatted as key-section pairs, where each key corresponds to a section.
#                     You can use these sections as a reference to generate sections.
#                     JSON String  for sections: {}
# """
#         # {key:{"SectionName": section["SectionName"], "description": section["SectionItems"]["description"]}}
#         section_info = json.dumps([{key: section} for key, section in self.section_repo.items() if int(key) in template["section_list"]], indent=4)
#         parser = PydanticOutputParser(pydantic_object=Schema_WebSite_Page_Sections)
#         instruction = """
# You are an expert in React JS and generating sections for pages in a website.
# Your task is to create section details in output format given below so that these section details can be used to create a page of website by combining different sections.
# Your input will be JSON string which have details of every pages in that website.
# """
#         prompt = PromptTemplate(
#             template="{instruction}\nOutput Format:\n{format_instructions}\nONLY OUTPUT THE JSON, DON'T ADD ANY OTHER INSTRUCTION OR COMMENTS\n{additional_info}\nJSON String :\n{input_data}\n",
#             input_variables=["input_data","instruction","additional_info"],
#             partial_variables={"format_instructions": parser.get_format_instructions()},
#         )
#         print(prompt, flush=True)
#         # chain = prompt | self.llm | parser
#         # print("input_data", page_description, "instruction", instruction, "additional_info",section_ref_info.format(self.section_repo_json_string))
#         # return  chain.invoke({"input_data": page_description, "instruction": instruction, "additional_info":section_ref_info.format(self.section_repo_json_string)}).dict()
#         payload = DARequest(mode=USECASE_PROMPT, promptOverride=False, userSignature=self.user_signature, prompt=prompt.format(input_data=page_description, additional_info=section_ref_info.format(section_info), instruction=instruction))
#         print('input Payload: ', payload, flush=True)
#         response = await self.api_client.post(self.api_endpoint,data=payload.model_dump())
#         if not response:
#             #  Write rewrite mechanism with 2 tries
#             return {
#                 "status_code": 500,
#                 "error": "Error communicating with DA server"
#                 }
#         # print('Type of the response is:\n', response.choices[0].text)
#         try:
#             # response = DAResponse.model_validate(response)
#             print('Response:', response)
#             return json.loads(response['response']['choices'][0]['text'])
#         except Exception as e:
#             # TODO - Use GPT to fix issues with JSON
#             print('Error validating the Response from DA: Response -> ', response)
#         # TODO - If model validation fails, use another json parser as a backup to extract text.

#     async def generate_website_page_js_content_DA_single(self, page_details, template):
#             imports_text = ""
#             imports_text_template = """
#         import {} from './components_repo/{}';
#         """
#             html_content_text = ""
#             js_const_props = ""
#             js_const_variable = """
#         const {} = {};
#         """
#             html_content_text_template = """
#             <{}/>
#         """
#             model_str = "from pydantic import BaseModel, Field\nfrom typing import List, Dict, Optional, Union\n"
#             section_descriptions = ""
#             schema_prop = "\n\nclass Schema_Props(BaseModel):"

#             for gpt_section in page_details["section_list"]:
#                 try:
#                     module_name = gpt_section["SectionName"]
#                     print(f"Searching For Section: {module_name} in REPO...", flush=True)
#                     key = self.search_section_repo(gpt_section, template)
#                     if key:
#                         section = self.section_repo[key]
#                         model_str += section["props_model"].format(index=int(key))
#                         section_descriptions += f"section_{int(key)}:\nSection Header:{section['SectionHeader']}\nSection Description:{section['SectionItems']['description']}\n\n"
#                         schema_prop += f"\n    section_{int(key)}: Schema_Props_{int(key)}"
#                 except Exception as e:
#                     print(f"Error: {e}\nFailed to load model string for GPT Section: {gpt_section}")

#             model_str += schema_prop
#             print(f"model_str: {model_str}")
#             print(f"section_descriptions: {section_descriptions}")

#             print("Generate props...", flush=True)
#             section_props_raw = await SectionPrompt(
#                 model_str, self.api_client, self.user_signature, self.api_endpoint
#             ).get_props_data_DA_single(section_descriptions, page_details["page_description"])

#             print(type(section_props_raw))
#             print(f"Generated Props:\n{section_props_raw}", flush=True)

#             start_index = section_props_raw.find('{')
#             end_index = section_props_raw.rfind('}') + 1

#             section_props = section_props_raw[start_index:end_index]
#             print(f"section:{section_props}", flush=True)
#             if isinstance(section_props, str):
#                 try:
#                     section_props = json.loads(section_props)
#                 except json.JSONDecodeError as e:
#                     print(f"JSONDecodeError: {e}\nInvalid JSON string: {section_props}")
#                     return None

#             print(f"Decoded section_props:\n{section_props}")

#             for section_key, section_prop in section_props.items():
#                 try:
#                     key = section_key.split("_")[1]
#                     if key:
#                         section = self.section_repo[key]
#                         print(f"Match Found. Key: {key}\tHeader: {section['SectionHeader']}", flush=True)
#                         imports_text += imports_text_template.format(section["SectionHeader"], f"{key}/{section['SectionHeader']}")
#                         js_const_props += "".join(js_const_variable.format(f"{_}{key}Data", self.convert_to_js(section_prop[_])) for _ in section['props'])
#                         html_content_text += html_content_text_template.format(f"{section['SectionHeader']} {' '.join([f'{_}={{{_}{key}Data}}' for _ in section['props']])}")
#                         print("Section Processing Completed.", flush=True)
#                 except Exception as e:
#                     print(f"Error: {e}\nFailed to generate props for GPT Section: {section_key}")


#             return template["code"].format(js_const_props, html_content_text)


#     async def convert_code(self, user_input: Dict[str, Any]) -> str:
#         payload = {
#             "mode": user_input["mode"],
#             "userSignature": user_input["userSignature"],
#             "prompt": user_input["prompt"],
#             "useCaseIdentifier": user_input["useCaseIdentifier"]
#         }
#         try:
#             print('Input Payload: ', payload, flush=True)
#             response = await self.api_client.post(self.api_endpoint, data=payload)
#             if not response:
#                 return {
#                     "status_code": 500,
#                     "error": "Error communicating with DA server"
#                 }
#             response_json = await response.json() if hasattr(response, 'json') else response
#             print(f'Response JSON: {response_json}', flush=True)
#             choices = response_json.get('response', {}).get('choices', [])
#             if not choices:
#                 raise ValueError("No choices found in the response")

#             code = choices[0].get('text', '')

#             if not code:
#                 raise ValueError("No text found in the choice")
#             start_tags = ["```html", "```jsx", "```json", "```css", "```typescript"]
#             end_tag = "```"

#             output_code = {}
#             start_index = 0

#             while start_index != -1:
#                 start_index = code.find('### ', start_index)
#                 if start_index == -1:
#                     break

#                 end_filename_index = code.find('\n', start_index)
#                 if end_filename_index == -1:
#                     raise ValueError("No newline found after file name")

#                 filename = code[start_index + 4:end_filename_index].strip()

#                 code_start_index = -1
#                 for start_tag in start_tags:
#                     code_start_index = code.find(start_tag, end_filename_index)
#                     if code_start_index != -1:
#                         break

#                 if code_start_index == -1:
#                     start_index = end_filename_index + 1
#                     continue

#                 code_end_index = code.find(end_tag, code_start_index + len(start_tag))
#                 if code_end_index == -1:
#                     raise ValueError(f"No valid end tag found for {filename}")

#                 code_block = code[code_start_index + len(start_tag):code_end_index].strip()
#                 output_code[filename] = code_block

#                 start_index = code_end_index + len(end_tag)

#             # If no valid code blocks were found using the first approach, try the second approach
#             if not output_code:
#                 start_index = -1
#                 for start_tag in start_tags:
#                     start_index = code.find(start_tag)
#                     if start_index != -1:
#                         break

#                 if start_index == -1:
#                     raise ValueError("No valid start tag found")

#                 end_index = code.find(end_tag, start_index + len(start_tag))
#                 if end_index == -1:
#                     raise ValueError("No valid end tag found")

#                 output_code = code[start_index + len(start_tag):end_index].strip()

#             return output_code
#         except httpx.RequestError as e:
#             raise Exception(f"HTTP request error: {str(e)}")
#         except httpx.HTTPStatusError as e:
#             raise Exception(f"HTTP status error: {str(e)}: {e.response.text}")
#         except Exception as e:
#             raise Exception(f"An error occurred: {str(e)}")


#     async def generate_design_generation(self, user_input: Dict[str, Any]):
#         payload = {
#             "mode": user_input["mode"],
#             "userSignature": user_input["userSignature"],
#             "prompt": user_input["prompt"],
#             "useCaseIdentifier": user_input["mode"]+da_creds.DA_USECASE_IDENTIFIER,
#         }
#         try:
#             print('Input Payload: ', payload, flush=True)
#             response = await self.api_client.post(self.api_endpoint, data=payload)
#             if not response:
#                 return {
#                         "status_code": 500,
#                         "error": "Error communicating with DA server"
#                     }
#             response_json = await response.json() if hasattr(response, 'json') else response
#             print(f'Response JSON: {response_json}', flush=True)
#             choices = response_json.get('response', {}).get('choices', [])
#             if not choices:
#                 raise ValueError("No choices found in the response")

#             code = choices[0].get('text', '')
#             return code
#         except httpx.RequestError as e:
#             raise Exception(f"HTTP request error: {str(e)}")
#         except httpx.HTTPStatusError as e:
#             raise Exception(f"HTTP status error: {str(e)}: {e.response.text}")
#         except Exception as e:
#             raise Exception(f"An error occurred: {str(e)}")

#     async def generate_website_page_js_content_DA(self, page_details, template):
#         imports_text :str = ""
#         imports_text_template :str = """
# import {} from './components_repo/{}';
# """
#         html_content_text :str =""
#         js_const_props = ""
#         js_const_variable = """
# const {} = {};
# """
#         html_content_text_template :str = """
#         <{}/>
# """
#         for gpt_section in page_details["section_list"]:
#             try:
#                 module_name = gpt_section["SectionName"]
#                 print(f"Searching For Section: {module_name} in REPO...", flush=True)
#                 key = self.search_section_repo(gpt_section)
#                 if key:
#                     section = self.section_repo[key]
#                     print(f"Match Found. Key:{key}\tHeader:{section['SectionHeader']}", flush=True)
#                     imports_text += imports_text_template.format(section["SectionHeader"], f"{key}/{section['SectionHeader']}")
#                     if section["props_model"] != "":
#                         print("Generate props...", flush=True)
#                         section_data = await SectionPrompt(section["props_model"], self.api_client, self.user_signature, self.api_endpoint).get_props_data_DA(section["SectionHeader"], section["SectionItems"]["description"], page_details["page_description"])
#                         print(f"Generated Props:\n{section_data}", flush=True)
#                         js_const_props += "".join(js_const_variable.format(f"{_}{key}Data", self.convert_to_js(section_data[_])) for _ in section['props'])
#                     html_content_text += html_content_text_template.format(f"{section['SectionHeader']} {' '.join([f'{_}={{{_}{key}Data}}' for _ in section['props']])}")
#                     print("Section Processing Completed.", flush=True)
#             except Exception as e:
#                 print(f"Error :{e}\nFailed to generate props for GPT Section:{gpt_section}")
#         # with open(output_path, "w") as js_file:
#         #     js_file.write(self.project_js_file_content.format(imports_text, html_content_text))
#         # print(f"{output_path} is Generated..", flush=True)
#         return template["code"].format(js_const_props, html_content_text)

#     def generate_project_description(self, user_input: str=None):
#         parser = PydanticOutputParser(pydantic_object=Schema_WebSiteInfo)
#         instruction = """
#                     You are an expert in generating webpage description.
#                     Yo task is to create webpage descriptions based on the user's input description and
#                     a hand-drawn or wireframe image of the webpage layout.
#                     Generate details for the attributes provided under the Output Format.
#                     If the given user input is not sufficient to answer or calculate the value for an attribute,
#                     then leave the attribute value as 'insufficient_info' and if attribute is a list then empty list.

#                     User Input Format:
#                     {{
#                         "project_name": "Project name provided by the user",
#                         "description": "User description about the webpage in detail",
#                         "webpage_sketch": "(optional) User hand-drawn image or wireframe of the webpage",
#                         "device": "Desktop or mobile",
#                         "technology": "React JS / Angular"
#                     }}

#                     Output Format:
#         """
#         prompt = PromptTemplate(
#             template="{instruction}\n{format_instructions}\nUser Input:\n{input_data}\n",
#             input_variables=["input_data","instruction"],
#             partial_variables={"format_instructions": parser.get_format_instructions()},
#         )
#         print(prompt.format)
#         print(parser.get_format_instructions(), flush=True)
#         chain = prompt | self.llm | parser
#         if user_input is None or not isinstance(user_input, str):
#             return False
#         data =  chain.invoke({"input_data": user_input, "instruction": instruction}).dict()
#         with open("webpage_desc.json", "w") as file:
#             json.dump(data, file, indent=4)
#         return data

#     def generate_page_descriptions(self, web_page_desc=None):
#         if web_page_desc is None or not (isinstance(web_page_desc, str) or isinstance(web_page_desc, dict)):
#             with open("webpage_desc.json", 'r') as file:
#                 data_dict = json.load(file)
#             web_page_description = json.dumps(data_dict)
#         elif isinstance(web_page_desc, dict):
#             web_page_description = json.dumps(web_page_desc)
#         else:
#             web_page_description = web_page_desc
#         parser = PydanticOutputParser(pydantic_object=Schema_WebSite_Pages)
#         instruction = """
#                     You are an expert in generating description for pages in a webpage.
#                     Your task is to create descriptions 'pages' based on the input given as JSON String have description of the webpage.
#                     Generate details for the attributes provided under the Output Format.
#                     Every Attribute must be generated using the webpage description.

#                     Output Format:
#         """
#         prompt = PromptTemplate(
#             template="{instruction}\n{format_instructions}\nJSON String :\n{input_data}\n",
#             input_variables=["input_data","instruction"],
#             partial_variables={"format_instructions": parser.get_format_instructions()},
#         )
#         print(parser.get_format_instructions(), flush=True)
#         chain = prompt | self.llm | parser
#         data =  chain.invoke({"input_data": web_page_description, "instruction": instruction}).dict()
#         with open("website_page_details.json", "w") as file:
#             json.dump(data, file, indent=4)
#         return data

#     def generate_sections_using_pages(self, website_pages=None):
#         if website_pages is None or not (isinstance(website_pages, str) or isinstance(website_pages, list)):
#             with open("website_page_details.json", 'r') as file:
#                 data_dict = json.load(file)
#             website_page_details = json.dumps(data_dict["pages"])
#         elif isinstance(website_pages, list):
#             website_page_details = json.dumps(website_pages)
#         elif isinstance(website_pages, str):
#             website_page_details = website_pages
#         else:
#             False
#         parser = PydanticOutputParser(pydantic_object=Schema_WebPage_All_Page_Sections)
#         instruction = """
#                     You are an expert in React JS and generating sections for pages in a website.
#                     Your task is to create section details in output format given below so that these section details can be used to create a page of website by combining different sections.
#                     Your input will be JSON string which have details of every pages in that website.

#                     Output Format:
#         """
#         prompt = PromptTemplate(
#             template="{instruction}\n{format_instructions}\nONLY OUTPUT THE JSON, DON'T ADD ANY OTHER INSTRUCTION OR COMMENTS\n{additional_info}\nJSON String :\n{input_data}\n",
#             input_variables=["input_data", "additional_info", "instruction"],
#             partial_variables={"format_instructions": parser.get_format_instructions()},
#         )
#         print(parser.get_format_instructions(), flush=True)
#         section_ref_info = """
#                     Here's a reference JSON string for sections. It's formatted as key-section pairs, where each key corresponds to a section.
#                     You can use these sections as a reference to generate sections.
#                     JSON String  for sections: {}
# """
#         chain = prompt | self.llm | parser
#         print("input_data", website_page_details, "instruction", instruction, "additional_info",section_ref_info.format(self.section_repo_json_string))
#         data =  chain.invoke({"input_data": website_page_details, "instruction": instruction, "additional_info":section_ref_info.format(self.section_repo_json_string)}).dict()
#         with open("website_page_section_details.json", "w") as file:
#             json.dump(data, file, indent=4)
#         return data


#     def generate_website_page_js_content(self, page_details):
#         imports_text :str = ""
#         imports_text_template :str = """
# import {} from './components_repo/{}';
# """
#         html_content_text :str =""
#         js_const_variable = """
# const {} = {};
# """
#         html_content_text_template :str = """
#         <{}/>
# """
#         for gpt_section in page_details["section_list"]:
#             module_name = gpt_section["SectionName"]
#             print(f"Searching For Section: {module_name} in REPO...", flush=True)
#             key = self.search_section_repo(gpt_section)
#             if key:
#                 section = self.section_repo[key]
#                 print(f"Match Found. Key:{key}\tHeader:{section['SectionHeader']}", flush=True)
#                 imports_text += imports_text_template.format(section["SectionHeader"], f"{key}/{section['SectionHeader']}")
#                 if section["props_model"] != "":
#                     print("Generate props...", flush=True)
#                     section_data = SectionPrompt(section["props_model"]).get_props_data(self.llm, section["SectionHeader"], section["SectionItems"]["description"], page_details["page_description"])
#                     imports_text += "".join(js_const_variable.format(f"{_}{key}Data", self.convert_to_js(section_data[_])) for _ in section['props'])
#                 html_content_text += html_content_text_template.format(f"{section['SectionHeader']} {' '.join([f'{_}={{{_}{key}Data}}' for _ in section['props']])}")
#                 print("Section Processing Completed.", flush=True)
#         # with open(output_path, "w") as js_file:
#         #     js_file.write(self.project_js_file_content.format(imports_text, html_content_text))
#         # print(f"{output_path} is Generated..", flush=True)
#         return self.project_js_file_content.format(imports_text, html_content_text)

#     def generate_project_app_js_content(self):
#         self.load_section()
#         result = self.generate_sections()
#         gpt_gen_sections = result["section_list"]
#         print(f"{len(gpt_gen_sections)} Sections Generated", flush=True)
#         project_title = result["project_title"]
#         imports_text :str = ""
#         imports_text_template :str = """
# import {} from './components_repo/{}';
# """
#         html_content_text :str =""
#         js_const_variable = """
# const {} = {};
# """
#         html_content_text_template :str = """
#         <{}/>
# """
#         webpage_description = """
#             project_name: MusicJam
#             description: Create a user friendly web page to search and store music
#             device: Desktop
#             technology: React JS
#             """
#         for gpt_section in gpt_gen_sections:
#             module_name = gpt_section["SectionName"]
#             print(f"Searching For Section: {module_name} in REPO...", flush=True)
#             key = self.search_section_repo(gpt_section)
#             if key:
#                 section = self.section_repo[key]
#                 print(f"Match Found. Key:{key}\tHeader:{section['SectionHeader']}", flush=True)
#                 imports_text += imports_text_template.format(section["SectionHeader"], f"{key}/{section['SectionHeader']}")
#                 if section["props_model"] != "":
#                     print("Generate props...", flush=True)
#                     section_data = SectionPrompt(section["props_model"]).get_props_data(self.llm, section["SectionHeader"], section["SectionItems"]["description"], webpage_description)
#                     imports_text += "".join(js_const_variable.format(f"{_}{key}Data", self.convert_to_js(section_data[_])) for _ in section['props'])
#                 html_content_text += html_content_text_template.format(f"{section['SectionHeader']} {' '.join([f'{_}={{{_}{key}Data}}' for _ in section['props']])}")
#                 print("Section Processing Completed.", flush=True)
#         with open(self.project_app_js_path, "w") as js_file:
#             js_file.write(self.project_js_file_content.format(imports_text, html_content_text))
#         print("App Js is Generated..", flush=True)

#     def search_section_repo(self, section, template):
#         key =self.search_by_section_name(section, template)
#         if key:
#             return key
#         key = self.search_by_mandatory_child(section["SectionItems"]["mandatory_children"])
#         if key:
#             return key
#         else:
#             key = self.search_by_description_gpt(section["SectionItems"]["description"])
#             if key in self.section_repo:
#                 return key
#             elif section["SectionName"] == "Header":
#                 return "1"
#             elif section["SectionName"] == "Footer":
#                 return "2"
#             else:
#                 return False

#     def search_by_section_name(self, gpt_section, template):
#         for key, section in self.section_repo.items():
#             if int(key) in template["section_list"]:
#                 if section["SectionName"] == gpt_section["SectionName"]:
#                     return key

#     def search_by_description_gpt(self, req_description):
#         temp_section_desc = {}
#         for key, section in self.section_repo.items():
#             temp_section_desc[key] = section["SectionItems"]["description"]
#         json_string_sections = json.dumps(temp_section_desc)
#         parser = PydanticOutputParser(pydantic_object=Schema_SectionID)
#         instruction = """
# You are an expert in searching, who sifts through descriptions to find relevant information.
# The input consists of a JSON string and a required description (in string format).
# In the JSON string, each element is a key-value pair where each key corresponds to a description.
# Your task is to search the required description within the description values in the JSON string,
# find the closest matching description, and return its corresponding key or return '0000000000000' if doesn't found.
#         """
#         prompt = PromptTemplate(
#             template="{instruction}\n{format_instructions}\n{input_data}\n",
#             input_variables=["input_data","instruction"],
#             partial_variables={"format_instructions": parser.get_format_instructions()},
#         )
#         # print(parser.get_format_instructions(), flush=True)
#         chain = prompt | self.llm | parser
#         input_data_string = """
# JSON String : {}
# Required description: {}
# """
#         return chain.invoke({"input_data": input_data_string.format(json_string_sections, req_description), "instruction": instruction}).dict()["section_ID"]

#     def search_by_mandatory_child(self, mandatory_children: list, temp_section_repo_keys: list=None, check_order: bool=False):
#         mandatory_children_set = set(mandatory_children)
#         if set(["h1", "h2", "h3", "h4", "h5", "h6"]).intersection(mandatory_children_set):
#             modified_mandatory_children = ["h" if _ in ["h1", "h2", "h3", "h4", "h5", "h6"] else _ for _ in mandatory_children]
#             if check_order:
#                 for key in self.section_repo.keys() if temp_section_repo_keys is None else temp_section_repo_keys:
#                     if ["h" if _ in ["h1", "h2", "h3", "h4", "h5", "h6"] else _ for _ in self.section_repo[key]["SectionItems"]["mandatory_children"]] == modified_mandatory_children:
#                         return key
#             else:
#                 mandatory_children_set = set(modified_mandatory_children)
#                 for key in self.section_repo.keys() if temp_section_repo_keys is None else temp_section_repo_keys:
#                     if set(self.section_repo[key]["SectionItems"]["mandatory_children"]) == mandatory_children_set:
#                         return key
#         else:
#             if check_order:
#                 for key in self.section_repo.keys() if temp_section_repo_keys is None else temp_section_repo_keys:
#                     if self.section_repo[key]["SectionItems"]["mandatory_children"] == mandatory_children:
#                         return key
#             else:
#                 for key in self.section_repo.keys() if temp_section_repo_keys is None else temp_section_repo_keys:
#                     if set(self.section_repo[key]["SectionItems"]["mandatory_children"]) == mandatory_children_set:
#                         return key
#         return False

#     def generate_sections(self, description=None):
#         parser = PydanticOutputParser(pydantic_object=Schema_WebProject)
#         print(parser.get_format_instructions(), flush=True)
#         instruction = """
#                     Your are an expert in React JS.
#                     Based on the user's description and a hand-drawn or wireframe image of the webpage layout,
#                     Divide the webpage into meaningful sections and generate a list of sections in the following format:
#                     Create section details in JSON format so that these section details can be used to create a webpage by combining different sections.

#                     User Inputs:
#                         project_name: Project name provided by the user
#                         description: User description about the webpage in detail
#                         webpage_sketch: (optional) User hand-drawn image or wireframe of the webpage
#                         device: Desktop or mobile
#                         technology: React JS / Angular
#         """
#         prompt = PromptTemplate(
#             template="{instruction}\n{additional_info}\n{format_instructions}\n{query}\n",
#             input_variables=["query","instruction", "additional_info"],
#             partial_variables={"format_instructions": parser.get_format_instructions()},
#         )
#         section_ref_info = """
#                     Here's a reference JSON string. It's formatted as key-section pairs, where each key corresponds to a section.
#                     You can use these sections as a reference to generate sections.
#                     JSON String: {}
# """
#         chain = prompt | self.llm | parser
#         webpage_description = """
#             project_name: MusicJam
#             description: Create a user friendly web page to search and store music
#             device: Desktop
#             technology: React JS
#             """
#         if description is None:
#             description = webpage_description
#         return chain.invoke({"query": description, "instruction": instruction, "additional_info":section_ref_info.format(self.section_repo_json_string)}).dict()

#     async def generate_product_research_understanding(self, user_input: Dict[str, Any]):

#         VALID_KEYS = [
#         "Problem", "Solution", "Key Metrics", "Value Proposition",
#         "High-Level Concepts", "Unfair Advantage", "Cost Structure",
#         "Early Adopters", "Revenue Streams", "Customer Segments",
#         "Alternatives"
#         ]

#         payload = DARequest(mode=user_input["mode"], promptOverride=False, useCaseIdentifier=user_input["mode"]+da_creds.DA_USECASE_IDENTIFIER,userSignature=user_input["userSignature"], prompt=user_input["prompt"])
#         try:
#             print('Input Payload: ', payload, flush=True)
#             response = await self.api_client.post(self.api_endpoint, data=payload.model_dump())
#             if not response:
#                 return {
#                     "status_code": 500,
#                     "error": ["Error communicating with DA server"]
#                 }
#             response_json = await response.json() if hasattr(response, 'json') else response
#             print(f'Response JSON: {response_json}', flush=True)
#             choices = response_json.get('response', {}).get('choices', [])

#             if not choices:
#                 raise ValueError("No choices found in the response")

#             understanding_text = choices[0].get('text', '')
#             if not understanding_text:
#                 raise ValueError("No text found in the choice")
#             content = understanding_text.split('```plaintext\n')[-1].split('```')[0].strip()
#             focus_header = user_input["prompt"].split("Focus Header:")[1].split("\n")[0].strip()
#             if focus_header in VALID_KEYS:
#                 content = content.split(f"{focus_header}:", 1)[-1].strip()
#                 understanding_list = [
#                     item.strip().lstrip('-').strip()
#                     for item in content.split('\n')
#                     if item.strip() and not item.lower().startswith(f"{focus_header.lower()}:")
#                 ]
#                 return {
#                 "type": focus_header,
#                 "data": understanding_list
#                 }
#             else:
#                 raise ValueError(f"Focus header '{focus_header}' is not in the valid keys list.")
#         except httpx.RequestError as e:
#             raise Exception(f"HTTP request error: {str(e)}")
#         except httpx.HTTPStatusError as e:
#             raise Exception(f"HTTP status error: {str(e)}: {e.response.text}")
#         except Exception as e:
#             raise Exception(f"{str(e)}")


# if __name__ == "__main__":
#     WebPageGenerator().generate_sections_using_pages()
