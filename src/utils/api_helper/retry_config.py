import httpx

from src.settings.settings import DevConfig

RETRY_COUNT = DevConfig().API_RETRY_COUNTS
WAIT_TIME = DevConfig().API_RETRY_WAIT_TIME


def retry_if_500_or_timeout(exception: Exception) -> bool:
    """Return True if we should retry (on status code 500 or timeout errors)."""
    if isinstance(exception, httpx.HTTPStatusError):
        return exception.response.status_code == 500
    if isinstance(exception, httpx.TimeoutException):
        return True
    return False
