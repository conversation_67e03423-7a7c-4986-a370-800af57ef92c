import asyncio
import j<PERSON>
from typing import Any, Dict, Optional

import httpx
from fastapi import HTTPException

from src.utils.logger import AppLogger


class APIClient:
    def __init__(self, base_url: str, headers: Optional[Dict[str, str]] = None):
        self.base_url = base_url
        self.headers = headers or {}
        self.logger = AppLogger("Api_Client_Logger").get_logger()

    async def _handle_response(self, response: httpx.Response) -> Any:
        try:
            response.raise_for_status()
            return response.json()
        except httpx.HTTPStatusError as e:
            status_code = e.response.status_code
            try:
                error_detail = e.response.json()
            except json.JSONDecodeError:
                error_detail = e.response.text
            self.logger.error(
                f"HTTP error occurred: {e}\nStatus Code: {status_code}\nDetail: {error_detail}"
            )
            raise HTTPException(
                status_code=status_code, detail=f"HTTP error occurred: {error_detail}"
            )
        except httpx.RequestError as e:
            self.logger.error(f"Request error occurred: {e}")
            raise HTTPException(status_code=500, detail=f"Request error occurred: {e}")
        except Exception as e:
            self.logger.error(f"An unexpected error occurred: {e}")
            raise HTTPException(
                status_code=500, detail=f"An unexpected error occurred: {e}"
            )

    # @retry(retry=retry_if_exception(retry_if_500_or_timeout), stop=stop_after_attempt(RETRY_COUNT), wait=wait_fixed(WAIT_TIME))
    async def get(self, endpoint: str, params: Optional[Dict[str, Any]] = None) -> Any:
        url = self.base_url.rstrip("/") + "/" + endpoint.lstrip("/")
        async with httpx.AsyncClient() as client:
            try:
                response = await client.get(url, headers=self.headers, params=params)
                return await self._handle_response(response)
            except httpx.TimeoutException as e:
                self.logger.error(f"Timeout error occurred: {e}")
                raise HTTPException(
                    status_code=504, detail=f"Timeout error occurred: {e}"
                )

    # @retry(retry=retry_if_exception(retry_if_500_or_timeout), stop=stop_after_attempt(RETRY_COUNT), wait=wait_fixed(WAIT_TIME))
    async def post(self, endpoint: str, data: Optional[Dict[str, Any]] = None) -> Any:

        url = self.base_url.rstrip("/") + "/" + endpoint.lstrip("/")
        async with httpx.AsyncClient(timeout=1800) as client:
            try:
                response = await client.post(url, headers=self.headers, json=data)
                return await self._handle_response(response)
            except httpx.TimeoutException as e:
                self.logger.error(f"Timeout error occurred: {e}")
                raise HTTPException(
                    status_code=504, detail=f"Timeout error occurred: {e}"
                )

    async def put(self, endpoint: str, data: Optional[Dict[str, Any]] = None):
        url = self.base_url.rstrip("/") + "/" + endpoint.lstrip("/")
        async with httpx.AsyncClient(timeout=1800) as client:
            try:
                response = await client.put(url, headers=self.headers, json=data)
                return await self._handle_response(response)
            except httpx.TimeoutException as e:
                self.logger.error(f"Timeout error occurred: {e}")
                raise HTTPException(
                    status_code=504, detail=f"Timeout error occurred: {e}"
                )

    async def parallel_api_calls(
        self, requests: list[Dict[str, Any]], count: int = 1
    ) -> list[Any]:
        async def make_request(request: Dict[str, Any]) -> Any:
            method = request.get("method")
            endpoint = request.get("endpoint")
            payload = request.get("payload")
            headers = request.get("headers", self.headers)
            base_url = request.get("base_url", self.base_url)

            if method == "get":
                return await self.get(
                    endpoint, params=payload, headers=headers, base_url=base_url
                )
            elif method == "post":
                return await self.post(
                    endpoint, data=payload, headers=headers, base_url=base_url
                )
            else:
                raise ValueError(f"Unsupported method: {method}")

        tasks = []
        if len(requests) == 1:
            for _ in range(count):
                tasks.append(make_request(requests[0]))
        else:
            for req in requests:
                tasks.append(make_request(req))

        try:
            return await asyncio.gather(*tasks)
        except Exception as e:
            self.logger.error(f"An error occurred during parallel API calls: {e}")
            raise HTTPException(
                status_code=500,
                detail=f"An error occurred during parallel API calls: {e}",
            )
