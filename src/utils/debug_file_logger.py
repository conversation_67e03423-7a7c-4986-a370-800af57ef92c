import os
import json
from typing import Any

# Helper function to make custom objects JSON serializable
def json_serializer_default(o: Any) -> Any:
    """
    A default function for json.dump to handle custom objects.
    It converts objects to their dictionary representation if possible.
    """
    if hasattr(o, '__dict__'):
        return o.__dict__
    # For objects that don't have a __dict__ (like sets, etc.),
    # convert them to their string representation.
    # You could also add more specific handlers here if needed.
    # e.g., if isinstance(o, datetime.date): return o.isoformat()
    return str(o)


class DebugFileLogger:
    """
    Utility class for logging content to files for debugging purposes.
    """

    @staticmethod
    def log_to_file(file_name: str, content: Any) -> None:
        """
        Logs the given content to the specified file.
        Only generates files when ENVIRONMENT is set to "dev".

        Args:
            file_name (str): The name of the file to log to.
            content (Any): The content to log to the file. It will be serialized to JSON if not a string.
        """
        # Check if environment is dev, otherwise skip file generation
        environment = os.getenv("ENVIRONMENT", "").lower()
        if environment != "dev":
            return

        # Create logs directory if it doesn't exist
        logs_dir = os.path.join(os.getcwd(), "logs")
        if not os.path.exists(logs_dir):
            os.makedirs(logs_dir)

        # Combine logs directory with file name
        if not os.path.isabs(file_name):
            file_name = os.path.join(logs_dir, file_name)
        try:
            with open(file_name, "w", encoding="utf-8") as f:
                if isinstance(content, str):
                    f.write(content)
                else:
                    # THE FIX IS HERE: Use the 'default' parameter to handle custom objects
                    json.dump(
                        content,
                        f,
                        indent=4,
                        ensure_ascii=False,
                        default=json_serializer_default
                    )
            # print(f"Content successfully logged to {file_name}")
        except Exception as e:
            print(f"Error logging to file {file_name}: {e}")
