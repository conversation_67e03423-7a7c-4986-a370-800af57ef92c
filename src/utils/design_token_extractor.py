from typing import Dict, Any

from src.utils.debug_file_logger import DebugFileLogger
from src.utils.design_system_parser import convert_tailwind_to_design_tokens
from src.utils.logger import AppLogger

logger = AppLogger("DesignTokenExtractor").get_logger()


async def extract_design_tokens(
    design_system_code: Dict[str, Any], request_id: str
) -> Dict[str, Any]:
    """
    Extract design tokens from the generated design system code.

    Args:
        design_system_code: Dictionary containing design system files
        request_id: Unique identifier for the request

    Returns:
        Dictionary containing extracted design tokens or original design system code
    """
    try:
        if (
            isinstance(design_system_code, dict)
            and "src/index.css" in design_system_code
            and "tailwind.config.ts" in design_system_code
        ):
            logger.info("Design Tokens | Extracting design tokens")
            design_tokens = convert_tailwind_to_design_tokens(design_system_code)
            color_count = len(design_tokens.get("design_tokens", {}).get("colors", []))
            logger.info(f"Design Tokens | Extracted {color_count} colors ✓")
            DebugFileLogger.log_to_file(
                f"design_tokens_{request_id}.json", design_tokens
            )
        else:
            logger.info("Design Tokens | Using original design system")
            design_tokens = design_system_code
    except Exception as e:
        logger.error(f"Design Tokens | Token extraction failed: {e}")
        design_tokens = design_system_code

    return design_tokens


def create_design_token_metadata(design_tokens: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create metadata object for design tokens to be included in state updates.

    Args:
        design_tokens: Dictionary containing design tokens

    Returns:
        Dictionary containing metadata for state updates
    """
    return {
        "type": "artifact",
        "data": {
            "data": design_tokens,
            "type": "json",
        },
    }
