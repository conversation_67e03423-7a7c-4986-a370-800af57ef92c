from fastapi import HTTPException as FastAPIHTTPException  # Renamed to avoid clash
from pydantic import ValidationError

from src.exceptions.exception import (
    DAServerError,
    DBConnectionError,
    ValidationAPIError,
    BaseAPIException,
)
from src.model.error_codes import APIErrorCode  # For the generic unexpected error


import logging

logger = logging.getLogger(__name__)  # Example


def handle_exceptions_and_log(logger):  # Renamed for clarity
    def decorator(func):
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except ValidationError as e:
                logger.error(
                    f"Pydantic validation error in {func.__name__}: {e.errors()}"
                )
                raise ValidationAPIError(validation_error=e)
            except DBConnectionError as e:
                logger.debug(
                    f"DB Connection Exception ({e.error_code}) in {func.__name__}: {e.message}"
                )
                raise
            except DAServerError as e:
                logger.debug(
                    f"DA Server Exception ({e.error_code}) in {func.__name__}: {e.message}"
                )
                raise
            except BaseAPIException as e:  # Our custom exceptions
                # Logged by the exception handler, but can add context here if needed
                logger.debug(
                    f"API Exception ({e.error_code}) in {func.__name__}: {e.message}"
                )
                raise
            except FastAPIHTTPException as e:  # Standard FastAPI HTTPException
                logger.warning(
                    f"FastAPI HTTPException in {func.__name__}: Status {e.status_code}, Detail {e.detail}"
                )
                raise
            except Exception as e:
                logger.exception(
                    f"Unexpected error in {func.__name__}: {e}"
                )  # Use .exception for traceback
                # Wrap it in a BaseAPIException for consistent handling
                raise BaseAPIException(
                    status_code=500,
                    error_code=APIErrorCode.INTERNAL_SERVER_ERROR,
                    message=f"An unexpected error occurred in {func.__name__}.",
                    details=str(
                        e
                    ),  # Be cautious about exposing raw error messages in production
                )

        return wrapper

    return decorator
