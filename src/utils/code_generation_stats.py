import datetime
from typing import List, Dict, Any, <PERSON>ple

from src.utils.logger import AppLogger

logger = AppLogger("CodeGenerationStats").get_logger()


def calculate_code_generation_stats(
    identified_files: Dict[str, Any],
    generated_code: List[Dict[str, str]],
    start_time: datetime.datetime,
) -> Tuple[int, int, float, List[str], datetime.timedelta]:
    """
    Calculate statistics for code generation process.

    Args:
        identified_files: Dictionary containing files to generate
        generated_code: List of generated file objects
        start_time: Start time of the generation process

    Returns:
        Tuple containing:
        - total_files: Number of files that were planned to be generated
        - generated_files: Number of files actually generated
        - success_rate: Percentage of files successfully generated
        - error_files: List of files with error markers
        - duration: Total duration of the generation process
    """
    # Calculate statistics
    total_files = len(identified_files.get("filesToGenerate", []))
    generated_files = len(generated_code)
    success_rate = (generated_files / total_files) * 100 if total_files > 0 else 0

    # Check for error markers
    error_files = []
    for file_info in generated_code:
        if "// ERROR:" in file_info.get("content", ""):
            error_files.append(file_info.get("fileName", "unknown"))

    # Calculate duration
    duration = datetime.datetime.now() - start_time

    # Log summary
    logger.info(
        f"COMPLETE | Generated {generated_files}/{total_files} files ({success_rate:.1f}%) in {duration}"
    )

    if error_files:
        logger.warning(f"Errors | {len(error_files)} files contain errors X")
        logger.debug(f"Error files: {error_files}")

    return total_files, generated_files, success_rate, error_files, duration
