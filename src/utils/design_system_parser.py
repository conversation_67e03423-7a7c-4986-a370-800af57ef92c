import json
import re
import ast
import colorsys

# Helper functions (parse_hsl_string, hsl_to_rgb_colorsys, rgb_to_hex,
# generate_color_name, process_color_entry, flatten_tailwind_colors)
# remain the same as in the original provided script.


# (Helper functions from the original prompt would be here)
# Helper functions for HSL to HEX conversion
def parse_hsl_string(hsl_str):
    """Parses an HSL string like "H S% L%" into normalized H, S, L values (0-1)."""
    parts = hsl_str.split()
    if len(parts) != 3:
        # Check if it's H S L (like in some CSS vars, e.g. "0 0% 0%")
        # or "H, S, L"
        if "," in hsl_str:
            parts = [p.strip() for p in hsl_str.split(",")]
        else:  # Try splitting by space again if it was a compact form
            parts = hsl_str.split()

    if len(parts) != 3:
        raise ValueError(
            f"HSL string '{hsl_str}' does not have 3 parts after parsing attempts."
        )

    h_str, s_str, l_str = parts[0], parts[1], parts[2]

    h = float(h_str) / 360.0
    s = float(s_str.replace("%", "")) / 100.0
    l = float(l_str.replace("%", "")) / 100.0

    h = max(0.0, min(1.0, h))
    s = max(0.0, min(1.0, s))
    l = max(0.0, min(1.0, l))
    return h, s, l


def hsl_to_rgb_colorsys(h_norm, s_norm, l_norm):
    """Converts normalized HSL (all 0-1) to RGB (0-255)."""
    r_norm, g_norm, b_norm = colorsys.hls_to_rgb(
        h_norm, l_norm, s_norm
    )  # colorsys expects H, L, S
    return round(r_norm * 255), round(g_norm * 255), round(b_norm * 255)


def rgb_to_hex(r, g, b):
    """Converts RGB (0-255) to HEX string."""
    return f"#{r:02x}{g:02x}{b:02x}"


def generate_color_name(name_parts):
    processed_parts = []
    for part_str in name_parts:
        if "-" not in part_str and re.match(
            r"[a-z]+[A-Z]", part_str
        ):  # camelCase like primaryText
            display_part = re.sub(r"(?<!^)(?=[A-Z])", " ", part_str).title()
        else:  # kebab-case or single word
            display_part = part_str.replace("-", " ").title()
        processed_parts.append(display_part)
    return " ".join(processed_parts)


def process_color_entry(name_parts, color_value_str, css_vars_map):
    color_id_parts = [part.lower() for part in name_parts]
    color_id = "-".join(color_id_parts) + "-color"

    color_name = generate_color_name(name_parts)
    hex_value = None

    if color_value_str.startswith("hsl(var("):
        var_name_match = re.search(r"var\((--[\w-]+)\)", color_value_str)
        if var_name_match:
            css_var_name = var_name_match.group(1)
            if css_var_name in css_vars_map:
                hsl_val_str = css_vars_map[css_var_name]
                try:
                    h, s, l = parse_hsl_string(hsl_val_str)
                    r, g, b = hsl_to_rgb_colorsys(h, s, l)
                    hex_value = rgb_to_hex(r, g, b)
                except Exception as e:
                    print(
                        f"Warning: Error converting HSL '{hsl_val_str}' for '{css_var_name}' (from {'-'.join(name_parts)}): {e}"
                    )
            else:
                print(
                    f"Warning: CSS variable {css_var_name} not found for color {'-'.join(name_parts)}"
                )
        else:
            print(f"Warning: Could not parse CSS variable from {color_value_str}")
    elif color_value_str.startswith("#"):
        if re.match(r"^#(?:[0-9a-fA-F]{3}){1,2}$", color_value_str):
            hex_value = color_value_str.lower()
        else:
            print(
                f"Warning: Invalid hex color format for {'-'.join(name_parts)}: {color_value_str}"
            )
    else:
        print(
            f"Warning: Unknown color format for {'-'.join(name_parts)}: {color_value_str}"
        )

    if hex_value:
        return {
            "id": color_id,
            "name": color_name,
            "value": hex_value,
            "category": "Colors",
            "editable": True,
        }
    return None


def flatten_tailwind_colors(config_colors_dict, css_vars_map):
    extracted_colors = []
    for key, value in config_colors_dict.items():
        if isinstance(value, str):
            color_data = process_color_entry([key], value, css_vars_map)
            if color_data:
                extracted_colors.append(color_data)
        elif isinstance(value, dict):
            for sub_key, sub_value in value.items():
                if not isinstance(sub_value, str):
                    # print(f"Info: Skipping non-string sub-value for {key}-{sub_key}: {sub_value}")
                    continue
                current_name_parts = [key]
                if sub_key.upper() != "DEFAULT":
                    current_name_parts.extend(sub_key.split("-"))

                color_data = process_color_entry(
                    current_name_parts, sub_value, css_vars_map
                )
                if color_data:
                    extracted_colors.append(color_data)
    return extracted_colors


def convert_tailwind_to_design_tokens(file_inputs):
    """
    Convert Tailwind config and CSS to design tokens.

    Args:
        file_inputs: Can be either:
            1. List of dicts with "fileName" and "content" keys (legacy format)
            2. Dict with file paths as keys and content as values (new format)
    """
    tailwind_config_str = None
    css_content_str = None

    # Handle both input formats
    if isinstance(file_inputs, dict):
        # New format: {"src/index.css": "content", "tailwind.config.ts": "content"}
        tailwind_config_str = file_inputs.get("tailwind.config.ts")
        css_content_str = file_inputs.get("src/index.css")
    elif isinstance(file_inputs, list):
        # Legacy format: [{"fileName": "...", "content": "..."}, ...]
        for file_input in file_inputs:
            if file_input["fileName"] == "tailwind.config.ts":
                tailwind_config_str = file_input["content"]
            elif file_input["fileName"] == "src/index.css":
                css_content_str = file_input["content"]
    else:
        raise ValueError("file_inputs must be either a dict or a list")

    if not tailwind_config_str or not css_content_str:
        raise ValueError("Missing tailwind.config.ts or src/index.css content.")

    tailwind_colors_config_dict = {}
    obj_str_match = re.search(
        r"export default\s*(\{[\s\S]*?\})\s*satisfies Config;",
        tailwind_config_str,
        re.DOTALL,
    )

    if obj_str_match:
        obj_str = obj_str_match.group(1)

        # 1. Replace `require(...)` calls with `None` (Python's None keyword string)
        obj_str = re.sub(r"require\s*\([^)]*\)", "None", obj_str)

        # 2. Handle JavaScript spread operator (...array) by replacing with empty list
        #    This is a simplification but works for most Tailwind configs
        obj_str = re.sub(r"\.\.\.[\w.]+", "[]", obj_str)

        # 3. Quote unquoted JavaScript object keys to make them Python string keys
        #    e.g., key: value -> "key": value
        #    This regex targets identifiers used as keys.
        #    It ensures keys like `darkMode`, `theme`, `center` are quoted.
        #    Already quoted keys (e.g., '2xl') are not affected.
        obj_str = re.sub(
            r"(?<=[{\s,\[])([a-zA-Z_][a-zA-Z0-9_]*)(?=\s*:)", r'"\1"', obj_str
        )

        # 4. Convert JavaScript boolean and null literals to Python equivalents
        #    (true -> True, false -> False, null -> None)
        #    Using \b for word boundaries. This assumes "true", "false", "null"
        #    do not appear as standalone words within string literals in this config.
        obj_str = re.sub(r"\btrue\b", "True", obj_str)
        obj_str = re.sub(r"\bfalse\b", "False", obj_str)
        obj_str = re.sub(r"\bnull\b", "None", obj_str)

        # --- For Debugging: Print the string before ast.literal_eval ---
        # print("--- Processed obj_str for ast.literal_eval ---")
        # print(obj_str)
        # print("---------------------------------------------")

        try:
            config_dict = ast.literal_eval(obj_str)
            tailwind_colors_config_dict = (
                config_dict.get("theme", {}).get("extend", {}).get("colors", {})
            )
        except Exception as e:
            # Provide more context on parsing failure
            error_message = (
                f"Failed to parse tailwind.config.ts after modifications: {e}\n"
            )
            # Attempt to find the problematic part if it's a SyntaxError with lineno/offset
            if hasattr(e, "lineno") and hasattr(e, "offset") and hasattr(e, "text"):
                lines = obj_str.splitlines()
                if 0 < e.lineno <= len(lines):
                    error_line = lines[e.lineno - 1]
                    error_message += f"Error near line {e.lineno}, offset {e.offset}:\n"
                    error_message += error_line + "\n"
                    error_message += (
                        " " * (e.offset - 1) + "^-- HERE\n"
                        if e.offset > 0
                        else "^-- HERE (offset 0)\n"
                    )
            # print(f"Problematic string for ast.literal_eval:\n{obj_str}") # Uncomment for full string debug
            raise ValueError(error_message) from e

    else:
        raise ValueError(
            "Could not find main configuration object in tailwind.config.ts (expected 'export default { ... } satisfies Config;')."
        )

    css_vars_map = {}
    root_match = re.search(r":root\s*\{([\s\S]*?)\}", css_content_str, re.DOTALL)
    if root_match:
        root_block_content = root_match.group(1)
        var_matches = re.findall(r"(--[\w-]+)\s*:\s*([^;]+);", root_block_content)
        for name, value in var_matches:
            css_vars_map[name.strip()] = value.strip()
        print(f"Parser | Found {len(css_vars_map)} CSS variables")
    else:
        print("Parser | Warning: :root block not found")

    extracted_colors = flatten_tailwind_colors(
        tailwind_colors_config_dict, css_vars_map
    )
    print(f"Parser | Processed {len(tailwind_colors_config_dict)} color configs")

    output_json_structure = {"design_tokens": {"colors": extracted_colors}}
    return output_json_structure


def parse_design_system_from_json(json_data):
    """
    Parse design system from JSON data in the format:
    {
        "src/index.css": "css content...",
        "tailwind.config.ts": "config content..."
    }

    Returns design tokens with extracted colors.
    """
    if isinstance(json_data, str):
        try:
            json_data = json.loads(json_data)
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON string: {e}")

    if not isinstance(json_data, dict):
        raise ValueError("Input must be a JSON object/dict")

    return convert_tailwind_to_design_tokens(json_data)


def extract_colors_from_design_system(json_data):
    """
    Extract just the colors array from design system JSON data.

    Args:
        json_data: Dict or JSON string with "src/index.css" and "tailwind.config.ts" keys

    Returns:
        List of color objects with id, name, value, category, editable fields
    """
    design_tokens = parse_design_system_from_json(json_data)
    return design_tokens.get("design_tokens", {}).get("colors", [])

# Example usage with the provided input data:
# if __name__ == '__main__':
#     input_data = [
#         {
#             "fileName": "tailwind.config.ts",
#             "content": """import type { Config } from "tailwindcss";

# export default {
# 	darkMode: ["class"],
# 	content: [
# 		"./pages/**/*.{ts,tsx}",
# 		"./components/**/*.{ts,tsx}",
# 		"./app/**/*.{ts,tsx}",
# 		"./src/**/*.{ts,tsx}",
# 	],
# 	prefix: "",
# 	theme: {
# 		container: {
# 			center: true,
# 			padding: '2rem',
# 			screens: {
# 				'2xl': '1400px'
# 			}
# 		},
# 		extend: {
# 			colors: {
# 				border: 'hsl(var(--border))',
# 				input: 'hsl(var(--input))',
# 				ring: 'hsl(var(--ring))',
# 				background: 'hsl(var(--background))',
# 				foreground: 'hsl(var(--foreground))',
# 				primary: {
# 					DEFAULT: 'hsl(var(--primary))',
# 					foreground: 'hsl(var(--primary-foreground))'
# 				},
# 				secondary: {
# 					DEFAULT: 'hsl(var(--secondary))',
# 					foreground: 'hsl(var(--secondary-foreground))'
# 				},
# 				destructive: {
# 					DEFAULT: 'hsl(var(--destructive))',
# 					foreground: 'hsl(var(--destructive-foreground))'
# 				},
# 				muted: {
# 					DEFAULT: 'hsl(var(--muted))',
# 					foreground: 'hsl(var(--muted-foreground))'
# 				},
# 				accent: {
# 					DEFAULT: 'hsl(var(--accent))',
# 					foreground: 'hsl(var(--accent-foreground))'
# 				},
# 				popover: {
# 					DEFAULT: 'hsl(var(--popover))',
# 					foreground: 'hsl(var(--popover-foreground))'
# 				},
# 				card: {
# 					DEFAULT: 'hsl(var(--card))',
# 					foreground: 'hsl(var(--card-foreground))'
# 				},
# 				sidebar: {
# 					DEFAULT: 'hsl(var(--sidebar-background))',
# 					foreground: 'hsl(var(--sidebar-foreground))',
# 					primary: 'hsl(var(--sidebar-primary))',
# 					'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
# 					accent: 'hsl(var(--sidebar-accent))',
# 					'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
# 					border: 'hsl(var(--sidebar-border))',
# 					ring: 'hsl(var(--sidebar-ring))'
# 				},
# 				primaryText: '#FFFFFF',
# 				secondaryText: '#B3B3B3',
# 				surface: '#1E1E1E'
# 			},
# 			fontFamily: {
# 				roboto: ['Roboto', 'sans-serif']
# 			},
# 			borderRadius: {
# 				lg: 'var(--radius)',
# 				md: 'calc(var(--radius) - 2px)',
# 				sm: 'calc(var(--radius) - 4px)'
# 			},
# 			keyframes: {
# 				'accordion-down': {
# 					from: {
# 						height: '0'
# 					},
# 					to: {
# 						height: 'var(--radix-accordion-content-height)'
# 					}
# 				},
# 				'accordion-up': {
# 					from: {
# 						height: 'var(--radix-accordion-content-height)'
# 					},
# 					to: {
# 						height: '0'
# 					}
# 				}
# 			},
# 			animation: {
# 				'accordion-down': 'accordion-down 0.2s ease-out',
# 				'accordion-up': 'accordion-up 0.2s ease-out'
# 			}
# 		}
# 	},
# 	plugins: [require("tailwindcss-animate")],
# } satisfies Config;"""
#         },
#         {
#             "fileName": "src/index.css",
#             "content": """@tailwind base;
# @tailwind components;
# @tailwind utilities;

# @layer base {
#   :root {
#     --background: 0 0% 0%;
#     --foreground: 0 0% 100%;

#     --card: 0 0% 12%;
#     --card-foreground: 0 0% 100%;

#     --popover: 0 0% 12%;
#     --popover-foreground: 0 0% 100%;

#     --primary: 0 0% 100%;
#     --primary-foreground: 0 0% 0%;

#     --secondary: 0 0% 12%;
#     --secondary-foreground: 0 0% 70%;

#     --muted: 0 0% 12%;
#     --muted-foreground: 0 0% 70%;

#     --accent: 0 0% 89%;
#     --accent-foreground: 0 0% 12%;

#     --destructive: 0 84.2% 60.2%;
#     --destructive-foreground: 0 0% 100%;

#     --border: 0 0% 12%;
#     --input: 0 0% 12%;
#     --ring: 0 0% 89%;

#     --radius: 0.375rem;

#     --sidebar-background: 0 0% 12%;
#     --sidebar-foreground: 0 0% 100%;
#     --sidebar-primary: 0 0% 100%;
#     --sidebar-primary-foreground: 0 0% 0%;
#     --sidebar-accent: 0 0% 89%;
#     --sidebar-accent-foreground: 0 0% 12%;
#     --sidebar-border: 0 0% 12%;
#     --sidebar-ring: 0 0% 89%;
#   }
# }

# @layer base {
#   * {
#     @apply border-border;
#   }

#   body {
#     @apply bg-background text-foreground font-roboto;
#   }

#   @font-face {
#     font-family: 'Roboto';
#     font-style: normal;
#     font-weight: 400;
#     src: url('https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap');
#   }
# }"""
#         }
#     ]

#     design_tokens_dict = convert_tailwind_to_design_tokens(input_data)
#     print(json.dumps(design_tokens_dict, indent=2))
