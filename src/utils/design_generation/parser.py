import logging
from src.model.response_schemas import DAResponse
import re
from bs4 import BeautifulSoup


def get_content_inside_markdown(data: str, markdown: str):
    start_tag = "```" + markdown
    end_tag = "```"

    start_index = -1
    start_index = data.find(start_tag)

    if start_index == -1:
        return data

    end_index = data.find(end_tag, start_index + len(start_tag))
    if end_index == -1:
        return -1
        raise ValueError("No valid end tag found")

    output_code = data[start_index + len(start_tag) : end_index].strip()
    return output_code


def validate_response(response) -> DAResponse:
    """Validate the API response."""
    try:
        return DAResponse.model_validate(response)
    except Exception:
        logging.error("Error validating the Response from DA", exc_info=True)
        return None


def extract_html_content(html: str) -> str:
    match = re.search(r"<!DOCTYPE html>[\s\S]*</html>", html, re.IGNORECASE)
    return match.group(0) if match else ""


def update_html_sections(base_html, updated_sections, edit=True):
    """
    Updates sections in the base HTML with new content, replacing the entire parent div.

    :param base_html: String containing the base HTML.
    :param updated_sections: Dict with keys as section ids and values as the new HTML content for those sections.
    :return: Updated HTML as a string.
    """
    # Parse the base HTML
    soup = BeautifulSoup(base_html, "html.parser")

    # Iterate through the updated sections
    for identifier, new_content in updated_sections.items():
        # Try to find the section by id first
        section = soup.find(id=identifier)

        # If not found by id, try to find it by class name
        if not section:
            section = soup.find(class_=identifier)

        if section:
            # Get the parent div of the section
            parent_div = section.find_parent("div")

            if parent_div:
                # Replace the entire parent div with new content
                new_content_soup = BeautifulSoup(new_content, "html.parser")
                if edit:
                    parent_div.clear()
                parent_div.append(new_content_soup)
            else:
                print(f"Warning: Section '{identifier}' has no parent div.")
        else:
            print(
                f"Warning: Section with id or class '{identifier}' not found in the base HTML."
            )

    # Return the modified HTML
    return str(soup)
