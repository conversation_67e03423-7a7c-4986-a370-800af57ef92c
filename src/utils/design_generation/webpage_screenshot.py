from selenium import webdriver
from selenium.webdriver.edge.service import Service as EdgeService
from selenium.webdriver.edge.options import Options as EdgeOptions
import time
import base64
import os


class WebpageScreenshot:
    def __init__(self, driver_path="./app/assets/msedgedriver"):
        self.driver_path = driver_path
        self.driver = None
        self.html_path = "/tmp/wireframe.html"
        if not os.path.exists(self.html_path):
            # If not, create the file and write the HTML content to it
            os.makedirs(os.path.dirname(self.html_path), exist_ok=True)

    def initialize_driver(self):
        options = EdgeOptions()

        # Set browser to full width
        options.add_argument("--start-maximized")

        # Set browser to run in headless mode
        options.add_argument("--headless")
        options.add_argument("no-sandbox")
        options.add_argument("disable-dev-shm-usage")
        service = EdgeService(executable_path=self.driver_path)
        self.driver = webdriver.Edge(service=service, options=options)

    def open_html_string(self):
        if self.driver is None:
            raise Exception("Driver not initialized. Call 'initialize_driver' first.")
        # self.driver.get("data:text/html;charset=utf-8," + html_content)
        # print("Here is the image uri: ", 'file://'+ os.path.abspath(self.html_path))
        self.driver.get("file://" + os.path.abspath(self.html_path))

    def take_screenshot(self):
        if self.driver is None:
            raise Exception("Driver not initialized. Call 'initialize_driver' first.")
        self.set_viewport_size()
        time.sleep(15)  # Wait for the page to load
        png_data = self.driver.get_screenshot_as_png()
        screenshot_base64 = base64.b64encode(png_data).decode("utf-8")
        return screenshot_base64

    def take_screenshots(self):
        if self.driver is None:
            raise Exception("Driver not initialized. Call 'initialize_driver' first.")
        time.sleep(2)  # Wait for the page to load
        png_data = self.driver.get_screenshot_as_png()
        screenshot_base64 = base64.b64encode(png_data).decode("utf-8")
        return screenshot_base64

    def close_browser(self):
        if self.driver is not None:
            self.driver.quit()

    def set_viewport_size(self):
        # self.driver.set_window_size(1920, 1080)
        size = {
            "width": 1300,
            "height": self.driver.execute_script(
                "return document.documentElement.scrollHeight;"
            ),
        }

        print("width:", size["width"])
        print("height:", size["height"])
        view_width = self.driver.execute_script(
            "return document.documentElement.clientWidth;"
        )
        view_height = self.driver.execute_script(
            "return document.documentElement.clientHeight;"
        )
        window_size = self.driver.get_window_rect()
        offset = (
            window_size["width"] - view_width,
            window_size["height"] - view_height,
        )
        old_diff = (0, 0)
        retry_count = 0
        offset_reset_flag = True
        while True:
            self.driver.set_window_size(
                size["width"] + offset[0], size["height"] + offset[1]
            )
            cur_view_width = self.driver.execute_script(
                "return document.documentElement.clientWidth;"
            )
            cur_view_height = self.driver.execute_script(
                "return document.documentElement.clientHeight;"
            )
            width_diff = size["width"] - cur_view_width
            height_diff = size["height"] - cur_view_height
            offset = (offset[0] + width_diff, offset[1] + height_diff)
            new_diff = (abs(width_diff), abs(height_diff))
            if size["width"] == cur_view_width and size["height"] == cur_view_height:
                break
            if retry_count < 5:
                if old_diff == new_diff:
                    if offset_reset_flag:
                        if new_diff[0] != 0:
                            offset = (0, offset[1])
                            offset_reset_flag = False
                        if new_diff[1] != 0:
                            offset = (offset[0], 0)
                            offset_reset_flag = False
                    retry_count += 1
                else:
                    retry_count = 0
            else:
                break
            old_diff = new_diff

    def get_web_screenshot(self, html_content):
        with open(self.html_path, "w") as file:
            file.write(html_content)
        self.initialize_driver()
        self.open_html_string()
        image = self.take_screenshot()
        self.close_browser()
        return image

    def set_viewport_sizes(self, width, height):
        self.driver.set_window_size(width, height)
        # Ensure the page fits in the viewport
        self.driver.execute_script("document.body.style.overflow = 'hidden';")
        time.sleep(2)  # Allow time for resizing

    def get_web_screenshots(self, html_content):
        with open(self.html_path, "w") as file:
            file.write(html_content)

        self.initialize_driver()
        self.open_html_string()

        viewports = {
            "mobile": (375, 667),
            "tablet": (768, 1024),
            "monitor": (1920, 1080),
        }

        screenshots = {}

        for device, (width, height) in viewports.items():
            self.set_viewport_sizes(width, height)
            screenshots[device] = self.take_screenshots()

        self.close_browser()
        return screenshots
