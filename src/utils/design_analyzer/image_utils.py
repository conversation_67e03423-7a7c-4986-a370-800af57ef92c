import base64
import cv2
import numpy as np
from io import BytesIO
from PIL import Image
import logging


def encodeBase64(image_path):
    with open(image_path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode("utf-8")


def overlay_grid(data_uri, grid_size):
    # Decode Data URI to image
    encoded_data = data_uri.split(",")[1]
    nparr = np.frombuffer(base64.b64decode(encoded_data), np.uint8)
    image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)  # Convert from BGR to RGB

    # Get image dimensions
    height, width, _ = image.shape

    # Create a copy of the image to draw on
    grid_image = image_rgb.copy()

    # Draw vertical grid lines
    for x in range(0, width, grid_size):
        cv2.line(grid_image, (x, 0), (x, height), (255, 0, 0), 1)  # Red grid lines

    # Draw horizontal grid lines
    for y in range(0, height, grid_size):
        cv2.line(grid_image, (0, y), (width, y), (255, 0, 0), 1)  # Red grid lines

    # Convert the result to PIL Image for Data URI encoding
    grid_image_pil = Image.fromarray(grid_image)

    # Encode image as Data URI
    buffered = BytesIO()
    grid_image_pil.save(buffered, format="PNG")
    encoded_result = base64.b64encode(buffered.getvalue()).decode("utf-8")
    data_uri_result = f"data:image/png;base64,{encoded_result}"

    return data_uri_result


def image_to_data_uri(image: Image.Image) -> str:
    """
    Converts an image to a Data URI with Base64 encoding.

    Args:
        image (PIL.Image.Image): The image object.

    Returns:
        str: The Data URI string.
    """
    logging.info("---- Task: Encoder called")
    buffered = BytesIO()
    image.save(buffered, format="PNG")
    base64_encoded = base64.b64encode(buffered.getvalue()).decode("utf-8")
    logging.info("---- Status: Image encoded")
    return f"data:image/png;base64,{base64_encoded}"


def data_uri_to_image(data_uri: str) -> Image.Image:
    """
    Converts a Data URI back into an image.

    Args:
        data_uri (str): The Data URI string.

    Returns:
        Image.Image: The PIL Image object.
    """
    logging.info("---- Task: Decoder called")
    try:
        encoded_data = data_uri.split(",", 1)[1]
        image_data = base64.b64decode(encoded_data)
        image = Image.open(BytesIO(image_data))
        logging.info("---- Status: Image decoded successfully")
        return image
    except Exception as e:
        logging.error(f"---- Error: Failed to decode data URI - {e}", exc_info=True)
        raise ValueError("Invalid data URI")


# Example usage
# overlay_grid('path_to_your_image.jpg', grid_size=50)
