import json
import re
from typing import Dict, List, Any, Union

from src.utils.logger import AppLogger

logger = AppLogger("Code Regeneration:PP").get_logger()


def normalize_file_path(file_path: str) -> str:
    """
    Normalize a file path for consistent comparison.

    Args:
        file_path: The file path to normalize

    Returns:
        Normalized file path
    """
    # Convert backslashes to forward slashes
    normalized = file_path.replace("\\", "/")

    # Remove leading ./ or / if present
    if normalized.startswith("./"):
        normalized = normalized[2:]
    elif normalized.startswith("/"):
        normalized = normalized[1:]

    return normalized.lower()  # Convert to lowercase for case-insensitive comparison


def merge_code_lists(
    original_code: List[Dict[str, str]], new_code: List[Dict[str, str]]
) -> List[Dict[str, str]]:
    """
    Merges the original code list with the new code list, prioritizing new code files.

    Matches keys between original and new code (using normalized file paths),
    updating files that exist in both, adding new files that only exist in the
    new code, and preserving files that only exist in the original code.

    Args:
        original_code: The original list of code files from the user's request
        new_code: The new list of code files extracted from the API response

    Returns:
        A merged list of code files with new files taking precedence.
        If new_code is empty or merge fails, returns original_code.
    """
    try:
        logger.info(
            f"✓ Merging code lists: original={len(original_code)} files, new={len(new_code)} files"
        )

        # # Log original file names for debugging
        # if original_code:
        #     original_file_names = [file.get("fileName", "unnamed") for file in original_code if isinstance(file, dict) and "fileName" in file]
        #     logger.debug(f"Original files: {', '.join(original_file_names[:5])}{'...' if len(original_file_names) > 5 else ''}")

        # # Log new file names for debugging
        # if new_code:
        #     new_file_names = [file.get("fileName", "unnamed") for file in new_code if isinstance(file, dict) and "fileName" in file]
        #     logger.debug(f"New files: {', '.join(new_file_names[:5])}{'...' if len(new_file_names) > 5 else ''}")

        # If new_code is empty, return the original code
        if not new_code:
            logger.info("✓ New code list is empty, returning original code unchanged.")
            return original_code

        # Create a map of existing files for quick lookup, using normalized paths
        existing_files = {}
        for file in original_code:
            # Ensure file has both fileName and content and fileName is not empty
            if (
                isinstance(file, dict)
                and "fileName" in file
                and file["fileName"]
                and "content" in file
            ):
                # Ensure content is a string
                if isinstance(file["content"], str):
                    normalized_path = normalize_file_path(file["fileName"])
                    existing_files[normalized_path] = file

        merged_code_map = (
            {}
        )  # Use a map during merging to handle updates/additions easily

        # Add/Update files from the new code list
        updated_files_count = 0
        new_files_count = 0

        for file in new_code:
            # Ensure file has both fileName and content and fileName is not empty
            if (
                isinstance(file, dict)
                and "fileName" in file
                and file["fileName"]
                and "content" in file
            ):
                # Ensure content is a string
                if isinstance(file["content"], str):
                    file_name = file["fileName"]
                    normalized_path = normalize_file_path(file_name)

                    # If file exists in original, count as updated, otherwise new
                    if (
                        normalized_path in existing_files
                        and normalized_path not in merged_code_map
                    ):  # Check merged_code_map to avoid double counting if new_code has duplicates
                        # Check if content actually changed before logging as updated
                        if file.get("content", "") != existing_files[
                            normalized_path
                        ].get("content", ""):
                            updated_files_count += 1
                            logger.debug(f"Updated file: {file_name}")
                        else:
                            # Content is same, just add it to the map (new takes precedence if duplicates)
                            logger.debug(
                                f"File content unchanged, but adding from new_code: {file_name}"
                            )
                        merged_code_map[normalized_path] = (
                            file  # Add new file (overwrites original in map)
                        )

                    elif (
                        normalized_path not in merged_code_map
                    ):  # Ensure it's genuinely new and not already processed from new_code duplicates
                        new_files_count += 1
                        logger.debug(f"Added new file: {file_name}")
                        merged_code_map[normalized_path] = file

        # Add files from the original code list that were not in the new code list
        unchanged_files_count = 0
        for file in original_code:
            if isinstance(file, dict) and "fileName" in file and file["fileName"]:
                normalized_path = normalize_file_path(file["fileName"])
                if normalized_path not in merged_code_map:
                    unchanged_files_count += 1
                    logger.debug(
                        f"Keeping original file not present in new code: {file['fileName']}"
                    )
                    merged_code_map[normalized_path] = file  # Add original file

        # Convert the map back to a list
        merged_code = list(merged_code_map.values())

        # Log summary statistics
        logger.info(f"✓ Code regeneration merge summary:")
        logger.info(f"  - Total files after merge: {len(merged_code)}")
        logger.info(
            f"  - Files from new code (added/updated): {new_files_count + updated_files_count}"
        )
        logger.info(f"    - New files added: {new_files_count}")
        logger.info(f"    - Files updated (content changed): {updated_files_count}")
        logger.info(
            f"  - Original files kept (not in new code): {unchanged_files_count}"
        )

        return merged_code

    except Exception as e:
        # Log the error and return the original code as fallback
        logger.error(f"Error merging code lists: {str(e)}", exc_info=True)
        logger.warning("Fallback: returning original code due to merge failure.")
        return original_code  # Always return original code on merge failure


def extract_mlo_code_blocks(text_content: str) -> List[Dict[str, str]]:
    """
    Extracts code blocks enclosed in <mlo-write file_path="..."> tags from a string.

    Args:
        text_content: The input string potentially containing MLO code tags.

    Returns:
        List of dictionaries with "fileName" and "content" for each extracted code block.
    """
    code_blocks = []
    # Pattern to find <mlo-write> blocks and capture file path and content
    # Use a non-greedy match for content (.*?) and DOTALL to match across lines
    write_pattern = re.compile(
        r'<mlo-write\s+file_path="([^"]+)">(.*?)</mlo-write>', re.DOTALL
    )

    if "<mlo-write" not in text_content:
        logger.debug("No <mlo-write> tags found in text content")
        return code_blocks

    matches = list(write_pattern.finditer(text_content))

    for i, match in enumerate(matches):
        file_path = match.group(1).strip()  # Extract file path
        content = match.group(2).strip()  # Extract content

        if file_path:  # Ensure file path is not empty
            code_blocks.append({"fileName": file_path, "content": content})

    return code_blocks


def extract_code_from_api_response(
    api_response: Union[Dict[str, Any], List, str, None],
) -> List[Dict[str, str]]:
    """
    Args:
        api_response: The API response containing the regenerated code.Can be a dictionary, a list, a string containing MLO tags/JSON, or None.

    Returns:
        List of dictionaries with fileName and content. Returns an empty list if no recognizable code structure is found.
    """
    try:
        logger.debug(
            f"✓ Attempting to extract code from API response. Type: {type(api_response)}"
        )

        # Handle None explicitly
        if api_response is None:
            return []

        # --- Case 1: Response is a string ---
        if isinstance(api_response, str):
            mlo_code = extract_mlo_code_blocks(api_response)
            if mlo_code:
                logger.info(
                    f"✓ Extracted {len(mlo_code)} code blocks from string using MLO tags."
                )
                return mlo_code

            # If no MLO tags, try parsing the string as JSON
            try:
                parsed_json = json.loads(api_response)
                logger.debug("Successfully parsed string as JSON.")
                # Recursively call the function with the parsed JSON
                return extract_code_from_api_response(parsed_json)
            except json.JSONDecodeError:
                logger.debug("String is not valid JSON.")
                # If it's a string, no MLO tags, and not JSON, there's no code list to extract
                logger.warning(
                    "✗ String response contains no MLO tags and is not JSON."
                )
                return []

        # --- Case 2: Response is a dictionary ---
        if isinstance(api_response, dict):
            # Check common keys for code lists (including 'codebase' which seems to be the issue)
            for key in [
                "code",
                "code_blocks",
                "files",
                "codebase",
                "generated_code",
                "output",
                "result",
            ]:
                if key in api_response:
                    if isinstance(api_response[key], list):
                        potential_code_list = api_response[key]

                        # Check if the list looks like a code list ([{"fileName": "...", "content": "..."}, ...])
                        if all(
                            isinstance(item, dict)
                            and any(k in item for k in ["fileName", "name", "path"])
                            and "content" in item
                            for item in potential_code_list
                        ):
                            # Normalize keys to 'fileName' if needed
                            normalized_list = []
                            for item in potential_code_list:
                                file_name = (
                                    item.get("fileName")
                                    or item.get("name")
                                    or item.get("path")
                                )
                                if file_name and isinstance(item.get("content"), str):
                                    normalized_list.append(
                                        {
                                            "fileName": file_name,
                                            "content": item["content"],
                                        }
                                    )

                            if normalized_list:
                                return normalized_list

                    elif isinstance(api_response[key], dict):
                        # Recursively check nested dictionaries
                        nested_result = extract_code_from_api_response(
                            api_response[key]
                        )
                        if nested_result:
                            return nested_result

                    elif isinstance(api_response[key], str):
                        # First try to extract MLO code blocks directly
                        mlo_result = extract_mlo_code_blocks(api_response[key])
                        if mlo_result:
                            return mlo_result

                        # If no MLO tags, try recursive processing
                        string_result = extract_code_from_api_response(
                            api_response[key]
                        )
                        if string_result:
                            return string_result

            # Check for a text/content field that might contain MLO tags or JSON string
            for key in ["text", "content", "response", "result"]:
                if key in api_response and isinstance(api_response[key], str):
                    text_content = api_response[key]
                    # Recursively call the function with the string content of this field
                    return extract_code_from_api_response(text_content)

            return []

        # --- Case 3: Response is a list ---
        if isinstance(api_response, list):
            # Check if the list looks like a code list ([{"fileName": "...", "content": "..."}, ...])
            if all(
                isinstance(item, dict)
                and any(k in item for k in ["fileName", "name", "path"])
                and "content" in item
                for item in api_response
            ):
                # Normalize keys to 'fileName' if needed
                normalized_list = []
                for item in api_response:
                    file_name = (
                        item.get("fileName") or item.get("name") or item.get("path")
                    )
                    if file_name and isinstance(item.get("content"), str):
                        normalized_list.append(
                            {"fileName": file_name, "content": item["content"]}
                        )

                if normalized_list:
                    return normalized_list

            return []

        # --- Case 4: Unhandled type ---
        return []

    except Exception as e:
        logger.error(
            f"✗ Error during code extraction from API response: {str(e)}", exc_info=True
        )
        # Return empty list on extraction error
        return []
