import asyncio
import logging
import json  # <-- Make sure to import json
from collections import defaultdict
from typing import Dict, List

from redis.asyncio import Redis

logger = logging.getLogger(__name__)


class SSEManager:
    """Manages a single Redis stream listener and distributes messages to SSE clients."""

    def __init__(self, redis_client: Redis):
        self.redis_client = redis_client
        self.listeners: Dict[str, List[asyncio.Queue]] = defaultdict(list)
        self._listener_task: asyncio.Task = None
        self.stream_name = "project_status_stream"

    async def _redis_listener(self):
        stream_ids = {self.stream_name: "0-0"}
        logger.info(
            f"CONSUMER: Starting Redis listener for stream: '{self.stream_name}'"
        )  # Added quotes for clarity
        while True:
            try:
                # --- ADD THIS LOGGING ---
                logger.debug(
                    "CONSUMER: Waiting for messages on Redis stream (xread)..."
                )
                messages = await self.redis_client.xread(
                    {self.stream_name: stream_ids[self.stream_name]},
                    block=5000,
                    count=10,
                )

                if not messages:
                    # This is normal, it just means no new messages in the last 5s
                    continue

                # --- ADD THIS LOGGING ---
                logger.info(
                    f"CONSUMER: Received {len(messages[0][1])} new message(s) from Redis."
                )

                for stream_key, stream_messages in messages:
                    for message_id, data in stream_messages:
                        logger.debug(
                            f"CONSUMER: Processing message {message_id} with raw data: {data}"
                        )
                        try:
                            # The Redis client is decoding responses, so we check for the string key 'data'.
                            if "data" not in data:
                                logger.warning(
                                    f"CONSUMER: Skipping message {message_id} without 'data' field. Found fields: {list(data.keys())}"
                                )
                                continue

                            # The value is already a string, so no .decode() is needed.
                            json_payload = data["data"]
                            payload_dict = json.loads(json_payload)

                            # Add the Redis message ID as a fallback if no custom ID is present
                            if "id" not in payload_dict:
                                payload_dict["id"] = message_id

                            request_id = payload_dict.get("request_id")

                            # --- CRITICAL DIAGNOSTIC LOGGING ---
                            if request_id and (request_id in self.listeners):
                                logger.info(
                                    f"CONSUMER: Match found! Routing message for request_id '{request_id}' to {len(self.listeners[request_id])} listener(s)."
                                )
                                for queue in self.listeners[request_id]:
                                    await queue.put(payload_dict)
                            else:
                                # This block tells you if you received a message but couldn't find a listener for it
                                logger.warning(
                                    f"CONSUMER: No active listener found for request_id '{request_id}'. Listeners exist for keys: {list(self.listeners.keys())}"
                                )
                            # --- END OF CRITICAL LOGGING ---

                        except Exception as e:
                            logger.error(
                                f"CONSUMER: Failed to process message {message_id}: {e}"
                            )

                        stream_ids[stream_key] = message_id

            except asyncio.CancelledError:
                logger.info("CONSUMER: Redis listener task cancelled.")
                break
            except Exception:
                logger.exception("CONSUMER: Error in Redis listener task. Retrying...")
                await asyncio.sleep(5)

    def start_listener_task(self):
        """Starts the background listener task if it's not already running."""
        if self._listener_task is None or self._listener_task.done():
            self._listener_task = asyncio.create_task(self._redis_listener())
            logger.info("CONSUMER: Redis listener background task created.")
        else:
            logger.info("CONSUMER: Redis listener task already running.")

        # Check if the task is healthy
        if self._listener_task and not self._listener_task.done():
            logger.info("CONSUMER: Redis listener task is active.")
        elif self._listener_task and self._listener_task.done():
            try:
                # This will raise the exception if the task failed
                self._listener_task.result()
                logger.warning(
                    "CONSUMER: Redis listener task completed unexpectedly without error."
                )
            except Exception as e:
                logger.error(f"CONSUMER: Redis listener task failed with error: {e}")
                # Restart the task
                self._listener_task = asyncio.create_task(self._redis_listener())
                logger.info("CONSUMER: Restarted failed Redis listener task.")

    async def stop_listener_task(self):
        """Stops the background listener task."""
        if self._listener_task and not self._listener_task.done():
            self._listener_task.cancel()
            try:
                await self._listener_task
            except asyncio.CancelledError:
                pass  # Expected on cancellation

    async def add_listener(self, request_id: str) -> asyncio.Queue:
        queue = asyncio.Queue()
        self.listeners[request_id].append(queue)
        # It's good practice to start the listener when the first client connects
        self.start_listener_task()
        logger.info(
            f"CONSUMER: Added new listener for request_id '{request_id}'. Total listeners for this ID: {len(self.listeners[request_id])}"
        )
        logger.info(
            f"CONSUMER: Current active request_ids: {list(self.listeners.keys())}"
        )
        return queue

    def remove_listener(self, request_id: str, queue: asyncio.Queue):
        if request_id in self.listeners:
            try:
                self.listeners[request_id].remove(queue)
                if not self.listeners[request_id]:
                    del self.listeners[request_id]
            except ValueError:
                pass
