-- src/services/database/schema.sql

CREATE EXTENSION IF NOT EXISTS "pgcrypto";

CREATE TABLE IF NOT EXISTS users (
    user_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    username TEXT NOT NULL,
    email TEXT UNIQUE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);
-- Create projects table
CREATE TABLE IF NOT EXISTS projects (
    project_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_name TEXT NOT NULL,
    project_description TEXT,
    project_type TEXT CHECK (project_type IN ('UI', 'APP', 'DESIGN_ANALYSIS')),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    last_modified TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES users(user_id) ON DELETE CASCADE,
    project_state TEXT CHECK (project_state IN ('ACTIVE', 'ARCHIVED', 'DELETED'))
);

-- Create project_images table
CREATE TABLE IF NOT EXISTS project_images (
    image_id SERIAL PRIMARY KEY,
    project_id UUID REFERENCES projects(project_id) ON DELETE CASCADE,
    image_url TEXT NOT NULL CHECK (image_url ~* '^https?://'),
    uploaded_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create project_conversations table
CREATE TABLE IF NOT EXISTS project_conversations (
    conversation_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID REFERENCES projects(project_id) ON DELETE CASCADE,
    user_query TEXT NOT NULL,
    ai_response TEXT NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create project_status table
CREATE TABLE IF NOT EXISTS project_status (
    status_id UUID PRIMARY KEY,
    project_id UUID REFERENCES projects(project_id) ON DELETE CASCADE,
    progress TEXT NOT NULL CONSTRAINT project_status_progress_check CHECK (
        progress IN (
            'OVERVIEW',
            'FILE_QUEUE',
            'SEED_PROJECT_INITIALIZED',
            'LAYOUT_ANALYZED',
            'DESIGN_SYSTEM_MAPPED',
            'COMPONENTS_CREATED',
            'PAGES_GENERATED',
            'BUILD_STARTED',
            'BUILD_SUCCEEDED',
            'BUILD_FAILED',
            'COMPLETED',
            'FAILED',
            'CODE_GENERATION'
        )
    ),
    status TEXT NOT NULL CONSTRAINT project_status_status_check CHECK (
        status IN (
            'PENDING',
            'IN_PROGRESS',
            'COMPLETED',
            'FAILED'
        )
    ),
    log TEXT,
    progress_description log TEXT,
    progress_description TEXT, -- New column for storing human-readable progress info
    history JSONB, -- New column for storing a list of JSON objects
    metadata JSONB,
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create code_versions table
CREATE TABLE IF NOT EXISTS code_versions (
    version_id SERIAL PRIMARY KEY,
    project_id UUID REFERENCES projects(project_id) ON DELETE CASCADE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    is_full_snapshot BOOLEAN DEFAULT FALSE,
    code_snapshot JSONB
);

-- Create code_diffs table
CREATE TABLE IF NOT EXISTS code_diffs (
    diff_id SERIAL PRIMARY KEY,
    version_id INTEGER REFERENCES code_versions(version_id) ON DELETE CASCADE,
    file_path TEXT NOT NULL,
    diff_data JSONB NOT NULL
);

-- Create indexes for performance
CREATE INDEX idx_project_created_by ON projects (created_by);
CREATE INDEX idx_conversation_project ON project_conversations (project_id);
CREATE INDEX idx_version_project ON code_versions (project_id);
