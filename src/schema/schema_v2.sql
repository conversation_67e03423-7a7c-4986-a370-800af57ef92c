-- Create custom ENUM types for consistency and data integrity
CREATE TYPE migration_schema.project_state AS ENUM ('ACTIVE', 'ARCHIVED', 'DELETED');
CREATE TYPE migration_schema.generation_type AS ENUM ('wireframe_generation', 'app_generation', 'design_analysis');
CREATE TYPE migration_schema.device_platform AS ENUM ('web', 'mobile', 'tablet');
CREATE TYPE migration_schema.js_framework AS ENUM ('react', 'angular', 'vue', 'svelte', 'nextjs', 'other');
CREATE TYPE migration_schema.design_system AS ENUM ('tailwind', 'material_ui', 'bootstrap', 'ant_design', 'custom');
CREATE TYPE migration_schema.vcs_provider AS ENUM ('github', 'azure_devops', 'gitlab');
CREATE TYPE migration_schema.deployment_provider AS ENUM ('netlify', 'vercel', 'azure_static_web_apps', 'other');
CREATE TYPE migration_schema.message_type AS ENUM ('user', 'assistant', 'capsule');

-- A simple table for users, which you likely already have.
CREATE TABLE IF NOT EXISTS migration_schema.users (
    user_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email TEXT NOT NULL UNIQUE,
    full_name TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Your projects table, slightly enhanced with our ENUM types.
CREATE TABLE IF NOT EXISTS migration_schema.projects (
    project_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_name TEXT NOT NULL,
    project_description TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    last_modified TIMESTAMPTZ DEFAULT NOW(),
    created_by UUID NOT NULL REFERENCES migration_schema.users(user_id) ON DELETE CASCADE,
    project_state migration_schema.project_state DEFAULT 'ACTIVE'
);

-- NEW TABLE: Stores all generation settings for a project (One-to-One relationship)
CREATE TABLE IF NOT EXISTS migration_schema.project_settings (
    project_id UUID PRIMARY KEY, -- Establishes a 1-to-1 relationship
    device migration_schema.device_platform NOT NULL,
    framework migration_schema.js_framework NOT NULL,
    design_system migration_schema.design_system NOT NULL,
    generation_type migration_schema.generation_type NOT NULL,
    CONSTRAINT fk_project
        FOREIGN KEY(project_id)
        REFERENCES migration_schema.projects(project_id)
        ON DELETE CASCADE
);

-- NEW TABLE: Stores all repository and deployment info for a project (One-to-One relationship)
CREATE TABLE IF NOT EXISTS migration_schema.repository_details (
    project_id UUID PRIMARY KEY, -- Establishes a 1-to-1 relationship
    vcs_provider migration_schema.vcs_provider NOT NULL,
    clone_url TEXT NOT NULL,
    deployment_provider migration_schema.deployment_provider NOT NULL,
    deployed_url TEXT, -- Can be nullable initially
    CONSTRAINT fk_project
        FOREIGN KEY(project_id)
        REFERENCES migration_schema.projects(project_id)
        ON DELETE CASCADE
);

-- NEW TABLE: Represents the single conversation attached to a project.
-- Its main purpose is to hold the initial metadata for the UI stepper.
CREATE TABLE IF NOT EXISTS migration_schema.conversations (
    conversation_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    project_id UUID NOT NULL UNIQUE, -- Ensures a project has only one conversation
    initial_ui_metadata JSONB NOT NULL, -- For the initial stepper states
    created_at TIMESTAMPTZ DEFAULT NOW(),
    CONSTRAINT fk_project
        FOREIGN KEY(project_id)
        REFERENCES migration_schema.projects(project_id)
        ON DELETE CASCADE
);

-- NEW TABLE: Stores every message within a conversation.
CREATE TABLE IF NOT EXISTS migration_schema.messages (
    message_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    conversation_id UUID NOT NULL REFERENCES migration_schema.conversations(conversation_id) ON DELETE CASCADE,
    message_type migration_schema.message_type NOT NULL,
    content TEXT, -- The text from the user or AI. Can be null for capsules.
    ui_metadata JSONB, -- UI state specific to this point in the conversation.
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create project_status_v3 table with improved design
CREATE TABLE IF NOT EXISTS migration_schema.project_status_v3 (
    status_id UUID PRIMARY KEY,
    project_id UUID REFERENCES migration_schema.projects(project_id) ON DELETE CASCADE,
    progress TEXT NOT NULL CONSTRAINT project_status_progress_check CHECK (
        progress IN (
            'OVERVIEW',
            'FILE_QUEUE',
            'SEED_PROJECT_INITIALIZED',
            'LAYOUT_ANALYZED',
            'DESIGN_SYSTEM_MAPPED',
            'COMPONENTS_CREATED',
            'PAGES_GENERATED',
            'BUILD_STARTED',
            'BUILD_SUCCEEDED',
            'BUILD_FAILED',
            'COMPLETED',
            'FAILED',
            'CODE_GENERATION'
        )
    ),
    status TEXT NOT NULL CONSTRAINT project_status_status_check CHECK (
        status IN (
            'PENDING',
            'IN_PROGRESS',
            'COMPLETED',
            'FAILED'
        )
    ),
    log TEXT,
    progress_description TEXT,
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create project_status_history table to track all status changes
CREATE TABLE IF NOT EXISTS migration_schema.project_status_history (
    history_id SERIAL PRIMARY KEY,
    status_id UUID REFERENCES migration_schema.project_status_v3(status_id) ON DELETE CASCADE,
    project_id UUID REFERENCES migration_schema.projects(project_id) ON DELETE CASCADE,
    progress TEXT NOT NULL,
    status TEXT NOT NULL,
    log TEXT,
    progress_description TEXT,
    metadata JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes for faster lookups
CREATE INDEX idx_project_status_v3_project_id ON migration_schema.project_status_v3(project_id);
CREATE INDEX idx_project_status_v3_updated_at ON migration_schema.project_status_v3(updated_at);
CREATE INDEX idx_project_status_history_project_id ON migration_schema.project_status_history(project_id);
CREATE INDEX idx_project_status_history_created_at ON migration_schema.project_status_history(created_at);

-- Add an index to quickly fetch messages for a conversation in the correct order.
CREATE INDEX idx_messages_on_conversation_and_time ON migration_schema.messages (conversation_id, created_at ASC);
