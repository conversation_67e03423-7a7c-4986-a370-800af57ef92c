import os

# Define the expected structure
EXPECTED_STRUCTURE = {
"exceptions": ["__init__.py"],
"services": ["__init__.py"],
"migrations": ["__init__.py"],
"models": ["__init__.py"],
"routers": ["__init__.py"],
"schemas": ["__init__.py"],
"security": ["__init__.py"],
"settings": ["__init__.py"],
"utils": ["__init__.py"],
}

def check_structure(base_path):
    for folder, files in EXPECTED_STRUCTURE.items():
        folder_path = os.path.join(base_path, folder)
        if not os.path.exists(folder_path):
            print(f"Missing folder: {folder}")
            continue
        for file in files:
            file_path = os.path.join(folder_path, file)
            if not os.path.exists(file_path):
                print(f"Missing file: {file} in folder: {folder}")

if __name__ == "__main__":
    base_path = "src"
    check_structure(base_path)