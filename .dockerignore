# .dockerignore

# Ignore Python bytecode
__pycache__
*.pyc
*.pyo
*.pyd

# Ignore virtual environments
env/
venv/
ENV/
env.bak/
venv.bak/

# Ignore distribution / packaging
.Python
*.egg-info
dist/
build/
*.egg

# Ignore installer logs
pip-log.txt
pip-delete-this-directory.txt

# Ignore unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Ignore Jupyter Notebook checkpoints
.ipynb_checkpoints

# Ignore pyenv
.python-version

# Ignore mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Ignore Pyre type checker
.pyre/

# Ignore Ruff cache
.ruff_cache/

# Ignore VS Code settings
.vscode/

# Ignore pre-commit configuration
.pre-commit-config.yaml

# Ignore Docker override files
docker-compose.override.yml

# Ignore pre-commit hook scripts
scripts/

.env

test