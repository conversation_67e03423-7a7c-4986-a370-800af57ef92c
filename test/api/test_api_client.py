from http.client import <PERSON>TT<PERSON><PERSON>x<PERSON>
import pytest
from unittest.mock import AsyncMock, patch
from httpx import HTTPStatusError, Response, TimeoutException
from tenacity import RetryError
from src.utils.api_helper.api_client import APIClient

@pytest.mark.asyncio
async def test_get_retries_on_500_error():
    base_url = "https://reqres.in/"
    headers = {}
    api_client = APIClient(base_url=base_url, headers=headers)

    mock_response = AsyncMock()
    mock_response.raise_for_status.side_effect = HTTPStatusError(
        message="Internal Server Error",
        request=None,
        response=Response(status_code=500)
    )

    with patch("httpx.AsyncClient.get", new_callable=AsyncMock) as mock_get:
        mock_get.side_effect = mock_response.raise_for_status.side_effect
        with patch("tenacity.nap.sleep", return_value=None):  # Correctly patching tenacity's sleep method
            with pytest.raises(RetryError):
                await api_client.get("/api/users?page=2")

                

    assert mock_response.raise_for_status.call_count == 3  # Ensure it retried 3 times

@pytest.mark.asyncio
async def test_get_retries_on_timeout():
    base_url = "https://reqres.in/"
    headers = {}
    api_client = APIClient(base_url=base_url, headers=headers)

    # Mock the response to simulate a timeout error
    mock_response = AsyncMock()
    mock_response.raise_for_status.side_effect = TimeoutException("Timeout occurred")

    with patch("httpx.AsyncClient.get", new_callable=AsyncMock) as mock_get:
        mock_get.side_effect = mock_response.raise_for_status.side_effect
        with patch("tenacity.nap.sleep", return_value=None):  # Correctly patching tenacity's sleep method
            with pytest.raises(HTTPException) as exc_info:
                await api_client.get("/api/users?page=2")
            assert exc_info.value.status_code == 504

    assert mock_response.raise_for_status.call_count == 3  # Ensure it retried 3 times

@pytest.mark.asyncio
async def test_post_retries_on_500_error():
    base_url = "https://reqres.in/"
    headers = {}
    api_client = APIClient(base_url=base_url, headers=headers)

    # Mock the response to simulate a 500 error
    mock_response = AsyncMock()
    mock_response.raise_for_status.side_effect = HTTPStatusError(
        message="Internal Server Error",
        request=None,
        response=Response(status_code=500)
    )

    with patch("httpx.AsyncClient.post", new_callable=AsyncMock) as mock_post:
        mock_post.side_effect = mock_response.raise_for_status.side_effect
        with patch("tenacity.nap.sleep", return_value=None):  # Correctly patching tenacity's sleep method
            with pytest.raises(RetryError):
                await api_client.post("/api/users", data={"name": "morpheus", "job": "leader"})

    assert mock_response.raise_for_status.call_count == 3  # Ensure it retried 3 times

@pytest.mark.asyncio
async def test_post_retries_on_timeout():
    base_url = "https://reqres.in/"
    headers = {}
    api_client = APIClient(base_url=base_url, headers=headers)

   # Mock the response to simulate a timeout error
    mock_response = AsyncMock()
    mock_response.raise_for_status.side_effect = mock_response.raise_for_status.side_effect = [TimeoutException("Timeout occurred")] * 3  # Raise the exception 3 times to simulate retries

    with patch("httpx.AsyncClient.post", new_callable=AsyncMock) as mock_post:
        mock_post.side_effect = mock_response.raise_for_status.side_effect
        with patch("tenacity.nap.sleep", return_value=None):  # Correctly patching tenacity's sleep method
            with pytest.raises(HTTPException) as exc_info:
                await api_client.post("/api/users", data={"name": "morpheus", "job": "leader"})
            assert exc_info.value.status_code == 504

    assert mock_response.raise_for_status.call_count == 3  # Ensure it retried 3 times

