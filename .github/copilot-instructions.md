# Copilot Instructions  

## Project Overview  
This project utilizes the following technologies:  
- **UV**: For project management and virtual environment setup.  
- **FastAPI**: For building APIs.  
- **Locust**: For API load testing.  
- **PostgreSQL**: As the database backend.  
- **Redis**: For server-sent events and potentially for pub/sub API requests (feature yet to be implemented).  

## Instructions for Copilot  
1. **Code Generation**:  
    - Generate FastAPI routes and endpoints for API functionality.  
    - Ensure proper integration with PostgreSQL for database operations.  
    - Use Redis for server-sent events and prepare for future pub/sub implementation.  

2. **Testing**:  
    - Write Locust scripts for API load testing.  
    - Include scenarios for high-concurrency testing.  

3. **Database Operations**:  
    - Generate queries and models for PostgreSQL using asyncpg or SQLAlchemy.  
    - Ensure proper indexing and schema design for performance optimization.  

4. **Redis Integration**:  
    - Implement server-sent events using Redis.  
    - Prepare a modular design for pub/sub functionality to be added later.  

5. **Project Management**:  
    - Use UV for virtual environment setup and dependency management.  
    - Ensure all dependencies are frozen and documented in `pyproject.toml`.  

6. **Documentation**:  
    - Provide clear comments and docstrings for all generated code.  
    - Include examples for API usage and testing in the README.  

7. **Best Practices**:  
    - Follow SOLID principles and modular design patterns.  
    - Ensure code is scalable and maintainable.  

8. **Testing and Deployment**:  
    - Write unit tests for all modules.  
    - Ensure compatibility with CI/CD pipelines for deployment.  
