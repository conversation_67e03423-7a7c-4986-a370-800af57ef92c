.idea/
*.pyc
__pycache__/
.venv# .gitignore
.env

# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
*.egg-info/
dist/
build/
*.egg

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints
testing.ipynb
logger_files/

# pyenv
.python-version

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# Ruff
.ruff_cache/

# VS Code
.vscode/


# Docker
docker-compose.override.yml

# logs
logs/
log_data/*

# local
/local
/prompts


.cursor

# Ignore .DS_Store files
.DS_Store

# Ignore Python cache directories
__pycache__/

# Ignore ruff cache
.ruff_cache/

# Ignore pytest cache
.pytest_cache/
