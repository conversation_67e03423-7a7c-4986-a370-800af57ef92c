
repos:
  # Standard pre-commit hooks
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.6.0 # Or latest stable version
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-toml
      - id: check-json
      - id: check-added-large-files
      - id: check-merge-conflict
      - id: debug-statements # Catches `breakpoint()`, `pdb.set_trace()`
      - id: detect-private-key

  # - repo: https://github.com/PyCQA/bandit
  #   rev: 1.7.9 # Or your desired/latest version
  #   hooks:
  #     - id: bandit
  #       args: [
  #           "-r",         # Recursive mode
  #           "src/",       # Scan the 'src' directory
  #           # "tests/",   # Optionally scan the 'tests' directory if relevant
  #           "-s", "B101", # Example: skip the 'assert_used' check (B101)
  #           # Add other bandit arguments as needed
  #         ]

  #       files: \.py$

  # - repo: https://github.com/RobertCraigie/pyright-python
  #   rev: v1.1.402 # Check for latest pyright-python tag
  #   hooks:
  #     - id: pyright

  - repo: https://github.com/gitleaks/gitleaks
    rev: v8.18.4 # Check for latest gitleaks version
    hooks:
      - id: gitleaks

  # Ruff for linting and formatting (replaces flake8, isort, pydocstyle, etc.)
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.5.0 # Check for the latest ruff-pre-commit tag
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix] # Auto-fix issues and exit if fixes were made
      - id: ruff-format # If you want to use ruff's formatter (alternative/replacement for Black)
        # args: [--check] # To only check, remove for auto-formatting

  # local custom hooks
  - repo: local
    hooks:
      - id: check-structure
        name: Check Directory Structure
        entry: python scripts/check_structure.py # Ensure this script is executable and in this path
        language: python
        files: ^src/  # Assuming your script checks files under src/
        pass_filenames: false # Or true, depending on if your script expects filenames as args
      - id: pip-audit
        name: Pip Audit Dependencies
        entry: pip-audit
        language: system
        files: ^(pyproject\.toml|uv\.lock|requirements\.lock)$ # Adjust uv.lock/requirements.lock if your lock file is named differently
        pass_filenames: false
        stages: [pre-commit]
